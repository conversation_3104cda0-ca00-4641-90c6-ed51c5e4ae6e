import os
import torch
import torch.utils.data as data
import torchvision.utils as vutils
import torch.nn.functional as F
from torch import nn
import numpy as np
from fid.src.pytorch_fid.fid_score import calculate_fid_given_paths
from skimage.metrics import peak_signal_noise_ratio
from skimage.metrics import structural_similarity
from attgan.data import check_attribute_conflict
import shutil
from tqdm import tqdm
import argparse
import get_output
from model_data_prepare import prepare
import gc
import json
from datetime import datetime

# 设置 GPU 设备
os.environ['CUDA_VISIBLE_DEVICES'] = '7'

def setup_directories(model_names, batch_idx=None):
    """为每个模型创建必要的目录"""
    base_dirs = {}
    
    # 创建原图保存目录
    if batch_idx is not None:
        original_dir = f'original_images_batch_{batch_idx}'
    else:
        original_dir = 'original_images'
    
    if os.path.exists(original_dir):
        shutil.rmtree(original_dir)
    os.makedirs(original_dir, exist_ok=True)
    
    # 为原图创建子目录
    max_pairs = 500
    for i in range(max_pairs):
        pair_original_dir = os.path.join(original_dir, str(i))
        os.makedirs(pair_original_dir, exist_ok=True)
    
    base_dirs['original'] = original_dir
    
    for model in model_names:
        # 创建模型特定的目录
        if batch_idx is not None:
            gen_dir = os.path.join(model, f'gen_batch_{batch_idx}')
            gen_noattack_dir = os.path.join(model, f'gen_noattack_batch_{batch_idx}')
        else:
            gen_dir = os.path.join(model, 'gen')
            gen_noattack_dir = os.path.join(model, 'gen_noattack')
        
        # 如果目录已存在，先删除
        if os.path.exists(gen_dir):
            shutil.rmtree(gen_dir)
        if os.path.exists(gen_noattack_dir):
            shutil.rmtree(gen_noattack_dir)
            
        # 创建新目录
        os.makedirs(gen_dir, exist_ok=True)
        os.makedirs(gen_noattack_dir, exist_ok=True)
        
        # 为每对图像创建单独的目录
        for i in range(max_pairs):
            pair_gen_dir = os.path.join(gen_dir, str(i))
            pair_noattack_dir = os.path.join(gen_noattack_dir, str(i))
            os.makedirs(pair_gen_dir, exist_ok=True)
            os.makedirs(pair_noattack_dir, exist_ok=True)
        
        base_dirs[model] = {
            'gen': gen_dir,
            'gen_noattack': gen_noattack_dir
        }
    
    return base_dirs

def save_batch_results(results, batch_idx, output_dir='fid_results'):
    """保存每批次的FID结果"""
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f'fid_results_batch_{batch_idx}_{timestamp}.txt')
    
    with open(output_file, 'w') as f:
        f.write(f"批次 {batch_idx} FID评估结果:\n")
        f.write("=" * 50 + "\n")
        for model_name, fid_score in results.items():
            f.write(f"{model_name} FID Score: {fid_score:.4f}\n")
        f.write("=" * 50 + "\n")
    
    # 同时保存为JSON格式，方便后续处理
    json_file = os.path.join(output_dir, f'fid_results_batch_{batch_idx}_{timestamp}.json')
    with open(json_file, 'w') as f:
        json.dump(results, f, indent=4)

def save_final_results(all_results, output_dir='fid_results'):
    """保存最终的平均FID结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f'fid_results_final_{timestamp}.txt')
    
    # 计算每个模型的平均FID
    avg_results = {}
    num_batches = len(all_results)
    
    for model in all_results[0].keys():
        total_fid = sum(batch[model] for batch in all_results if model in batch)
        avg_results[model] = total_fid / num_batches
    
    with open(output_file, 'w') as f:
        f.write("最终FID评估结果:\n")
        f.write("=" * 50 + "\n")
        f.write(f"总批次数: {num_batches}\n")
        f.write("每个批次500张图像\n\n")
        for model_name, avg_fid in avg_results.items():
            f.write(f"{model_name} 平均FID Score: {avg_fid:.4f}\n")
        f.write("\n各批次详细结果:\n")
        for i, batch_result in enumerate(all_results):
            f.write(f"\n批次 {i+1}:\n")
            for model_name, fid_score in batch_result.items():
                f.write(f"{model_name}: {fid_score:.4f}\n")
        f.write("=" * 50 + "\n")

def calculate_fid_for_model(gen_dir, gen_noattack_dir, device='cuda', dims=2048, batch_size=1, max_pairs=500):
    """计算单个模型的FID分数"""
    try:
        total_fid = 0.0
        valid_pairs = 0
        
        # 遍历每一对图像
        for i in tqdm(range(max_pairs), desc="计算FID"):
            pair_gen_dir = os.path.join(gen_dir, str(i))
            pair_noattack_dir = os.path.join(gen_noattack_dir, str(i))
            
            # 检查是否存在图像
            gen_images = len([f for f in os.listdir(pair_gen_dir) if f.endswith('.jpg')])
            noattack_images = len([f for f in os.listdir(pair_noattack_dir) if f.endswith('.jpg')])
            
            if gen_images == 0 or noattack_images == 0:
                continue
                
            try:
                # 计算这一对的FID
                fid_value = calculate_fid_given_paths(
                    paths=[pair_gen_dir, pair_noattack_dir],
                    batch_size=1,  # 强制使用batch_size=1
                    device=device,
                    dims=dims
                )
                
                if fid_value is not None:
                    total_fid += fid_value
                    valid_pairs += 1
                    print(f"第 {i+1} 对图像的FID: {fid_value:.4f}")
                
            except Exception as e:
                print(f"计算第 {i+1} 对图像的FID时出错: {str(e)}")
                continue
            
            # 清理GPU内存
            torch.cuda.empty_cache()
            gc.collect()
        
        if valid_pairs > 0:
            avg_fid = total_fid / valid_pairs
            print(f"平均FID (over {valid_pairs} pairs): {avg_fid:.4f}")
            return avg_fid
        else:
            print("没有有效的图像对用于计算FID")
            return None
            
    except Exception as e:
        print(f"计算FID时出错: {str(e)}")
        return None
    
    
def evaluate_models_fid(args):
    """评估所有模型的FID分数"""
    all_results = []  # 存储所有批次的结果
    sub_batch_size = 500  # 每500张图像计算一次FID
    num_sub_batches = (args.max_samples + sub_batch_size - 1) // sub_batch_size  # 向上取整
    
    # 准备模型和相关组件
    print("正在加载模型...")
    all_models = prepare()
    attack_dataloader, test_dataloader, attgan, attgan_args, solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = all_models
    print("模型加载完成")

    # 创建配置类
    class GlobalSettings:
        def __init__(self):
            self.device = args.device
            self.gpu = True if args.device == 'cuda' else False
            self.data_path = args.data_path
            self.num_test = None
            self.batch_size = args.batch_size
            
    class StarGANConfig:
        def __init__(self):
            self.c_dim = 5
            self.g_conv_dim = 64
            self.d_conv_dim = 64
            self.g_repeat_num = 6
            self.d_repeat_num = 6
            
    class AttGANConfig:
        def __init__(self):
            self.n_attrs = 13
            self.b_size = args.batch_size
            self.g_conv_dim = 64
            self.d_conv_dim = 64
            self.g_repeat_num = 6
            self.d_repeat_num = 6
            
    class AttentionGANConfig:
        def __init__(self):
            self.input_nc = 3
            self.output_nc = 3
    
    # 创建args_attack对象
    class ArgsAttack:
        def __init__(self):
            self.global_settings = GlobalSettings()
            self.attacks = type('Attacks', (), {'epsilon': 0.05})()
            self.stargan = StarGANConfig()
            self.AttGAN = AttGANConfig()
            self.AttentionGAN = AttentionGANConfig()
    
    args_attack = ArgsAttack()
    
    # 创建攻击工具
    class AttackUtils:
        def __init__(self, watermark, epsilon=0.05):
            self.up = watermark.cuda() if args.device == 'cuda' else watermark
            self.epsilon = epsilon
            self.device = args.device
            
        def adjust_watermark_batch(self, batch_size):
            """调整水印的batch size"""
            if self.up.size(0) != batch_size:
                self.up = self.up[0:1].repeat(batch_size, 1, 1, 1)
    
    # 加载水印
    if args.watermark_path:
        watermark = torch.load(args.watermark_path)
        watermark = watermark.to(args.device)
        attack_utils = AttackUtils(watermark)
    
    # 设置目录
    model_names = ['HiSD', 'AttGAN', 'StarGAN', 'AttentionGAN']
    
    for sub_batch_idx in range(num_sub_batches):
        print(f"\n处理子批次 {sub_batch_idx + 1}/{num_sub_batches}")
        start_idx = sub_batch_idx * sub_batch_size
        end_idx = min((sub_batch_idx + 1) * sub_batch_size, args.max_samples)
        
        # 为每个子批次创建新的目录
        base_dirs = setup_directories(model_names, sub_batch_idx)
        
        # 计算当前子批次的FID
        sub_batch_results = {}
        current_sub_batch_size = end_idx - start_idx
        
        print(f"\n开始生成第 {start_idx + 1} 到 {end_idx} 张图像...")
        
        try:
            for idx, (img_a, att_a, c_org) in enumerate(tqdm(test_dataloader, desc="生成图像")):
                if idx < start_idx:
                    continue
                if idx >= end_idx:
                    break
                    
                local_idx = idx - start_idx
                
                img_a = img_a.cuda() if args.device == 'cuda' else img_a
                att_a = att_a.cuda() if args.device == 'cuda' else att_a
                att_a = att_a.type(torch.float)
                
                # 保存裁剪后的原图
                vutils.save_image(img_a, os.path.join(base_dirs['original'], str(local_idx), 'original.jpg'),
                                nrow=1, normalize=True, value_range=(-1., 1.))
                
                # 如果有水印，也保存添加水印后的原图
                if args.watermark_path:
                    current_watermark = attack_utils.up
                    current_watermark = torch.clamp(current_watermark, -attack_utils.epsilon, attack_utils.epsilon)
                    img_with_watermark = img_a + current_watermark
                    vutils.save_image(img_with_watermark, os.path.join(base_dirs['original'], str(local_idx), 'original_with_watermark.jpg'),
                                    nrow=1, normalize=True, value_range=(-1., 1.))
                
                # 处理HiSD模型
                with torch.no_grad():
                    try:
                        # 生成无攻击图像
                        c = E(img_a)
                        c_trg = c
                        s_trg = F(reference, 1)
                        c_trg = T(c_trg, s_trg, 1)
                        gen_noattack = G(c_trg)
                        
                        # 生成加水印图像
                        if args.watermark_path:
                            current_watermark = attack_utils.up
                            current_watermark = torch.clamp(current_watermark, -attack_utils.epsilon, attack_utils.epsilon)
                            c = E(img_a + current_watermark)
                            c_trg = c
                            s_trg = F(reference, 1)
                            c_trg = T(c_trg, s_trg, 1)
                            gen = G(c_trg)
                            
                            vutils.save_image(gen, os.path.join(base_dirs['HiSD']['gen'], str(local_idx), 'gen.jpg'),
                                            nrow=1, normalize=True, value_range=(-1., 1.))
                        
                        vutils.save_image(gen_noattack, os.path.join(base_dirs['HiSD']['gen_noattack'], str(local_idx), 'gen_noattack.jpg'),
                                        nrow=1, normalize=True, value_range=(-1., 1.))
                    except Exception as e:
                        print(f"处理HiSD模型时出错: {str(e)}")
                
                # 处理AttGAN模型
                try:
                    att_b_list = [att_a]
                    for i in range(attgan_args.n_attrs):
                        tmp = att_a.clone()
                        tmp[:, i] = 1 - tmp[:, i]
                        tmp = check_attribute_conflict(tmp, attgan_args.attrs[i], attgan_args.attrs)
                        att_b_list.append(tmp)
                    
                    with torch.no_grad():
                        # 生成无攻击图像
                        gen_noattack, _ = attgan.G(img_a, att_b_list[0])
                        
                        # 生成加水印图像
                        if args.watermark_path:
                            current_watermark = attack_utils.up
                            current_watermark = torch.clamp(current_watermark, -attack_utils.epsilon, attack_utils.epsilon)
                            gen, _ = attgan.G(img_a + current_watermark, att_b_list[0])
                            
                            vutils.save_image(gen, os.path.join(base_dirs['AttGAN']['gen'], str(local_idx), 'gen.jpg'),
                                            nrow=1, normalize=True, value_range=(-1., 1.))
                        
                        vutils.save_image(gen_noattack, os.path.join(base_dirs['AttGAN']['gen_noattack'], str(local_idx), 'gen_noattack.jpg'),
                                        nrow=1, normalize=True, value_range=(-1., 1.))
                except Exception as e:
                    print(f"处理AttGAN模型时出错: {str(e)}")
                
                # 处理StarGAN模型
                try:
                    x_noattack_list, x_fake_list = solver.test_universal_model_level(idx, img_a, c_org, 
                                                                                attack_utils.up if args.watermark_path else None,
                                                                                args_attack.stargan)
                    
                    if args.watermark_path:
                        vutils.save_image(x_fake_list[0], os.path.join(base_dirs['StarGAN']['gen'], str(local_idx), 'gen.jpg'),
                                        nrow=1, normalize=True, value_range=(-1., 1.))
                    vutils.save_image(x_noattack_list[0], os.path.join(base_dirs['StarGAN']['gen_noattack'], str(local_idx), 'gen_noattack.jpg'),
                                 nrow=1, normalize=True, value_range=(-1., 1.))
                except Exception as e:
                    print(f"处理StarGAN模型时出错: {str(e)}")
                
                # 处理AttentionGAN模型
                try:
                    x_noattack_list, x_fake_list = attentiongan_solver.test_universal_model_level(idx, img_a, c_org,
                                                                                             attack_utils.up if args.watermark_path else None,
                                                                                             args_attack.AttentionGAN)
                    
                    if args.watermark_path:
                        vutils.save_image(x_fake_list[0], os.path.join(base_dirs['AttentionGAN']['gen'], str(local_idx), 'gen.jpg'),
                                        nrow=1, normalize=True, value_range=(-1., 1.))
                    vutils.save_image(x_noattack_list[0], os.path.join(base_dirs['AttentionGAN']['gen_noattack'], str(local_idx), 'gen_noattack.jpg'),
                                 nrow=1, normalize=True, value_range=(-1., 1.))
                except Exception as e:
                    print(f"处理AttentionGAN模型时出错: {str(e)}")
                
                # 定期清理GPU内存
                if local_idx % 10 == 0:
                    torch.cuda.empty_cache()
                    gc.collect()
        
        except Exception as e:
            print(f"生成图像时出现错误: {str(e)}")
        
        print(f"\n子批次 {sub_batch_idx + 1} 图像生成完成，开始计算FID分数...")
        
        # 计算每个模型的FID分数
        for model_name in model_names:
            print(f"\n计算 {model_name} 的FID分数...")
            
            try:
                fid_score = calculate_fid_for_model(
                    base_dirs[model_name]['gen'],
                    base_dirs[model_name]['gen_noattack'],
                    device=args.device,
                    dims=args.dims,
                    batch_size=1,
                    max_pairs=current_sub_batch_size
                )
                
                if fid_score is not None:
                    sub_batch_results[model_name] = fid_score
                    print(f"{model_name} 子批次 {sub_batch_idx + 1} FID分数: {sub_batch_results[model_name]:.4f}")
                else:
                    print(f"警告：无法计算 {model_name} 的FID分数")
                    
                # 清理GPU内存
                torch.cuda.empty_cache()
                gc.collect()
                
            except Exception as e:
                print(f"计算 {model_name} 的FID分数时出错: {str(e)}")
                continue
        
        # 保存当前子批次的结果
        save_batch_results(sub_batch_results, sub_batch_idx, args.output_dir)
        all_results.append(sub_batch_results)
        
        print(f"\n子批次 {sub_batch_idx + 1} 完成")
        print("当前子批次FID结果:")
        for model_name, score in sub_batch_results.items():
            print(f"{model_name}: {score:.4f}")
        
        # 清理当前子批次的目录和内存
        for model in model_names:
            shutil.rmtree(os.path.join(model, f'gen_batch_{sub_batch_idx}'), ignore_errors=True)
            shutil.rmtree(os.path.join(model, f'gen_noattack_batch_{sub_batch_idx}'), ignore_errors=True)
        # 保留原图目录，不删除
        # shutil.rmtree(f'original_images_batch_{sub_batch_idx}', ignore_errors=True)
        
        # 彻底清理内存
        torch.cuda.empty_cache()
        gc.collect()
    
    # 保存最终结果
    save_final_results(all_results, args.output_dir)

def parse_args():
    parser = argparse.ArgumentParser(description='计算模型FID分数')
    parser.add_argument('--data_path', type=str, required=True,
                        help='数据集路径')
    parser.add_argument('--watermark_path', type=str, default=None,
                        help='水印文件路径')
    parser.add_argument('--batch_size', type=int, default=1,
                        help='批次大小')
    parser.add_argument('--max_samples', type=int, default=1000,
                        help='最大样本数')
    parser.add_argument('--num_workers', type=int, default=4,
                        help='数据加载的工作进程数')
    parser.add_argument('--device', type=str, default='cuda',
                        help='使用的设备')
    parser.add_argument('--dims', type=int, default=2048,
                        help='FID特征维度')
    parser.add_argument('--output_dir', type=str, default='/home/<USER>/zhenghongrui/FOUND-main/FOUND_code/fid_results3',
                        help='结果输出目录')
    
    args = parser.parse_args()
    return args

if __name__ == '__main__':
    args = parse_args()
    evaluate_models_fid(args)

# 使用示例：
# 不使用水印：
# python calculate_model_fid.py --data_path /home/<USER>/shujuji/data/CelebA/img_align_celeba --max_samples 10000 --batch_size 1

# 使用水印：
# python calculate_model_fid.py --data_path /home/<USER>/shujuji/data/CelebA/img_align_celeba --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND3--0.90.pt --max_samples 10000 --batch_size 1
# python calculate_model_fid.py --data_path /home/<USER>/shujuji/data/LFW/LFW  --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND3--0.90.pt --max_samples 1000 --batch_size 1
# python calculate_model_fid.py --data_path /home/<USER>/shujuji/FF++/c23/val/real  --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND3--0.90.pt --max_samples 10000 --batch_size 1