# Copyright (C) 2018 <PERSON> <<EMAIL>>
#
# This work is licensed under the MIT License. To view a copy of this license,
# visit https://opensource.org/licenses/MIT.

"""Main entry point for fine-tuning AttGAN network."""

import argparse
import datetime
import json
import os
from os.path import join

import torch.utils.data as data

import torch
import torchvision.utils as vutils
from attgan import AttGAN
from data import check_attribute_conflict # Ensure this can handle your new data attributes or modify as needed
from helpers import Progressbar, add_scalar_dict
from tensorboardX import SummaryWriter


attrs_default = [
    'Bald', 'Bangs', '<PERSON>_Hair', 'Blond_Hair', '<PERSON>_Hair', '<PERSON>y_Eyebrows',
    'Eyeglasses', 'Male', 'Mouth_Slightly_Open', 'Mustache', 'No_Beard', 'Pale_Skin', 'Young'
] # TODO: Modify this list based on the attributes of your new dataset

def parse(args=None):
    parser = argparse.ArgumentParser()

    # Arguments for attributes and data
    parser.add_argument('--attrs', dest='attrs', default=attrs_default, nargs='+', help='attributes to learn/fine-tune')
    # TODO: Update choices if you add a new dataset loader for your new data
    parser.add_argument('--data', dest='data', type=str, choices=['CelebA', 'CelebA-HQ', 'NewDataset'], default='CelebA', help='Choose the dataset to use. Add NewDataset if you implement a custom one.')
    # TODO: IMPORTANT - Update these paths for your new dataset
    parser.add_argument('--data_path', dest='data_path', type=str, default='data/new_dataset_path/images', help='Path to your new dataset image directory')
    parser.add_argument('--attr_path', dest='attr_path', type=str, default='data/new_dataset_path/attributes.txt', help='Path to your new dataset attribute file')
    parser.add_argument('--image_list_path', dest='image_list_path', type=str, default='data/new_dataset_path/image_list.txt', help='Path to your new dataset image list file (if applicable)')

    # Model configuration - usually kept same as pre-training unless specifically intended
    parser.add_argument('--img_size', dest='img_size', type=int, default=128)
    parser.add_argument('--shortcut_layers', dest='shortcut_layers', type=int, default=1)
    parser.add_argument('--inject_layers', dest='inject_layers', type=int, default=0)
    parser.add_argument('--enc_dim', dest='enc_dim', type=int, default=64)
    parser.add_argument('--dec_dim', dest='dec_dim', type=int, default=64)
    parser.add_argument('--dis_dim', dest='dis_dim', type=int, default=64)
    parser.add_argument('--dis_fc_dim', dest='dis_fc_dim', type=int, default=1024)
    parser.add_argument('--enc_layers', dest='enc_layers', type=int, default=5)
    parser.add_argument('--dec_layers', dest='dec_layers', type=int, default=5)
    parser.add_argument('--dis_layers', dest='dis_layers', type=int, default=5)
    parser.add_argument('--enc_norm', dest='enc_norm', type=str, default='batchnorm')
    parser.add_argument('--dec_norm', dest='dec_norm', type=str, default='batchnorm')
    parser.add_argument('--dis_norm', dest='dis_norm', type=str, default='instancenorm')
    parser.add_argument('--dis_fc_norm', dest='dis_fc_norm', type=str, default='none')
    parser.add_argument('--enc_acti', dest='enc_acti', type=str, default='lrelu')
    parser.add_argument('--dec_acti', dest='dec_acti', type=str, default='relu')
    parser.add_argument('--dis_acti', dest='dis_acti', type=str, default='lrelu')
    parser.add_argument('--dis_fc_acti', dest='dis_fc_acti', type=str, default='relu')
    
    # Loss weights
    parser.add_argument('--lambda_1', dest='lambda_1', type=float, default=100.0) # Reconstruction loss
    parser.add_argument('--lambda_2', dest='lambda_2', type=float, default=10.0)  # Adversarial loss for G
    parser.add_argument('--lambda_3', dest='lambda_3', type=float, default=1.0)   # Attribute classification loss for G
    parser.add_argument('--lambda_gp', dest='lambda_gp', type=float, default=10.0) # Gradient penalty

    # Training settings
    parser.add_argument('--mode', dest='mode', default='wgan', choices=['wgan', 'lsgan', 'dcgan'])
    # TODO: Adjust epochs for fine-tuning, likely fewer than training from scratch
    parser.add_argument('--epochs', dest='epochs', type=int, default=100, help='# of epochs for fine-tuning')
    parser.add_argument('--batch_size', dest='batch_size', type=int, default=32)
    parser.add_argument('--num_workers', dest='num_workers', type=int, default=0)
    # TODO: Adjust learning rate for fine-tuning, usually smaller
    parser.add_argument('--lr', dest='lr', type=float, default=0.00005, help='learning rate for fine-tuning')
    parser.add_argument('--beta1', dest='beta1', type=float, default=0.5)
    parser.add_argument('--beta2', dest='beta2', type=float, default=0.999)
    parser.add_argument('--n_d', dest='n_d', type=int, default=5, help='# of d updates per g update')

    # Interpolation and sampling settings
    parser.add_argument('--b_distribution', dest='b_distribution', default='none', choices=['none', 'uniform', 'truncated_normal'])
    parser.add_argument('--thres_int', dest='thres_int', type=float, default=0.5)
    parser.add_argument('--test_int', dest='test_int', type=float, default=1.0)
    parser.add_argument('--n_samples', dest='n_samples', type=int, default=16, help='# of sample images for validation')

    # Path and saving settings
    parser.add_argument('--load_checkpoint_path', dest='load_checkpoint_path', type=str, required=True, help='Path to the pre-trained generator weights (.pth file) to load for fine-tuning')
    parser.add_argument('--save_interval', dest='save_interval', type=int, default=500) # Adjust save interval if needed
    parser.add_argument('--sample_interval', dest='sample_interval', type=int, default=500) # Adjust sample interval
    parser.add_argument('--gpu', dest='gpu', action='store_true', help='Use GPU for training')
    parser.add_argument('--multi_gpu', dest='multi_gpu', action='store_true', help='Use multiple GPUs (if available and configured)')
    parser.add_argument('--experiment_name', dest='experiment_name', default=datetime.datetime.now().strftime("finetune_%I%M%p_on_%B_%d_%Y"))

    return parser.parse_args(args)

args = parse()
print("Fine-tuning arguments:")
print(json.dumps(vars(args), indent=4))

args.lr_base = args.lr # For fine-tuning, lr_base is the new lr
args.n_attrs = len(args.attrs)
args.betas = (args.beta1, args.beta2)

# Create output directories
output_dir = join('output', args.experiment_name)
checkpoint_dir = join(output_dir, 'checkpoint')
sample_dir = join(output_dir, 'sample_finetuning')
os.makedirs(output_dir, exist_ok=True)
os.makedirs(checkpoint_dir, exist_ok=True)
os.makedirs(sample_dir, exist_ok=True)

with open(join(output_dir, 'finetune_setting.txt'), 'w') as f:
    f.write(json.dumps(vars(args), indent=4, separators=(',', ':')))

# Prepare new dataset
# TODO: IMPORTANT - If your new dataset ('NewDataset') has a different structure or name,
# you MUST implement a new PyTorch Dataset class (e.g., in data.py) 
# and update the choices for '--data' argument and the loading logic below.
# from data import NewDataset # Example: if you create NewDataset in data.py
if args.data == 'CelebA':
    from data import CelebA
    train_dataset = CelebA(args.data_path, args.attr_path, args.img_size, 'train', args.attrs)
    valid_dataset = CelebA(args.data_path, args.attr_path, args.img_size, 'valid', args.attrs)
elif args.data == 'CelebA-HQ':
    from data import CelebA_HQ
    train_dataset = CelebA_HQ(args.data_path, args.attr_path, args.image_list_path, args.img_size, 'train', args.attrs)
    valid_dataset = CelebA_HQ(args.data_path, args.attr_path, args.image_list_path, args.img_size, 'valid', args.attrs)
elif args.data == 'NewDataset': # Update this string if your new dataset name is different
    # This is a placeholder. You need to ensure 'NewDataset' class is defined and imported.
    # from data import NewDataset # Make sure this import works
    # train_dataset = NewDataset(args.data_path, args.attr_path, args.img_size, 'train', args.attrs)
    # valid_dataset = NewDataset(args.data_path, args.attr_path, args.img_size, 'valid', args.attrs)
    print(f"Attempting to use 'NewDataset'. Ensure it is correctly implemented and imported from data.py")
    # Fallback or error if NewDataset is not properly set up
    raise NotImplementedError(f"Dataset loader for 'NewDataset' is not fully implemented/verified in this script. Please check data.py and script logic.")
else:
    raise NotImplementedError(f"Dataset loader for {args.data} is not implemented. Please add it or choose from 'CelebA', 'CelebA-HQ', or 'NewDataset' (after implementation). ")

train_dataloader = data.DataLoader(
    train_dataset, batch_size=args.batch_size, num_workers=args.num_workers,
    shuffle=True, drop_last=True
)
valid_dataloader = data.DataLoader(
    valid_dataset, batch_size=args.n_samples, num_workers=args.num_workers,
    shuffle=False, drop_last=False
)
print('Fine-tuning with new dataset. Training images:', len(train_dataset), '/', 'Validating images:', len(valid_dataset))

# Initialize AttGAN model
attgan = AttGAN(args) # Assumes AttGAN constructor can handle all args

# Load pre-trained generator weights
if args.load_checkpoint_path:
    if os.path.exists(args.load_checkpoint_path):
        # The original train.py uses saveG, which saves a dict {'G': G.state_dict()}
        # So we need to load this dict and then load the G's state_dict into attgan.G
        checkpoint = torch.load(args.load_checkpoint_path, map_location=lambda storage, loc: storage)
        if 'G' in checkpoint:
            # If model was saved with DataParallel, keys might have "module." prefix
            # Create a new state_dict and remove "module." prefix if it exists
            new_state_dict = {}
            is_data_parallel = False
            for k, v in checkpoint['G'].items():
                if k.startswith('module.'):
                    is_data_parallel = True
                    break
            
            if is_data_parallel:
                for k, v in checkpoint['G'].items():
                    name = k[7:] # remove `module.`
                    new_state_dict[name] = v
                attgan.G.load_state_dict(new_state_dict)
            else:
                attgan.G.load_state_dict(checkpoint['G'])
            
            print(f"Successfully loaded pre-trained generator weights from: {args.load_checkpoint_path}")
            
            # If you also saved optimizer states for G with saveG (which is not typical for saveG)
            # and want to load them, you would do something like:
            # if 'optim_G' in checkpoint:
            #     attgan.optim_G.load_state_dict(checkpoint['optim_G'])
            #     print(f"Successfully loaded pre-trained generator optimizer state from: {args.load_checkpoint_path}")
        else:
            print(f"Error: Checkpoint file {args.load_checkpoint_path} does not contain 'G' key. Trying to load the entire state_dict directly into G.")
            try:
                # Fallback: if the checkpoint is just the state_dict of G
                attgan.G.load_state_dict(checkpoint)
                print(f"Successfully loaded pre-trained generator (raw state_dict) from: {args.load_checkpoint_path}")
            except Exception as e:
                print(f"Error loading raw state_dict into G: {e}")
                print("Please ensure the checkpoint is compatible.")

    else:
        print(f"Error: Pre-trained checkpoint not found at {args.load_checkpoint_path}.")
        print("The script will continue, but the model will NOT be initialized with pre-trained weights.")
        # Optionally, you could exit here if pre-trained weights are mandatory for your use case:
        # exit(f"Exiting: Pre-trained checkpoint {args.load_checkpoint_path} not found.")
else:
    # This case should ideally not be hit if load_checkpoint_path is required=True in argparse
    print("Warning: No pre-trained checkpoint path provided, though it is marked as required. Model will use initial weights.")


progressbar = Progressbar() # Make sure Progressbar is defined/imported correctly
writer = SummaryWriter(join(output_dir, 'summary')) # Ensure SummaryWriter is imported

# Prepare fixed images and attributes for validation/sampling
# It's crucial that valid_dataloader yields data compatible with your model and attributes
fixed_img_a, fixed_att_a = next(iter(valid_dataloader))
fixed_img_a = fixed_img_a.cuda() if args.gpu else fixed_img_a
fixed_att_a = fixed_att_a.cuda() if args.gpu else fixed_att_a
fixed_att_a = fixed_att_a.type(torch.float)

sample_att_b_list = [fixed_att_a.clone()] # Start with original attributes
for i in range(args.n_attrs):
    tmp = fixed_att_a.clone()
    tmp[:, i] = 1 - tmp[:, i] # Flip the i-th attribute
    # Ensure check_attribute_conflict is appropriate for your new attributes and their interactions
    tmp = check_attribute_conflict(tmp, args.attrs[i], args.attrs)
    sample_att_b_list.append(tmp)

it = 0 # Global iteration counter
it_per_epoch = len(train_dataset) // args.batch_size

print(f"Starting fine-tuning for {args.epochs} epochs...")
for epoch in range(args.epochs):
    # Learning rate scheduling for fine-tuning.
    # You might want a smaller, constant LR or a different decay schedule for fine-tuning.
    # The example below halves LR halfway through epochs. Adjust as needed.
    # lr = args.lr_base # Constant LR example
    if args.epochs >=2 : # Basic decay, avoid division by zero if epochs=1
        lr = args.lr_base / (10 ** (epoch // (args.epochs // 2)))
    else:
        lr = args.lr_base
        
    if hasattr(attgan, 'set_lr'):
        attgan.set_lr(lr) 
    else:
        # If AttGAN does not have set_lr, you might need to update optimizer LR directly
        # This depends on how optimizers are managed within AttGAN class
        print("Warning: AttGAN class does not have set_lr method. Learning rate might not be updated.")
        # Example: if attgan.optim_G exists: attgan.optim_G.param_groups[0]['lr'] = lr
        #          if attgan.optim_D exists: attgan.optim_D.param_groups[0]['lr'] = lr

    writer.add_scalar('LR/learning_rate', lr, epoch) # Log LR per epoch

    for img_a, att_a in progressbar(train_dataloader):
        if not hasattr(attgan, 'trainD') or not hasattr(attgan, 'trainG'):
            raise AttributeError("AttGAN class is missing trainD or trainG methods.")

        attgan.train() # Set model to training mode

        img_a = img_a.cuda() if args.gpu else img_a
        att_a = att_a.cuda() if args.gpu else att_a
        
        idx = torch.randperm(len(att_a)) # Shuffle for target attributes
        att_b = att_a[idx].contiguous()
        
        att_a = att_a.type(torch.float)
        att_b = att_b.type(torch.float)
        
        att_a_ = (att_a * 2 - 1) * args.thres_int
        if args.b_distribution == 'none':
            att_b_ = (att_b * 2 - 1) * args.thres_int
        elif args.b_distribution == 'uniform':
            att_b_ = (att_b * 2 - 1) * torch.rand_like(att_b) * (2 * args.thres_int)
        elif args.b_distribution == 'truncated_normal':
            att_b_ = (att_b * 2 - 1) * (torch.fmod(torch.randn_like(att_b), 2) + 2) / 4.0 * (2 * args.thres_int)
        
        errD_summary = {} # To store D losses for logging
        if (it + 1) % (args.n_d + 1) != 0:
            errD = attgan.trainD(img_a, att_a, att_a_, att_b, att_b_)
            add_scalar_dict(writer, errD, it + 1, 'D')
            errD_summary = errD # Save for progressbar
        else:
            errG = attgan.trainG(img_a, att_a, att_a_, att_b, att_b_)
            add_scalar_dict(writer, errG, it + 1, 'G')
            # Ensure errD_summary has d_loss from previous D step for progressbar
            d_loss_val = errD_summary.get('d_loss', 0) # Default to 0 if D hasn't run yet in this G step cycle
            g_loss_val = errG.get('g_loss', 0)
            progressbar.say(epoch=epoch, iter=(it%it_per_epoch)+1, total_iter=it_per_epoch, d_loss=d_loss_val, g_loss=g_loss_val)

        if (it + 1) % args.save_interval == 0:
            save_path = os.path.join(checkpoint_dir, f'weights_finetuned_epoch{epoch}_iter{it+1}.pth')
            attgan.saveG(save_path) # Save generator
            # attgan.save(save_path) # Or save full model state
            print(f"Saved fine-tuned G weights to {save_path}")

        if (it + 1) % args.sample_interval == 0:
            attgan.eval() # Evaluation mode for sampling
            with torch.no_grad():
                samples = [fixed_img_a.clone()]
                for i, s_att_b in enumerate(sample_att_b_list):
                    s_att_b_for_G = s_att_b.clone() # Ensure original s_att_b is not modified
                    s_att_b_ = (s_att_b_for_G * 2 - 1) * args.thres_int
                    if i > 0: 
                        attr_idx_to_modify = i - 1 
                        if attr_idx_to_modify < s_att_b_.shape[1]: # Check bounds
                             s_att_b_[..., attr_idx_to_modify] = s_att_b_[..., attr_idx_to_modify] * args.test_int / args.thres_int
                        else:
                            print(f"Warning: Attribute index {attr_idx_to_modify} out of bounds for test_int modification.")
                    
                    # Call Generator in 'enc-dec' mode and take the first element (the image)
                    generated_output, _ = attgan.G(fixed_img_a, s_att_b_, mode='enc-dec')
                    samples.append(generated_output)
                samples = torch.cat(samples, dim=3)
                writer.add_image('sample_finetuned', vutils.make_grid(samples, nrow=1, normalize=True), it + 1)
                vutils.save_image(samples, os.path.join(
                        sample_dir,
                        f'Finetuned_Epoch_({epoch})_Iter_({(it%it_per_epoch)+1}of{it_per_epoch}).jpg'
                    ), nrow=1, normalize=True)
        it += 1

print("Fine-tuning completed.")
print(f"Fine-tuned models and samples saved in directory: {output_dir}") 


# python finetune.py \
#         --load_checkpoint_path /root/tf-logs/FOUND_code/attgan/output/256_shortcut1_inject0_none/checkpoint/weights.199.pth \
#         --data_path /root/autodl-tmp/img_align_celeba \
#         --attr_path /root/tf-logs/list_attr_celeba.txt \
#         --attrs Bald Bangs Black_Hair Blond_Hair Brown_Hair Bushy_Eyebrows Eyeglasses Male Mouth_Slightly_Open Mustache No_Beard Pale_Skin Young \
#         --experiment_name 256_shortcut1_inject0_none_finetune \
#         --gpu 