import cv2
import numpy as np
import os
from face_detection import build_detector
from tqdm import tqdm

def detect_faces(image_path):
    """检测单个图像中的人脸
    Args:
        image_path: 图像文件路径
    Returns:
        tuple: (处理后的图像, 检测结果)
    """
    # 检查图片路径是否存在
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Failed to load image: {image_path}")
    
    # 初始化MobileNet检测器
    detector = build_detector(
        "RetinaNetMobileNetV1",
        confidence_threshold=0.5,
        nms_iou_threshold=0.3,
        max_resolution=1080
    )
    
    # 执行检测 (需要将BGR转换为RGB)
    dets = detector.detect(image[:, :, ::-1])
    
    # 在图像上绘制检测结果
    for i, det in enumerate(dets):
        # 获取边界框和置信度
        box = det[:4].astype(int)
        score = det[4]
        
        # 绘制边界框
        cv2.rectangle(image, 
                     (box[0], box[1]), 
                     (box[2], box[3]),
                     (255, 0, 0), 2)
        
        # # 绘制置信度分数
        # cv2.putText(image, 
        #             f"Score: {score:.2f}", 
        #             (box[0], box[1] - 10),
        #             cv2.FONT_HERSHEY_SIMPLEX, 
        #             0.5, 
        #             (0, 255, 0), 
        #             2)
    
    return image, dets

def process_directory(input_dir, output_dir):
    """处理整个目录中的图像
    Args:
        input_dir: 输入图像目录
        output_dir: 输出图像目录
    Returns:
        tuple: (总人脸数, 处理的图像数, 平均人脸数)
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有图像文件
    image_files = [f for f in os.listdir(input_dir) 
                  if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    total_faces = 0
    processed_images = 0
    
    print(f"\n开始处理目录: {input_dir}")
    print(f"共发现 {len(image_files)} 个图像文件")
    
    # 使用tqdm显示进度条
    for img_file in tqdm(image_files, desc="处理进度"):
        try:
            input_path = os.path.join(input_dir, img_file)
            output_path = os.path.join(output_dir, f"detected_{img_file}")
            
            # 处理单个图像
            result_image, detections = detect_faces(input_path)
            
            # 保存结果图像
            cv2.imwrite(output_path, result_image)
            
            # 统计人脸数量
            num_faces = len(detections)
            total_faces += num_faces
            processed_images += 1
            
        except Exception as e:
            print(f"\n处理图像 {img_file} 时出错: {str(e)}")
            continue
    
    # 计算平均人脸数量
    avg_faces = total_faces / processed_images if processed_images > 0 else 0
    
    return total_faces, processed_images, avg_faces

if __name__ == "__main__":
    # 设置输入输出路径
    input_dir = "/root/tf-logs/FOUND_code/DSFD_pytorch/img1"  # 输入图像文件夹
    output_dir = "/root/tf-logs/FOUND_code/DSFD/retinaface_image"  # 输出图像文件夹
    
    try:
        # 处理整个目录
        total_faces, processed_images, avg_faces = process_directory(input_dir, output_dir)
        
        # 打印统计结果
        print("\n处理完成！统计结果：")
        print(f"处理的图像总数: {processed_images}")
        print(f"检测到的人脸总数: {total_faces}")
        print(f"平均每张图像的人脸数量: {avg_faces:.2f}")
        
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
