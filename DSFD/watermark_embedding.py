import argparse
import torch
import os
from os.path import join
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import cv2
from tqdm import tqdm
import shutil

class ImageDataset:
    """图像数据集加载器"""
    def __init__(self, data_path, output_path, image_size=256, num_samples=None):
        self.data_path = data_path
        self.output_path = output_path
        self.image_paths = []
        
        # 确保输出目录存在
        os.makedirs(output_path, exist_ok=True)
        
        # 支持的图像格式
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        for root, _, files in os.walk(data_path):
            for file in files:
                if os.path.splitext(file)[1].lower() in valid_extensions:
                    self.image_paths.append(os.path.join(root, file))
        
        # 如果指定了样本数量，随机选择并复制到image1
        if num_samples and num_samples < len(self.image_paths):
            selected_indices = np.random.choice(len(self.image_paths), num_samples, replace=False)
            selected_paths = [self.image_paths[i] for i in selected_indices]
            self.image_paths = selected_paths
            
            # 复制选中的图像到image1文件夹
            print(f"\n正在将选中的{num_samples}张图像复制到: {output_path}")
            for src_path in tqdm(selected_paths, desc="复制图像"):
                dst_path = os.path.join(output_path, os.path.basename(src_path))
                shutil.copy2(src_path, dst_path)
        
        self.transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),
        ])
        
        self.length = len(self.image_paths)
        
    def __getitem__(self, index):
        return self.transform(Image.open(self.image_paths[index]).convert('RGB'))
        
    def __len__(self):
        return self.length

def save_image(tensor, path):
    """保存图像张量为文件"""
    # 转换为numpy数组并调整范围到0-255
    image_np = (tensor.permute(1, 2, 0).numpy() * 0.5 + 0.5) * 255
    image_np = image_np.astype(np.uint8)
    # BGR转RGB
    image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
    cv2.imwrite(path, image_np)

def embed_watermark(image_dir, watermark_path, output_dir, batch_size=8, num_samples=None):
    """嵌入水印到图像中
    
    Args:
        image_dir (str): 输入图像目录
        watermark_path (str): 水印文件路径
        output_dir (str): 输出目录
        batch_size (int): 批处理大小
        num_samples (int, optional): 处理的图像数量，None表示处理所有图像
    """
    image1_dir = "/root/tf-logs/FOUND_code/output88/22"  # 选中图像的保存目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载水印
    try:
        watermark = torch.load(watermark_path)
        print(f"已加载水印，形状: {watermark.shape}")
    except Exception as e:
        print(f"加载水印时出错: {e}")
        return
    
    # 准备数据集，同时复制选中的图像到image1
    dataset = ImageDataset(image_dir, image1_dir, num_samples=num_samples)
    
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # 使用GPU如果可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    watermark = watermark.to(device)
    
    print(f"\n开始处理图像...")
    for batch_idx, images in enumerate(tqdm(dataloader)):
        images = images.to(device)
        
        # 添加水印
        watermarked_images = images + watermark
        watermarked_images = torch.clamp(watermarked_images, -1, 1)
        
        # 保存每张图像
        for i, watermarked_image in enumerate(watermarked_images):
            global_idx = batch_idx * batch_size + i
            save_image(
                watermarked_image.cpu(),
                join(output_dir, f'CMUA-watermarked_{global_idx:05d}.png')
            )
            
            # 同时保存原始图像以供对比
            save_image(
                images[i].cpu(),
                join(output_dir, f'original_{global_idx:05d}.png')
            )

def main():
    parser = argparse.ArgumentParser(description='将水印嵌入到图像中')
    parser.add_argument('--image_dir', type=str, 
                        default='/root/tf-logs/FOUND_code/output88/11',  # 修改为image1文件夹
                        required=True,
                        help='输入图像目录的路径')
    parser.add_argument('--watermark_path', type=str, 
                        default='/root/tf-logs/FOUND_code/perturbation_CMUA.pt',
                        required=True,
                        help='水印文件的路径')
    parser.add_argument('--output_dir', type=str, 
                        default='/root/tf-logs/FOUND_code/output88/66',  # 水印后的图像保存在image2
                        help='输出目录的路径')
    parser.add_argument('--batch_size', type=int, 
                        default=1,
                        help='批处理大小')
    parser.add_argument('--num_samples', type=int, 
                        default='8',  # 可以设置具体数值来限制处理的图像数量
                        help='要处理的图片数量，默认处理所有图片')
    
    args = parser.parse_args()
    
    # 检查输入路径
    if not os.path.exists(args.image_dir):
        print(f"错误: 输入目录不存在: {args.image_dir}")
        return
    
    if not os.path.exists(args.watermark_path):
        print(f"错误: 水印文件不存在: {args.watermark_path}")
        return
    
    # 嵌入水印
    embed_watermark(
        args.image_dir,
        args.watermark_path,
        args.output_dir,
        args.batch_size,
        args.num_samples
    )
    
    print(f'所有处理后的图像已保存到 {args.output_dir} 目录')

if __name__ == '__main__':
    main()
