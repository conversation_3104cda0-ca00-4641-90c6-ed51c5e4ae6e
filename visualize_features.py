import torch
import os
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from torchvision.utils import save_image
import matplotlib.pyplot as plt
from model_data_prepare import prepare
from train_FOUND import select_model_to_get_feature_pairs, init_get_outputs, parse,DI
os.environ['CUDA_VISIBLE_DEVICES'] = '7'

class FeatureVisualizer:
    def __init__(self):
        # 初始化模型和必要组件
        self.args_attack = parse()
        self.attack_utils = init_get_outputs(self.args_attack)
        _, _, self.attgan, self.attgan_args, self.stargan_solver, \
        self.attentiongan_solver, self.transform, self.F, self.T, \
        self.G, self.E, self.reference, self.gen_models = prepare()
        
        # 创建输出目录
        self.output_dir = "feature_visualization"
        self.model_names = ["AttGAN", "StarGAN", "AGGAN", "HiSD"]
        os.makedirs(self.output_dir, exist_ok=True)
        for model_name in self.model_names:
            os.makedirs(os.path.join(self.output_dir, model_name), exist_ok=True)

    def preprocess_image(self, image_path):
        """预处理输入图像"""
        image = Image.open(image_path).convert('RGB')
        transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        img = transform(image).unsqueeze(0)
        return img.cuda() if torch.cuda.is_available() else img

    def add_watermark(self, image):
        """添加水印到图像"""
        # 加载预训练的水印
        try:
            watermark = torch.load('pert_FOUND-011.pt')
            watermarked_image = image + watermark
            return torch.clamp(watermarked_image, -1, 1)
        except:
            print("未找到水印文件，使用原始图像")
            return image

    def normalize_feature_map(self, feature_map):
        """归一化特征图以便可视化"""
        feature_map = feature_map.mean(dim=1)  # 对通道维度取平均
        min_val = feature_map.min()
        max_val = feature_map.max()
        normalized = (feature_map - min_val) / (max_val - min_val + 1e-8)
        return normalized

    def visualize_features(self, image_path):
        """可视化特征图，只保留最具代表性的热力图"""
        # 预处理图像
        img = self.preprocess_image(image_path)
        watermarked_img = self.add_watermark(img)
        
        # 准备模型输入
        c_org = torch.zeros(1, 5).cuda()  # 属性向量
        att_a = torch.zeros(1, 13).cuda()  # AttGAN属性
        
        # 获取每个模型的特征
        for case, model_name in enumerate(self.model_names):
            print(f"正在处理 {model_name} 的特征图")
            processed_img = DI(watermarked_img) if case < 3 else watermarked_img
            _, middle_pair = select_model_to_get_feature_pairs(
                case=case,
                img=processed_img,
                ori_imgs=img,
                reference=self.reference,
                attribute_c=c_org if case < 2 else att_a,
                attribute_attgan=att_a,
                attack=self.attack_utils,
                stargan_s=self.stargan_solver,
                atggan_s=self.attentiongan_solver,
                attgan_s=self.attgan,
                attgan_args=self.attgan_args,
                EE=self.E,
                FF=self.F,
                TT=self.T,
                GG=self.G,
                g_models=self.gen_models,
                reconstruct=256
            )
            
            # 处理特征图
            feature_map = middle_pair[1]  # 使用处理后的特征
            normalized_feature = self.normalize_feature_map(feature_map)
            
            # 创建增强的热力图可视化
            plt.figure(figsize=(12, 8))
            
            # 主热力图
            plt.subplot(1, 2, 1)
            heatmap = plt.imshow(normalized_feature[0].cpu().detach(), 
                               cmap='inferno',  # 使用inferno配色方案，更好地显示特征强度
                               interpolation='gaussian')  # 使用高斯插值使图像更平滑
            plt.colorbar(heatmap, label='Feature Intensity')
            plt.title(f"{model_name} Feature Map")
            plt.axis('off')
            
            # 添加特征强度分布直方图
            plt.subplot(1, 2, 2)
            feature_data = normalized_feature[0].cpu().detach().numpy().flatten()
            plt.hist(feature_data, bins=50, color='skyblue', alpha=0.7)
            plt.title('Feature Intensity Distribution')
            plt.xlabel('Intensity')
            plt.ylabel('Frequency')
            
            # 调整布局并保存
            plt.tight_layout()
            base_filename = os.path.splitext(os.path.basename(image_path))[0]
            output_path = os.path.join(
                self.output_dir, 
                model_name, 
                f"{base_filename}_feature_map.png"
            )
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"已保存 {model_name} 的特征图到: {output_path}")

    def process_directory(self, input_dir):
        """处理整个目录的图像"""
        for filename in os.listdir(input_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_path = os.path.join(input_dir, filename)
                print(f"\n处理图像: {filename}")
                self.visualize_features(image_path)

def main():
    # 设置输入图像目录
    input_dir = "/home/<USER>/zhenghongrui/FOUND-main/FOUND_code/myimages"  # 请将此改为你的输入图像目录
    
    # 创建可视化器并处理图像
    visualizer = FeatureVisualizer()
    visualizer.process_directory(input_dir)

if __name__ == "__main__":
    main()