import os
import glob

def verify_dataset(root_dir, mode='train'):
    # 确定标签文件路径
    if mode == 'train':
        label_file = os.path.join(root_dir, 'train/label.txt')
    else:
        label_file = os.path.join(root_dir, 'val/wider_val.txt')
    
    # 读取标签文件中的图像路径
    label_images = set()
    with open(label_file, 'r') as f:
        lines = f.readlines()
        for line in lines:
            if '.jpg' in line:
                img_path = line.strip()
                label_images.add(img_path)
    
    # 获取实际图像文件
    image_dir = os.path.join(root_dir, f'{mode}/images')
    actual_images = set()
    for root, _, files in os.walk(image_dir):
        for file in files:
            if file.endswith('.jpg'):
                # 获取相对路径
                rel_path = os.path.relpath(os.path.join(root, file), image_dir)
                actual_images.add(rel_path)
    
    # 检查匹配情况
    print(f"\n检查{mode}集...")
    print(f"标签文件中的图像数量: {len(label_images)}")
    print(f"实际图像文件数量: {len(actual_images)}")
    
    # 检查缺失的图像
    missing_images = label_images - actual_images
    if missing_images:
        print(f"\n标签文件中存在但实际缺失的图像:")
        for img in list(missing_images)[:5]:
            print(f"- {img}")
        if len(missing_images) > 5:
            print(f"... 等 {len(missing_images)} 个文件")
    
    # 检查多余的图像
    extra_images = actual_images - label_images
    if extra_images:
        print(f"\n实际存在但标签文件中没有的图像:")
        for img in list(extra_images)[:5]:
            print(f"- {img}")
        if len(extra_images) > 5:
            print(f"... 等 {len(extra_images)} 个文件")
    
    return len(missing_images) == 0 and len(extra_images) == 0

if __name__ == '__main__':
    dataset_root = '/root/autodl-tmp/widerface'
    
    # 检查训练集
    train_ok = verify_dataset(dataset_root, 'train')
    # 检查验证集
    val_ok = verify_dataset(dataset_root, 'val')
    
    if train_ok and val_ok:
        print("\n✅ 数据集验证通过！图像和标签完全匹配。")
    else:
        print("\n❌ 数据集验证失败！请检查上述问题。")