import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import models
import torchvision.transforms as transforms
from torchvision.models import vgg19
from PIL import Image
import numpy as np
from skimage.metrics import structural_similarity as ssim
import os
import random
from PIL import ImageDraw
from torchvision.models.detection import FasterRCNN_ResNet50_FPN_Weights, SSD300_VGG16_Weights
os.environ['CUDA_VISIBLE_DEVICES'] = '6'

#这个值最开始的两个检测器实现中毒检测，能达到(Average DUQ): -62.42%     (Average SSIM): 0.9269
#但是 DUQ 的检测方法与文字并不一致

# 设置随机种子
def set_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

set_seed(42)

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 感知损失类
class PerceptualLoss(nn.Module):
    def __init__(self):
        super(PerceptualLoss, self).__init__()
        # vgg = vgg16(pretrained=True).features[:16].eval().to(device)
        vgg = vgg19(pretrained=True).features[:20].eval().to(device)
        for param in vgg.parameters():
            param.requires_grad = False
        self.vgg = vgg

    def forward(self, x, y):
        x_features = self.vgg(x)
        y_features = self.vgg(y)
        loss = nn.functional.mse_loss(x_features, y_features)
        return loss

# 黑盒对抗性扰动生成器类，使用 PGD 和感知损失
class BlackBoxAdversarialPerturbationGenerator:
    def __init__(self, models, epsilon=0.15, max_iterations=100, step_size=0.005):
        self.models = models
        self.epsilon = epsilon
        self.max_iterations = max_iterations
        self.step_size = step_size
        self.perceptual_loss_fn = PerceptualLoss()
        self.perceptual_weight = 0.05

    def generate_perturbation(self, image_path):
        # 加载并预处理图像
        image = Image.open(image_path).convert('RGB')
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        img_tensor = transform(image).unsqueeze(0).to(device)

        # 初始化扰动为全零
        perturbation = torch.zeros_like(img_tensor, requires_grad=True).to(device)
        
        # 使用 Adam 优化器
        optimizer = optim.Adam([perturbation], lr=self.step_size)

        # 添加学习率调度器
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=50, gamma=0.5)

        previous_loss = float('inf')
        stagnation_count = 0
        max_stagnation = 10
        stagnation_threshold = 1e-4

        # 使用 PGD 攻击生成对抗扰动
        for iteration in range(self.max_iterations):
            optimizer.zero_grad()

            # 将扰动应用到图像
            perturbed_image = torch.clamp(img_tensor + perturbation, 0, 1)

            # 累计损失（目标模型损失 + 感知损失）
            total_loss = 0
            for model in self.models:
                detections = model(perturbed_image)
                total_loss += self.calculate_loss(detections)

            # 增加感知损失以保持扰动的隐蔽性
            total_loss += self.perceptual_weight * self.perceptual_loss_fn(perturbed_image, img_tensor)

            # 加入熵损失，增加对抗样本的输出不确定性
            entropy_loss = self.calculate_entropy(perturbed_image)
            total_loss += 0.02 * entropy_loss

            # 反向传播和更新
            total_loss.backward()
            optimizer.step()
            scheduler.step()

            # 使用非 in-place 操作更新扰动，确保扰动范围被约束在 epsilon 内
            with torch.no_grad():
                perturbation.data = torch.clamp(perturbation, -self.epsilon, self.epsilon)

            # 打印当前损失
            print(f"Iteration {iteration + 1}/{self.max_iterations}, Loss: {total_loss.item()}")

            # 检查损失停滞
            if abs(previous_loss - total_loss.item()) < stagnation_threshold:
                stagnation_count += 1
            else:
                stagnation_count = 0

            if stagnation_count >= max_stagnation:
                print("Loss stagnation detected, adjusting parameters.")
                self.perceptual_weight *= 0.9  # 动态调整感知损失权重
                self.epsilon *= 1.05  # 动态增加 epsilon
                stagnation_count = 0

            previous_loss = total_loss.item()

        # 返回生成的对抗性扰动
        return perturbation

    def calculate_loss(self, detections):
        # 目标是降低人脸检测的置信度，并增加误检的概率
        loss = 0
        for detection in detections:
            if 'scores' in detection:
                for score in detection['scores']:
                    if score > 0.5:
                        # 对高置信度人脸检测进行适度的惩罚
                        loss += torch.log(1 - score + 1e-6) * 15
                    else:
                        # 对低置信度进行奖励
                        loss -= torch.log(1 - score + 1e-6) * 10
        return -loss  # 最大化误检

    def calculate_entropy(self, image):
        # 计算输出的熵，以增加对抗扰动的有效性
        image_flat = image.view(image.size(0), -1)
        prob = torch.softmax(image_flat, dim=1)
        entropy = -torch.sum(prob * torch.log(prob + 1e-6), dim=1).mean()
        return entropy

    def evaluate_metrics(self, original_image, perturbed_image, original_detections, perturbed_detections):
        # 计算数据效用质量（DUQ）和结构相似性（SSIM）
        original_true_positives = 0
        perturbed_true_positives = 0

        for detection in original_detections:
            if 'scores' in detection:
                for score in detection['scores']:
                    if score > 0.5:
                        original_true_positives += 1

        for detection in perturbed_detections:
            if 'scores' in detection:
                for score in detection['scores']:
                    if score > 0.5:
                        perturbed_true_positives += 1

        # 计算数据效用质量（DUQ）
        duq = (original_true_positives - perturbed_true_positives) / original_true_positives if original_true_positives > 0 else 0

        try:
            # 转换图像为numpy数组
            original_image_np = np.array(original_image).astype(np.float32) / 255.0
            perturbed_image_np = np.array(perturbed_image).astype(np.float32) / 255.0
            
            # 确保图像尺寸足够大，以满足SSIM计算要求
            min_size = min(original_image_np.shape[:2])
            if min_size < 7:
                print(f"警告: 图像尺寸过小 ({original_image_np.shape[:2]})，调整为适合SSIM计算的尺寸")
                # 使用双三次插值放大图像到至少32x32
                from skimage.transform import resize
                new_size = tuple(max(32, s) for s in original_image_np.shape[:2])
                original_image_np = resize(original_image_np, new_size, order=3, preserve_range=True)
                perturbed_image_np = resize(perturbed_image_np, new_size, order=3, preserve_range=True)
                min_size = min(new_size)
            
            # 设置适当的win_size：确保是奇数且不超过图像最小尺寸
            win_size = 7  # 默认win_size
            if min_size < 9:  # 如果图像仍然很小
                win_size = min_size - (1 if min_size % 2 == 0 else 0)  # 确保是奇数
                win_size = max(3, win_size)  # win_size最小为3
            
            # 计算SSIM，明确指定参数
            ssim_index = ssim(
                original_image_np, 
                perturbed_image_np, 
                win_size=win_size, 
                data_range=1.0, 
                multichannel=True, 
                channel_axis=-1 if original_image_np.ndim > 2 else None
            )
        except Exception as e:
            print(f"计算SSIM时出错: {e}")
            ssim_index = 0.0  # 出错时的默认值

        return duq, ssim_index

# 添加绘制检测框的函数
def draw_detection_boxes(image, detections, output_path, confidence_threshold=0.5):
    """
    在图像上绘制检测框
    :param image: PIL Image对象
    :param detections: 模型检测结果
    :param output_path: 保存带检测框图像的路径
    :param confidence_threshold: 置信度阈值
    """
    draw = ImageDraw.Draw(image)
    
    if 'boxes' in detections[0] and 'scores' in detections[0]:
        boxes = detections[0]['boxes'].cpu().detach().numpy()
        scores = detections[0]['scores'].cpu().detach().numpy()
        
        for box, score in zip(boxes, scores):
            if score > confidence_threshold:
                # 绘制矩形框
                draw.rectangle(
                    [(box[0], box[1]), (box[2], box[3])],
                    outline='red',
                    width=3
                )
                # 绘制置信度分数
                draw.text(
                    (box[0], box[1] - 10),
                    f'Score: {score:.2f}',
                    fill='red'
                )
    
    image.save(output_path)

# 黑盒攻击的示例用法
if __name__ == "__main__":
    # 初始化模型（例如，Faster-RCNN 和 SSD 模型）
    from torchvision.models.detection import FasterRCNN_ResNet50_FPN_Weights, SSD300_VGG16_Weights

    models = [
        
        models.detection.fasterrcnn_resnet50_fpn(weights=FasterRCNN_ResNet50_FPN_Weights.COCO_V1).to(device),
        models.detection.ssd300_vgg16(weights=SSD300_VGG16_Weights.COCO_V1).to(device)
    ]

    # 将模型设置为评估模式
    for model in models:
        model.eval()

    # 初始化黑盒对抗扰动生成器
    black_box_adv_generator = BlackBoxAdversarialPerturbationGenerator(models)

    # 输入图像文件夹和输出文件夹
    input_dir = '/home/<USER>/shujuji/data/CelebA/img_align_celeba'
    output_dir = '/home/<USER>/zhenghongrui/FOUND-main/FOUND_code/output'
    os.makedirs(output_dir, exist_ok=True)

    # 获取输入图像路径
    input_image_paths = [os.path.join(input_dir, img) for img in os.listdir(input_dir) if img.endswith(('.jpg', '.jpeg', '.png'))]

    # 控制图像数量
    num_images_to_process = 100  # 用户可以指定需要处理的图像数量
    input_image_paths = input_image_paths[:num_images_to_process]

    # 保存所有图像的 DUQ 值和 SSIM 值
    duq_values = []
    ssim_values = []

    # 逐一处理每个图像，生成对抗性扰动并评估
    for image_path in input_image_paths:
        try:
            # 生成最优扰动
            best_perturbation = black_box_adv_generator.generate_perturbation(image_path)

            # 评估指标
            original_image = Image.open(image_path).convert('RGB')
            perturbed_image = transforms.ToPILImage()(torch.clamp(transforms.ToTensor()(original_image).to(device) + best_perturbation, 0, 1).squeeze().cpu())

            # 保存生成的添加扰动的图像
            output_image_path = os.path.join(output_dir, f"perturbed_{os.path.basename(image_path)}")
            perturbed_image.save(output_image_path)

            # 计算 DUQ 和 SSIM 值
            original_image_tensor = transforms.ToTensor()(original_image).unsqueeze(0).to(device)
            perturbed_image_tensor = transforms.ToTensor()(perturbed_image).unsqueeze(0).to(device)

            # 对每个模型分别计算 DUQ 和 SSIM 并取平均值
            duq_sum = 0
            ssim_sum = 0
            for model in models:
                original_detections = model(original_image_tensor)
                perturbed_detections = model(perturbed_image_tensor)
                current_duq, current_ssim = black_box_adv_generator.evaluate_metrics(original_image, perturbed_image, original_detections, perturbed_detections)
                duq_sum += current_duq
                ssim_sum += current_ssim

            average_duq_for_image = duq_sum / len(models)
            average_ssim_for_image = ssim_sum / len(models)
            duq_values.append(average_duq_for_image)
            ssim_values.append(average_ssim_for_image)

            # 输出每张图像的 DUQ 和 SSIM 值
            print(f"图像 {os.path.basename(image_path)} 的数据效用质量 (DUQ): {average_duq_for_image * 100:.2f}%, 结构相似性 (SSIM): {average_ssim_for_image:.4f}")

            # 在保存扰动图像后，为每个模型绘制检测框
            for i, model in enumerate(models):
                perturbed_detections = model(perturbed_image_tensor)
                
                # 创建带检测框的图像副本
                detection_image = perturbed_image.copy()
                detection_output_path = os.path.join(
                    output_dir, 
                    f"detected_model{i+1}_{os.path.basename(image_path)}"
                )
                draw_detection_boxes(detection_image, perturbed_detections, detection_output_path)

        except Exception as e:
            print(f"Error processing {image_path}: {e}")

    # 计算平均数据效用质量（DUQ）和平均结构相似性（SSIM）
    if duq_values and ssim_values:
        overall_average_duq = sum(duq_values) / len(duq_values)
        overall_average_ssim = sum(ssim_values) / len(ssim_values)
        print(f"平均数据效用质量 (Average DUQ): {overall_average_duq * 100:.2f}%")
        print(f"平均结构相似性 (Average SSIM): {overall_average_ssim:.4f}")

class PoisonGenerator:
    def __init__(self, epsilon=0.15, max_iterations=100, step_size=0.005):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        # 初始化检测模型
        self.models = [
            models.detection.fasterrcnn_resnet50_fpn(weights=FasterRCNN_ResNet50_FPN_Weights.COCO_V1).to(self.device),
            models.detection.ssd300_vgg16(weights=SSD300_VGG16_Weights.COCO_V1).to(self.device)
        ]
        for model in self.models:
            model.eval()
            
        self.perceptual_loss_fn = PerceptualLoss()
        self.epsilon = epsilon
        self.max_iterations = max_iterations
        self.step_size = step_size
        self.perceptual_weight = 0.05

    def calculate_loss(self, detections):
        """
        计算检测损失
        :param detections: 模型检测结果
        :return: 损失值
        """
        loss = 0
        for detection in detections:
            if 'scores' in detection:
                for score in detection['scores']:
                    if score > 0.5:
                        # 对高置信度人脸检测进行惩罚
                        loss += torch.log(1 - score + 1e-6) * 15
                    else:
                        # 对低置信度进行奖励
                        loss -= torch.log(1 - score + 1e-6) * 10
        return -loss  # 最大化误检

    def calculate_entropy(self, image):
        """
        计算图像熵
        :param image: 输入图像张量
        :return: 熵值
        """
        image_flat = image.view(image.size(0), -1)
        prob = torch.softmax(image_flat, dim=1)
        entropy = -torch.sum(prob * torch.log(prob + 1e-6), dim=1).mean()
        return entropy

    def generate_poison(self, image_tensor):
        """
        生成中毒扰动
        :param image_tensor: 输入图像张量 [B,C,H,W]
        :return: 扰动张量
        """
        perturbation = torch.zeros_like(image_tensor, requires_grad=True).to(self.device)
        optimizer = optim.Adam([perturbation], lr=self.step_size)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=50, gamma=0.5)

        for iteration in range(self.max_iterations):
            optimizer.zero_grad()
            perturbed_image = torch.clamp(image_tensor + perturbation, 0, 1)
            
            total_loss = 0
            for model in self.models:
                with torch.enable_grad():
                    detections = model(perturbed_image) 
                    total_loss += self.calculate_loss(detections)

            # 添加感知损失和熵损失
            total_loss += self.perceptual_weight * self.perceptual_loss_fn(perturbed_image, image_tensor)
            total_loss += 0.02 * self.calculate_entropy(perturbed_image)

            total_loss.backward()
            optimizer.step()
            scheduler.step()

            with torch.no_grad():
                perturbation.data = torch.clamp(perturbation, -self.epsilon, self.epsilon)

            if iteration % 50 == 0:
                print(f"Poison Generation Iteration {iteration}, Loss: {total_loss.item()}")

        return perturbation

    def evaluate_metrics(self, original_image, perturbed_image, original_detections, perturbed_detections):
        # 计算数据效用质量（DUQ）和结构相似性（SSIM）
        original_true_positives = 0
        perturbed_true_positives = 0

        for detection in original_detections:
            if 'scores' in detection:
                for score in detection['scores']:
                    if score > 0.5:
                        original_true_positives += 1

        for detection in perturbed_detections:
            if 'scores' in detection:
                for score in detection['scores']:
                    if score > 0.5:
                        perturbed_true_positives += 1

        # 计算数据效用质量（DUQ）
        duq = (original_true_positives - perturbed_true_positives) / original_true_positives if original_true_positives > 0 else 0

        try:
            # 转换图像为numpy数组
            original_image_np = np.array(original_image).astype(np.float32) / 255.0
            perturbed_image_np = np.array(perturbed_image).astype(np.float32) / 255.0
            
            # 确保图像尺寸足够大，以满足SSIM计算要求
            min_size = min(original_image_np.shape[:2])
            if min_size < 7:
                print(f"警告: 图像尺寸过小 ({original_image_np.shape[:2]})，调整为适合SSIM计算的尺寸")
                # 使用双三次插值放大图像到至少32x32
                from skimage.transform import resize
                new_size = tuple(max(32, s) for s in original_image_np.shape[:2])
                original_image_np = resize(original_image_np, new_size, order=3, preserve_range=True)
                perturbed_image_np = resize(perturbed_image_np, new_size, order=3, preserve_range=True)
                min_size = min(new_size)
            
            # 设置适当的win_size：确保是奇数且不超过图像最小尺寸
            win_size = 7  # 默认win_size
            if min_size < 9:  # 如果图像仍然很小
                win_size = min_size - (1 if min_size % 2 == 0 else 0)  # 确保是奇数
                win_size = max(3, win_size)  # win_size最小为3
            
            # 计算SSIM，明确指定参数
            ssim_index = ssim(
                original_image_np, 
                perturbed_image_np, 
                win_size=win_size, 
                data_range=1.0, 
                multichannel=True, 
                channel_axis=-1 if original_image_np.ndim > 2 else None
            )
        except Exception as e:
            print(f"计算SSIM时出错: {e}")
            ssim_index = 0.0  # 出错时的默认值

        return duq, ssim_index