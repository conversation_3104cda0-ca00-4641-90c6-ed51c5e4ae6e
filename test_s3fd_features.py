import torch
import sys
import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入S3FD检测器和特征提取函数
from S3FDDetector import init_detector, get_features, preprocess_image
from face_poison_integration import extract_s3fd_features_direct_improved, preprocess_s3fd_image

def main():
    print("测试S3FD特征提取...")
    
    # 初始化设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 初始化S3FD检测器
    detector = init_detector(device=device)
    if detector is None:
        print("初始化检测器失败，退出")
        return
    
    # 创建一个随机测试图像 (3通道，224x224)
    test_img = torch.rand(1, 3, 224, 224, device=device) * 2 - 1  # 范围为[-1, 1]
    print(f"测试图像形状: {test_img.shape}")
    
    # 预处理图像
    preprocessed_img = preprocess_s3fd_image(test_img, device)
    print(f"预处理后图像范围: [{preprocessed_img.min().item():.1f}, {preprocessed_img.max().item():.1f}]")
    
    print("\n1. 使用S3FDDetector原生get_features函数")
    try:
        features1, outputs1 = get_features(detector, preprocessed_img)
        if features1:
            print(f"成功提取特征! 特征数量: {len(features1)}")
            for i, feat in enumerate(features1):
                print(f"  特征 {i+1} 形状: {feat.shape}")
        else:
            print("提取的特征列表为空")
    except Exception as e:
        print(f"原生get_features失败: {e}")
    
    print("\n2. 测试改进的extract_s3fd_features_direct_improved函数")
    try:
        features2, outputs2 = extract_s3fd_features_direct_improved(test_img, detector)
        if features2:
            print(f"成功提取特征! 特征数量: {len(features2)}")
            for i, feat in enumerate(features2):
                print(f"  特征 {i+1} 形状: {feat.shape}")
                
            # 检查特征是否有梯度
            has_grad = [feat.requires_grad for feat in features2]
            print(f"  特征是否有梯度: {has_grad}")
        else:
            print("提取的特征列表为空")
    except Exception as e:
        print(f"改进的特征提取函数失败: {e}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main() 