import argparse
import torch
import os
from os.path import join
import torchvision.utils as vutils
from PIL import Image
import torchvision.transforms as transforms
from model_data_prepare import prepare
from attgan.data import check_attribute_conflict
import numpy as np
import random
from pathlib import Path

os.environ['CUDA_VISIBLE_DEVICES'] = '4'

def load_image(image_path):
    """加载并预处理输入图像"""
    transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    image = Image.open(image_path).convert('RGB')
    image = transform(image).unsqueeze(0)
    return image

def load_watermark(watermark_path):
    """加载预训练的水印"""
    watermark = torch.load(watermark_path)
    return watermark

def get_random_images(image_dir, num_images=5):
    """从图像目录中随机选择指定数量的图像"""
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    image_files = [
        f for f in Path(image_dir).iterdir()
        if f.is_file() and f.suffix.lower() in valid_extensions
    ]
    
    if len(image_files) < num_images:
        raise ValueError(f"目录中的图像数量({len(image_files)})少于请求的数量({num_images})")
    
    selected_images = random.sample(image_files, num_images)
    return selected_images

def perform_stargan(image, stargan_solver, c_org):
    """处理StarGAN模型的图像生成"""
    # 创建目标属性向量
    c_trg = torch.zeros_like(c_org)
    c_trg[:, np.random.randint(0, stargan_solver.c_dim)] = 1
    
    # 生成图像
    with torch.no_grad():
        x_fake = stargan_solver.G(image, c_trg)
    return x_fake

def perform_attentiongan(image, attentiongan_solver, c_org):
    """处理AttentionGAN模型的图像生成"""
    # 创建目标属性向量
    c_trg = torch.zeros_like(c_org)
    c_trg[:, np.random.randint(0, attentiongan_solver.c_dim)] = 1
    
    # 生成图像
    with torch.no_grad():
        x_fake = attentiongan_solver.G(image, c_trg)
    return x_fake

def generate_fake_images(image_dir, watermark_path, output_dir, num_images=5):
    """为多张输入图像生成伪造图像并保存"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 随机选择图像
    selected_images = get_random_images(image_dir, num_images)
    
    # 加载水印
    watermark = load_watermark(watermark_path).to(device)
    
    # 准备模型和数据
    _, _, attgan, attgan_args, stargan_solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()
    
    for idx, image_path in enumerate(selected_images):
        print(f"处理图像 {idx+1}/{num_images}: {image_path.name}")
        
        # 为每个图像创建子目录
        image_output_dir = join(output_dir, f"image_{idx+1}")
        os.makedirs(image_output_dir, exist_ok=True)
        
        # 加载图像
        image = load_image(str(image_path)).to(device)
        
        # 生成不同属性的列表
        att_a = torch.zeros(1, attgan_args.n_attrs).to(device)
        att_b_list = []
        for i in range(attgan_args.n_attrs):
            tmp = att_a.clone()
            tmp[:, i] = 1
            tmp = check_attribute_conflict(tmp, attgan_args.attrs[i], attgan_args.attrs)
            att_b_list.append((tmp, attgan_args.attrs[i]))
        
        # 随机选择4个不同的属性
        selected_attrs = np.random.choice(len(att_b_list), 4, replace=False)
        
        # 创建StarGAN和AttentionGAN的原始属性向量
        c_org_stargan = torch.zeros(1, stargan_solver.c_dim).to(device)
        c_org_attention = torch.zeros(1, attentiongan_solver.c_dim).to(device)
        
        with torch.no_grad():
            # 1. 生成不带水印的伪造图像
            fake_images_clean = []
            
            # HiDF
            c = E(image)
            s_trg = F(reference, 1)
            c_trg = T(c, s_trg, 1)
            fake_hidf = G(c_trg)
            fake_images_clean.append(('HiDF', fake_hidf))
            
            # AttGAN
            att_b, attr_name = att_b_list[selected_attrs[0]]
            att_b_ = (att_b * 2 - 1) * attgan_args.thres_int
            fake_attgan, _ = attgan.G(image, att_b_)
            fake_images_clean.append(('AttGAN_' + attr_name, fake_attgan))
            
            # StarGAN
            fake_stargan = perform_stargan(image, stargan_solver, c_org_stargan)
            fake_images_clean.append(('StarGAN', fake_stargan))
            
            # AttentionGAN
            fake_attentiongan = perform_attentiongan(image, attentiongan_solver, c_org_attention)
            fake_images_clean.append(('AttentionGAN', fake_attentiongan))
            
            # 2. 生成带水印的伪造图像
            watermarked_image = image + watermark
            watermarked_image = torch.clamp(watermarked_image, -1, 1)
            fake_images_watermarked = []
            
            # HiDF with watermark
            c = E(watermarked_image)
            s_trg = F(reference, 1)
            c_trg = T(c, s_trg, 1)
            fake_hidf_w = G(c_trg)
            fake_images_watermarked.append(('HiDF_watermarked', fake_hidf_w))
            
            # AttGAN with watermark
            fake_attgan_w, _ = attgan.G(watermarked_image, att_b_)
            fake_images_watermarked.append(('AttGAN_watermarked_' + attr_name, fake_attgan_w))
            
            # StarGAN with watermark
            fake_stargan_w = perform_stargan(watermarked_image, stargan_solver, c_org_stargan)
            fake_images_watermarked.append(('StarGAN_watermarked', fake_stargan_w))
            
            # AttentionGAN with watermark
            fake_attentiongan_w = perform_attentiongan(watermarked_image, attentiongan_solver, c_org_attention)
            fake_images_watermarked.append(('AttentionGAN_watermarked', fake_attentiongan_w))
            
            # 保存所有图像
            # 保存原始图像
            vutils.save_image(image, join(image_output_dir, 'original.png'), normalize=True, value_range=(-1., 1.))
            
            # 保存带水印的图像
            vutils.save_image(watermarked_image, join(image_output_dir, 'watermarked.png'), normalize=True, value_range=(-1., 1.))
            
            # 保存不带水印的伪造图像
            for name, fake_img in fake_images_clean:
                if isinstance(fake_img, tuple):
                    fake_img = fake_img[0]  # 如果是元组，取第一个元素
                vutils.save_image(fake_img, join(image_output_dir, f'fake_{name}.png'), normalize=True, value_range=(-1., 1.))
            
            # 保存带水印的伪造图像
            for name, fake_img in fake_images_watermarked:
                if isinstance(fake_img, tuple):
                    fake_img = fake_img[0]  # 如果是元组，取第一个元素
                vutils.save_image(fake_img, join(image_output_dir, f'fake_{name}.png'), normalize=True, value_range=(-1., 1.))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--image_dir', type=str, required=True, help='输入图像目录的路径')
    parser.add_argument('--watermark_path', type=str, required=True, help='水印文件的路径')
    parser.add_argument('--output_dir', type=str, default='output_fake_images', help='输出目录的路径')
    parser.add_argument('--num_images', type=int, default=5, help='要处理的图像数量')
    args = parser.parse_args()
    
    generate_fake_images(args.image_dir, args.watermark_path, args.output_dir, args.num_images)
    print(f'所有图像已保存到 {args.output_dir} 目录')

if __name__ == '__main__':
    main()



#python generate_fake_images.py --image_dir /home/<USER>/shujuji/data/CelebA/img_align_celeba --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND3--0.90.pt --output_dir /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/output88 --num_images 5
#python generate_fake_images.py --image_dir /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/output88/11  --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/perturbation_CMUA.pt --output_dir /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/output88 --num_images 2