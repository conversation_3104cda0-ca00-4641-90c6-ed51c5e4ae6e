import os
import sys
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.ops import nms
import typing

# 从DSFD导入必要的组件
from DSFD.face_detection import init_dsfd_detector as base_init_dsfd

class DSFDDetector(nn.Module):
    """
    DSFD (Dual Shot Face Detector) 实现
    
    核心特点:
    1. 双射结构 - 使用两路特征流进行检测
    2. 特征增强模块(FEM) - 增强特征表达能力
    3. 特征金字塔网络(FPN) - 多尺度特征融合
    4. 灵活的主干网络 - 默认使用VGG16
    """
    
    def __init__(self,
                 confidence_threshold=0.7,
                 nms_iou_threshold=0.3,
                 device="cuda" if torch.cuda.is_available() else "cpu",
                 model_path=None,
                 fp16_inference=False,
                 clip_boxes=True,
                 attack_mode=False):
        super(DSFDDetector, self).__init__()
        
        self.confidence_threshold = confidence_threshold
        self.nms_iou_threshold = nms_iou_threshold
        self.device = device
        self.fp16_inference = fp16_inference
        self.clip_boxes = clip_boxes
        self.attack_mode = attack_mode
        
        # 预处理参数
        self.mean = np.array([104, 117, 123], dtype=np.float32)
        
        # 加载模型
        self.model = self._load_model(model_path)
        
        # 将模型转移到指定设备
        self.to(device)
        # print(f"DSFD检测器已初始化，处于{'攻击' if attack_mode else '评估'}模式")
    
    def _load_model(self, model_path=None):
        """
        加载DSFD模型
        
        Args:
            model_path: 权重文件路径，如果为None则使用默认路径
            
        Returns:
            model: 加载的模型
        """
        try:
            # 使用DSFD提供的初始化函数，并传递attack_mode参数
            model = base_init_dsfd(device=self.device, weights_path=model_path, attack_mode=self.attack_mode)
            
            # 确保模型配置与attack_mode一致
            if self.attack_mode:
                model.train()  # 训练模式以保留梯度
                for param in model.parameters():
                    param.requires_grad = True
                # print("DSFD在攻击模式下初始化成功")
            else:
                model.eval()  # 评估模式
                
            return model
            
        except Exception as e:
            print(f"加载DSFD模型失败: {e}")
            import traceback
            traceback.print_exc()
            raise e
    
    def get_features(self, x):
        """
        提取特征用于攻击
        
        Args:
            x: 输入图像张量 [B,C,H,W]
            
        Returns:
            dict: 包含所有DSFD关键特征的字典
        """
        if not isinstance(x, torch.Tensor):
            raise TypeError("输入必须是PyTorch张量")
        
        # 确保输入是4D张量
        if x.dim() == 3:
            x = x.unsqueeze(0)
        
        # 预处理
        x = self._preprocess_image(x)
        
        # 存储不同类型的特征
        features_dict = {
            'backbone': [],    # 主干网络特征
            'fem': [],        # 特征增强模块特征
            'fpn': [],        # 特征金字塔特征
            'dual_shot': []   # 双射结构特征
        }
        
        hooks = []
        
        # 定义需要提取的特征层
        layers_to_extract = {
            'backbone': [
                'backbone.conv3_3',
                'backbone.conv4_3',
                'backbone.conv5_3',
                'backbone.fc6',
                'backbone.fc7'
            ],
            'fem': [
                'fem1.output',
                'fem2.output',
                'fem3.output'
            ],
            'fpn': [
                'fpn.lateral1',
                'fpn.lateral2',
                'fpn.lateral3'
            ],
            'dual_shot': [
                'dual_shot.branch1.output',
                'dual_shot.branch2.output'
            ]
        }
        
        # 注册钩子提取特征
        def hook_fn(feature_type, name):
            def hook(module, input, output):
                if isinstance(output, (list, tuple)):
                    features_dict[feature_type].append((name, output[0].detach()))
                else:
                    features_dict[feature_type].append((name, output.detach()))
            return hook
        
        # 为每个类型的特征层注册钩子
        for feature_type, layer_names in layers_to_extract.items():
            for name, module in self.model.named_modules():
                if any(layer in name for layer in layer_names):
                    hooks.append(
                        module.register_forward_hook(
                            hook_fn(feature_type, name)
                        )
                    )
        
        # 前向传播
        self.model(x)
        
        # 移除钩子
        for hook in hooks:
            hook.remove()
        
        # 对特征进行排序和组织
        for feature_type in features_dict:
            features_dict[feature_type].sort(key=lambda x: x[0])  # 按层名称排序
            features_dict[feature_type] = [feat for name, feat in features_dict[feature_type]]
        
        return features_dict
        
    def _preprocess_image(self, x):
        """
        图像预处理
        
        Args:
            x: 输入图像张量 [B,C,H,W]
            
        Returns:
            x: 预处理后的图像张量
        """
        # 确保输入为张量
        if isinstance(x, torch.Tensor):
            # 转换为numpy处理
            x_np = x.detach().cpu().numpy()
            x_np = np.transpose(x_np, (0, 2, 3, 1))  # BCHW -> BHWC
            
            # 从[-1,1]转换到[0,255]
            x_np = (x_np * 0.5 + 0.5) * 255.0  # [-1,1] -> [0,255]
            
            # 减去均值
            x_np = x_np - self.mean
            
            # 转回PyTorch张量
            x_np = np.transpose(x_np, (0, 3, 1, 2))  # BHWC -> BCHW
            x = torch.from_numpy(x_np).to(self.device).float()
            
        else:
            raise TypeError("输入必须是PyTorch张量")
            
        return x
    
    def detect(self, image):
        """
        人脸检测函数
        
        Args:
            image: 输入图像 (可以是numpy数组或PyTorch张量)
            
        Returns:
            dict: 包含边界框、分数和类别的结果
        """
        # 确保在评估模式
        self.eval()
        
        try:
            if isinstance(image, torch.Tensor):
                if image.dim() == 3:
                    image = image.unsqueeze(0)
                
                # 预处理
                x = self._preprocess_image(image)
                
                # 使用torch.no_grad()避免存储中间结果
                with torch.no_grad():
                    # 使用模型进行检测
                    results = self.model(x)
                    
                # 如果结果是list，意味着这是批量图像的检测结果
                if isinstance(results, list):
                    return results
                else:
                    # 可能是单张图像的特征，需要转换为检测结果
                    # 这里默认返回空检测结果，实际使用时需要根据模型输出调整
                    return {
                        'boxes': torch.zeros((0, 4), device=self.device),
                        'scores': torch.zeros(0, device=self.device),
                        'labels': torch.zeros(0, device=self.device, dtype=torch.int64)
                    }
                    
            # 处理numpy图像
            elif isinstance(image, np.ndarray):
                # 转为PyTorch张量
                if len(image.shape) == 3:
                    # 尝试处理BGR图像
                    try:
                        import cv2
                        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    except:
                        pass  # 如果cv2不可用或转换失败，假设已经是RGB
                        
                    image = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0).float() / 255.0
                    image = (image - 0.5) * 2.0  # [0,1] -> [-1,1]
                    
                    return self.detect(image)
            
            # 如果输入不是有效格式，返回空结果
            return {
                'boxes': torch.zeros((0, 4), device=self.device),
                'scores': torch.zeros(0, device=self.device),
                'labels': torch.zeros(0, device=self.device, dtype=torch.int64)
            }
            
        except Exception as e:
            print(f"DSFD检测过程出错: {e}")
            import traceback
            traceback.print_exc()
            # 返回空结果
            return {
                'boxes': torch.zeros((0, 4), device=self.device),
                'scores': torch.zeros(0, device=self.device),
                'labels': torch.zeros(0, device=self.device, dtype=torch.int64)
            }
                
    def forward(self, x):
        """
        模型前向传播，支持训练和推理两种模式
        
        Args:
            x: 输入图像张量
            
        Returns:
            训练/攻击模式: 返回特征
            评估模式: 返回检测结果
        """
        if self.training or self.attack_mode:
            # 攻击模式: 返回特征以支持攻击
            return self.get_features(x)
        else:
            # 评估模式: 返回检测结果
            return self.detect(x)

def init_dsfd_detector(device="cuda" if torch.cuda.is_available() else "cpu", weights_path=None, attack_mode=False):
    """
    初始化DSFD检测器
    
    Args:
        device: 计算设备
        weights_path: 权重路径
        attack_mode: 是否为攻击模式
        
    Returns:
        DSFDDetector: 检测器实例
    """
    try:
        # 创建DSFD检测器
        detector = DSFDDetector(
            confidence_threshold=0.7,
            nms_iou_threshold=0.3,
            device=device,
            model_path=weights_path,
            fp16_inference=False,
            clip_boxes=True,
            attack_mode=attack_mode
        )
        
        # 设置模式
        if attack_mode:
            detector.train()
            # print(f"DSFD检测器已初始化，处于攻击模式")
        else:
            detector.eval()
            # print(f"DSFD检测器已初始化，处于评估模式")
        
        return detector
        
    except Exception as e:
        print(f"初始化DSFD检测器出错: {e}")
        import traceback
        traceback.print_exc()
        return None