import argparse
import copy
import json
import os
from os.path import join
import numpy as np
from tqdm import tqdm
from random import shuffle
import torch
import torchvision
import get_output
from model_data_prepare import prepare
from evaluate_fid import evaluate_multiple_models
from attgan.data import check_attribute_conflict
import torch.nn.functional as TFunction
import torch.nn.functional as f
from skimage.metrics import structural_similarity
import torchvision.models as models
from torchvision.transforms import GaussianBlur

# 设置 GPU 设备
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'Using device: {device}')
# 设置攻击策略的开关
do_end_to_end = True
do_feature_ensemble = True
do_grad_ensemble = True

do_output_ensemble = False
do_loss_ensemble = False

batch_evaluate = True


def parse(args=None):
    with open(join('./setting.json'), 'r') as f:
        args_attack = json.load(f, object_hook=lambda d: argparse.Namespace(**d))
    return args_attack


# Init the attacker
def init_get_outputs(args_attack):
    get_output_models = get_output.get_all_features(model=None, device=torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
                                                    epsilon=args_attack.attacks.epsilon, args=args_attack.attacks)
    return get_output_models


def just_mean(d_grads):
    d_grads = torch.stack([d for d in d_grads])
    return torch.mean(d_grads, dim=0)

def denorm(x):
    """Convert the range from [-1, 1] to [0, 1]."""
    out = (x + 1) / 2
    return out.clamp_(0, 1)



def kl_divergence(p, q):
    """
    计算KL散度
    p, q: 输入张量，需要确保它们是概率分布（和为1的非负值）
    """
    # 将特征图转换为概率分布
    p = TFunction.softmax(p.view(p.size(0), -1), dim=1)
    q = TFunction.softmax(q.view(q.size(0), -1), dim=1)
    
    # 添加小值防止log(0)
    epsilon = 1e-10
    p = torch.clamp(p, min=epsilon)
    q = torch.clamp(q, min=epsilon)
    
    # 计算KL散度: KL(p||q) = Σ p(x) * log(p(x)/q(x))
    kl_div = torch.sum(p * torch.log(p/q), dim=1).mean()
    return kl_div


def perform_AttGAN(attack, input_imgs, original_imgs, attibutes, attgan_model, attgan_parse, rand_param):
    att_b_list = [attibutes]
    # No need to attack all attributes
    for i in range(attgan_parse.n_attrs):
        tmp = attibutes.clone()
        tmp[:, i] = 1 - tmp[:, i]
        tmp = check_attribute_conflict(tmp, attgan_parse.attrs[i], attgan_parse.attrs)
        att_b_list.append(tmp)
    att_b_list = [att_b_list[0]]
    
    for i, att_b in enumerate(att_b_list):
        att_b_ = (att_b * 2 - 1) * attgan_parse.thres_int
        if i > 0:
            att_b_[..., i - 1] = att_b_[..., i - 1] * attgan_parse.test_int / attgan_parse.thres_int
        with torch.no_grad():
            gen_noattack, no_attack_middle = attgan_model.G(original_imgs, att_b_)
        adv_gen, adv_gen_middle = attack.get_attgan_features(input_imgs, att_b_, attgan_model, rand_param)

    return [gen_noattack, adv_gen], [no_attack_middle[-1], adv_gen_middle[-1]]


def perform_HiSD(attack, input_imgs, original_imgs, reference_img, E_model, F_model, T_model, G_model, EFTG_models, rand_param):
    with torch.no_grad():
        # get the original deepfake images
        c = E_model(original_imgs)
        c_trg = c
        s_trg = F_model(reference_img, 1)
        c_trg = T_model(c_trg, s_trg, 1)
        x_trg = G_model(c_trg)

    adv_x_trg, adv_c = attack.get_hisd_features(input_imgs.cuda(), reference_img, F_model, T_model, G_model, E_model, EFTG_models, rand_param)
    return [x_trg, adv_x_trg], [c, adv_c]





def select_model_to_get_feature_pairs(case, img, ori_imgs, reference, attribute_c, attribute_attgan, attack, stargan_s, atggan_s,
                                      attgan_s, attgan_args, EE, FF, TT, GG, g_models, reconstruct=128, attr_aug= False):
    if attr_aug:
        rand_q = np.random.rand()
    else:
        rand_q = 0
    if case == 0:
        # print('attacking stargan...')
        output_pair, middle_pair = stargan_s.perform_stargan(img, ori_imgs, attribute_c, attack, rand_q)
    elif case == 1:
        # print('attacking attentiongan...')
        output_pair, middle_pair = atggan_s.perform_attentiongan(img, ori_imgs, attribute_c, attack, rand_q)
    elif case == 2:
        # print('attacking AttGan...')
        output_pair, middle_pair = perform_AttGAN(attack, img, ori_imgs, attribute_attgan, attgan_s, attgan_args, rand_q)
    elif case == 3:
        # print('attacking HiSD...')
        output_pair, middle_pair = perform_HiSD(attack, img, ori_imgs, reference, EE, FF, TT, GG, g_models, rand_q)
    else:
        raise NotImplementedError('wrong code!')

    # resize feature outputs
    new_middle_pair = []
    new_output_pair = []
    for middle in middle_pair:
        new_middle = torch.nn.functional.interpolate(middle, (reconstruct, reconstruct), mode='bilinear')  # 8, 256, 128, 128
        new_middle_pair.append(new_middle)
    # for output in output_pair:
    #     new_output  = torch.nn.functional.interpolate(output, (reconstruct, reconstruct), mode='bilinear')
    #     new_output_pair.append(new_output)
    # return new_output_pair, new_middle_pair
    return output_pair, new_middle_pair


def DI(X_in):
    import torch.nn.functional as F

    rnd = np.random.randint(256, 290, size=1)[0]
    h_rem = 290 - rnd
    w_rem = 290 - rnd
    pad_top = np.random.randint(0, h_rem, size=1)[0]
    pad_bottom = h_rem - pad_top
    pad_left = np.random.randint(0, w_rem, size=1)[0]
    pad_right = w_rem - pad_left

    c = np.random.rand(1)
    if c <= 0.5:
        X_out = F.pad(F.interpolate(X_in, size=(rnd, rnd)), (pad_left, pad_right, pad_top, pad_bottom), mode='constant',
                      value=0)
        return F.interpolate(X_out, (256, 256))
    else:
        return F.interpolate(X_in, (256, 256))

# def DI(x_in, prob=0.5):
#     # 设备兼容处理
#     device = x_in.device
    
#     # 核心缩放逻辑（保留原始DI功能）
#     if np.random.rand() < 0.5:
#         # 原始缩放填充逻辑
#         rnd = np.random.randint(256, 290)
#         x = f.interpolate(x_in, size=(rnd, rnd), mode='bilinear')
#         pad_h = (290 - rnd) // 2
#         pad_w = (290 - rnd) // 2
#         x = f.pad(x, [pad_w]*4, mode='constant', value=0)
#         x = f.interpolate(x, x_in.shape[2:], mode='bilinear')
#     else:
#         x = x_in.clone()
    
#     # 版本兼容的高斯模糊
#     if np.random.rand() < 0.3:  # 30%概率应用模糊
#         try:
#             # 方案1：使用torchvision官方实现（推荐）
#             blur_layer = GaussianBlur(
#                 kernel_size=3 + 2*np.random.randint(0,3),  # 随机3/5/7
#                 sigma=(0.1, 2.0)
#             )
#             x = blur_layer(x)  # 自动保持形状 [B,C,H,W]
#         except:
#             # 方案2：PyTorch原生函数兼容
#             kernel_size = 2*np.random.randint(1,3) + 1
#             x = f.gaussian_blur2d(
#                 x, 
#                 kernel_size=(kernel_size, kernel_size),
#                 sigma=(1.0, 1.0),
#                 padding= kernel_size//2
#             )
    
#     return x.requires_grad_(x_in.requires_grad)



class AdaptiveMomentum:
    def __init__(self, beta_base=0.9):
        self.beta_base = beta_base  # 基础动量系数
        self.momentum = None        # 动量缓存
        self.cos_sim = torch.nn.CosineSimilarity(dim=0)  # 余弦相似度计算器
        
    def update(self, grad):
        if self.momentum is None:
            self.momentum = grad.clone().detach()
        else:
            # 动态调整动量系数
            current_cos = self.cos_sim(grad.flatten(), self.momentum.flatten())
            dynamic_beta = self.beta_base + 0.1 * (1 - current_cos.item())
            
            # 动量更新
            self.momentum = dynamic_beta * self.momentum + grad
        
        return self.momentum.clone()

def enhance_feature_contrast(clean_feat, adv_feat, temperature=1.0, alpha=0.5):
    """特征对比增强，增加特征间的差异同时保留所有重要特征信息
    
    Args:
        clean_feat: 原始特征
        adv_feat: 对抗特征
        temperature: 温度参数，控制特征分布的平滑度
        alpha: 增强强度
        
    Returns:
        enhanced_adv_feat: 增强后的对抗特征
    """
    # 特征通道注意力
    def channel_attention(x):
        b, c = x.size()
        # 计算通道间的相关性
        channel_att = torch.matmul(x, x.transpose(1, 0)) / (torch.sqrt(torch.tensor(c).float()) + 1e-7)
        channel_att = torch.softmax(channel_att, dim=1)
        return torch.matmul(channel_att, x)

    # 计算特征统计信息
    clean_mean = torch.mean(clean_feat, dim=1, keepdim=True)
    clean_std = torch.std(clean_feat, dim=1, keepdim=True)
    adv_mean = torch.mean(adv_feat, dim=1, keepdim=True)
    adv_std = torch.std(adv_feat, dim=1, keepdim=True)
    
    # 特征归一化
    clean_norm = (clean_feat - clean_mean) / (clean_std + 1e-7)
    adv_norm = (adv_feat - adv_mean) / (adv_std + 1e-7)
    
    # 应用通道注意力
    clean_att = channel_attention(clean_norm.view(clean_norm.size(0), -1))
    adv_att = channel_attention(adv_norm.view(adv_norm.size(0), -1))
    
    # 计算多尺度特征差异
    diff_local = adv_norm - clean_norm  # 局部差异
    diff_global = (adv_mean - clean_mean) / (clean_std + 1e-7)  # 全局差异
    diff_structure = adv_att.view_as(adv_norm) - clean_att.view_as(clean_norm)  # 结构差异
    
    # 自适应权重
    w_local = torch.sigmoid(torch.mean(torch.abs(diff_local), dim=1, keepdim=True))
    w_global = torch.sigmoid(torch.mean(torch.abs(diff_global), dim=1, keepdim=True))
    w_structure = torch.sigmoid(torch.mean(torch.abs(diff_structure), dim=1, keepdim=True))
    
    # 综合增强方向
    enhancement = (
        w_local * diff_local + 
        w_global * diff_global + 
        w_structure * diff_structure
    )
    
    # 应用温度缩放
    scale = torch.exp(torch.mean(torch.abs(enhancement), dim=1, keepdim=True) / temperature)
    
    # 生成增强后的特征，保持原始特征结构
    enhanced_adv_feat = adv_feat + alpha * scale * enhancement
    
    # 特征重标定，确保增强后的特征分布合理
    enhanced_mean = torch.mean(enhanced_adv_feat, dim=1, keepdim=True)
    enhanced_std = torch.std(enhanced_adv_feat, dim=1, keepdim=True)
    enhanced_adv_feat = (enhanced_adv_feat - enhanced_mean) * (adv_std / (enhanced_std + 1e-7)) + adv_mean
    
    return enhanced_adv_feat

def train_attacker():
    args_attack = parse()
    print(args_attack)



    momentum_controller = AdaptiveMomentum(beta_base=0.8)
    
    # Init the attacker
    attack_utils = init_get_outputs(args_attack)
    # Init the attacked models
    attack_dataloader, test_dataloader, attgan, attgan_args, stargan_solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()

    model_cases = [0, 1, 2, 3]
    import time
    start_time = time.time()

    # Some hyperparameters
    attack_utils.epsilon = 0.05
    reconstruct_feature_size = 32
    iteration_out = 30
    iteration_in = 5
    alpha = 1e-3

    # pgd
    attack_utils.up = attack_utils.up + torch.tensor(
        np.random.uniform(-attack_utils.epsilon, attack_utils.epsilon, attack_utils.up.shape).astype('float32')
    ).to(attack_utils.device)
    momentum = 0

    for t in range(iteration_out):
        print('%dth iter' % t)

        for idx, (img_a, att_a, c_org) in enumerate(tqdm(attack_dataloader)):
            print('%dth batch' % idx)
            if args_attack.global_settings.num_test is not None and idx * args_attack.global_settings.batch_size == args_attack.global_settings.num_test:
                break
            img_a = img_a.cuda() if args_attack.global_settings.gpu else img_a
            att_a = att_a.cuda() if args_attack.global_settings.gpu else att_a
            att_a = att_a.type(torch.float)

            if do_feature_ensemble:
                # Feature-Ensemble
                for _ in range(iteration_in):
                    attack_utils.up.requires_grad = True
                    new_input = img_a + attack_utils.up
                    
                    # 每个外部迭代开始时清理内存
                    if _ == 0:
                        torch.cuda.empty_cache()
                        
                    middle_pairs = []
                    shuffle(model_cases)
                    for case in model_cases:
                        _, mid_pair = select_model_to_get_feature_pairs(
                            case, 
                            DI(new_input),  # 限制历史大小
                            img_a, reference, c_org, att_a,
                            attack_utils, stargan_solver,
                            attentiongan_solver, attgan, attgan_args,
                            E, F, T, G, gen_models, 
                            reconstruct_feature_size
                        )
                        middle_pairs.append(mid_pair)
                        
                    # 处理完一批数据后清理内存
                    if idx % 10 == 0:
                        torch.cuda.empty_cache()

                    clean_middle_from_models = [middle_pairs[p][0] for p in range(len(middle_pairs))]
                    adv_middle_from_models = [middle_pairs[q][1] for q in range(len(middle_pairs))]
                    clean_features_cat = torch.cat(clean_middle_from_models, 1)  # concat all clean features in channel dimension
                    adv_features_cat = torch.cat(adv_middle_from_models, 1)  # concat all adv features in channel dimension
                    adv_features = enhance_feature_contrast(
                        torch.sum(clean_features_cat, 1), 
                        torch.sum(adv_features_cat, 1),
                        temperature=0.1,  # 降低温度以增强特征差异
                        alpha=0.6  # 适当调整增强强度
                    )
                    deepfake_loss1 = -1 * attack_utils.loss_fn(adv_features, torch.sum(clean_features_cat, 1))
                    deepfake_loss2 = -1 * attack_utils.loss_fn(torch.sum(adv_features_cat, 1), torch.sum(clean_features_cat, 1))
                    deepfake_loss = 0.1 * deepfake_loss1 + 0.9 * deepfake_loss2 
                    # # 6. 组合总损失
                    total_loss = deepfake_loss

                    # loss_wa = -attack_utils.wasserstein_loss(torch.sum(adv_features_cat, 1), torch.sum(clean_features_cat, 1))
                    # loss_fn = -attack_utils.loss_fn(torch.sum(adv_features_cat, 1), torch.sum(clean_features_cat, 1))
                    

                    loss = total_loss
                    loss.backward(retain_graph=True)

                    grad_c = attack_utils.up.grad.clone().to(attack_utils.device)
                    grad_c_hat = grad_c / (torch.mean(torch.abs(grad_c), (1, 2, 3), keepdim=True) + 1e-12)
                    attack_utils.up.grad.zero_()

                    attack_utils.up.data = attack_utils.up.data - alpha * torch.sign(grad_c_hat)
                    attack_utils.up.data = attack_utils.up.data.clamp(-attack_utils.epsilon, attack_utils.epsilon)
                    attack_utils.up = attack_utils.up.detach()

            if do_end_to_end:
                # End-to-End Ensemble
                attack_utils.up.requires_grad = True
                new_new_input = img_a + attack_utils.up
                output_grads = []  # grad_ensemble
                logit_pairs = []  # logit_ensemble
                out_loss = 0  # loss_ensemble
                shuffle(model_cases)
                for case in model_cases:
                    out_pairs = []
                    for model_case in model_cases:
                        out_pair, _ = select_model_to_get_feature_pairs(model_case, DI(new_new_input), img_a, reference, c_org, att_a,
                                                                    attack_utils, stargan_solver,
                                                                    attentiongan_solver, attgan, attgan_args,
                                                                    E, F, T, G, gen_models, reconstruct_feature_size)
                        out_pairs.append(out_pair)

                    # 合并所有模型的输出
                    clean_outputs = [pair[0] for pair in out_pairs]
                    adv_outputs = [pair[1] for pair in out_pairs]
                    clean_outputs_cat = torch.cat(clean_outputs, 1)
                    adv_outputs_cat = torch.cat(adv_outputs, 1)

                    loss_wa = -1 * attack_utils.loss_fn(torch.sum(adv_outputs_cat, 1), torch.sum(clean_outputs_cat, 1))

                    loss_one = loss_wa
                    if do_grad_ensemble: 
                        loss_one.backward(retain_graph=True)
                        grad_case = attack_utils.up.grad.clone().to(attack_utils.device)
                        grad_case = grad_case / (torch.mean(torch.abs(grad_case), (1, 2, 3), keepdim=True) + 1e-12)
                        output_grads.append(grad_case)
                        attack_utils.up.grad.zero_()
                    elif do_loss_ensemble:
                        out_loss += loss_one
                    elif do_output_ensemble:
                        logit_pairs.append(out_pair)
                if do_grad_ensemble:
                    # grad_cout_hat = torch.mean(torch.stack(output_grads), dim=0)
                    grad_cout_hat = just_mean(output_grads)

                    
                elif do_loss_ensemble:
                    out_loss.backward()
                    grad_cout_hat = attack_utils.up.grad.clone().to(attack_utils.device)
                    grad_cout_hat = grad_cout_hat / (torch.mean(torch.abs(grad_cout_hat), (1, 2, 3), keepdim=True) + 1e-12)
                elif do_output_ensemble:
                    clean_out = [logit_pairs[p][0] for p in range(len(logit_pairs))]
                    adv_out = [logit_pairs[q][1] for q in range(len(logit_pairs))]
                    clean_cat = torch.cat(clean_out, 1)
                    adv_cat = torch.cat(adv_out, 1)
                    logit_loss = -1 * attack_utils.loss_fn(torch.sum(adv_cat, 1), torch.sum(clean_cat, 1))
                    logit_loss.backward()
                    grad_cout_hat = attack_utils.up.grad.clone().to(attack_utils.device)
                    grad_cout_hat = grad_cout_hat / (torch.mean(torch.abs(grad_cout_hat), (1, 2, 3), keepdim=True) + 1e-12)
                else:
                    raise NotImplementedError('choose one ensemble from grad/loss/logit')

                # MI
                # grad_cout_hat = grad_cout_hat + 0.8 * momentum
                grad_cout_hat = momentum_controller.update(grad_c_hat)
                
                momentum = grad_cout_hat

                attack_utils.up.data = attack_utils.up.data - alpha * torch.sign(grad_cout_hat)
                attack_utils.up.data = attack_utils.up.data.clamp(-attack_utils.epsilon, attack_utils.epsilon)
                attack_utils.up = attack_utils.up.detach()

        print('up:', torch.max(attack_utils.up), torch.min(attack_utils.up))
        print('破坏攻击up.shape:', attack_utils.up.shape)
        if batch_evaluate and t % 5 == 0:
            _, _, _, _ = evaluate_multiple_models(args_attack, test_dataloader, attgan, attgan_args, stargan_solver,
                                                  attentiongan_solver,
                                                  transform, F, T, G, E, reference, gen_models, attack_utils,
                                                  max_samples=20)

    end_time = time.time()
    print('cost time:', end_time - start_time)
    torch.save(attack_utils.up, 'pert_FOUND-03-original.pt')
    _, _, _, _ = evaluate_multiple_models(args_attack, test_dataloader, attgan, attgan_args, stargan_solver,
                                          attentiongan_solver,
                                          transform, F, T, G, E, reference, gen_models, attack_utils,
                                          max_samples=1000)



if __name__ == "__main__":
    train_attacker()
