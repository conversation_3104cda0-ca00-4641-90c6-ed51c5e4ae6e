{"global_settings": {"data_path": "/root/autodl-tmp/img_align_celeba", "data_path_test": "/root/autodl-tmp/img_align_celeba", "attr_path": "/root/tf-logs/list_attr_celeba.txt", "img_size": 256, "num_test": 128, "gpu": 1, "batch_size": 8, "use_celeba": 0}, "attacks": {"epsilon": 0.05}, "AttGAN": {"attgan_experiment_name": "256_shortcut1_inject0_none", "attgan_test_int": 1.0, "attgan_load_epoch": "latest", "attgan_multi_gpu": 0}, "stargan": {"c_dim": 5, "c2_dim": 8, "celeba_crop_size": 178, "rafd_crop_size": 256, "image_size": 256, "g_conv_dim": 64, "d_conv_dim": 64, "g_repeat_num": 6, "d_repeat_num": 6, "lambda_cls": 1, "lambda_rec": 10, "lambda_gp": 10, "resume_iters": "", "dataset": "CelebA", "batch_size": 1, "num_iters": 200000, "num_iters_decay": 100000, "g_lr": 0.0001, "d_lr": 0.0001, "n_critic": 5, "beta1": 0.5, "beta2": 0.999, "selected_attrs": ["Black_Hair", "Blond_Hair", "<PERSON><PERSON><PERSON>", "Male", "<PERSON>"], "test_iters": 200000, "num_workers": 1, "mode": "test", "use_tensorboard": 0, "model_save_dir": "./stargan/stargan_celeba_256/models", "result_dir": "./stargan/stargan_celeba_256/results_test", "log_step": 10, "sample_step": 1000, "model_save_step": 5000, "lr_update_step": 1000, "data_path": "./data/img_align_celeba", "attr_path": "./data/list_attr_celeba.txt", "img_size": 256, "num_test": 10, "gpu": 1, "universal": 1, "log_dir": "./stargan/stargan/logs", "sample_dir": "./stargan/stargan/samples"}, "AttentionGAN": {"c_dim": 5, "c2_dim": 8, "celeba_crop_size": 178, "rafd_crop_size": 256, "image_size": 256, "g_conv_dim": 64, "d_conv_dim": 64, "g_repeat_num": 6, "d_repeat_num": 6, "lambda_cls": 1, "lambda_rec": 10, "lambda_gp": 10, "resume_iters": "", "dataset": "CelebA", "batch_size": 1, "num_iters": 200000, "num_iters_decay": 100000, "g_lr": 0.0001, "d_lr": 0.0001, "n_critic": 5, "beta1": 0.5, "beta2": 0.999, "selected_attrs": ["Black_Hair", "Blond_Hair", "<PERSON><PERSON><PERSON>", "Male", "<PERSON>"], "test_iters": 200000, "num_workers": 1, "mode": "test", "use_tensorboard": 0, "model_save_dir": "./attentiongan/checkpoints/celeba_256_pretrained", "result_dir": "./attentiongan/attentiongan_celeba_256/results_test", "log_step": 10, "sample_step": 1000, "model_save_step": 5000, "lr_update_step": 1000, "data_path": "./data/img_align_celeba", "attr_path": "./data/list_attr_celeba.txt", "img_size": 256, "num_test": 10, "gpu": 1, "universal": 1, "log_dir": "./attentiongan/attentiongan/logs", "sample_dir": "./attentiongan/attentiongan/samples"}}