import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import numpy as np
from tqdm import tqdm
import argparse
import json
import os
from os.path import join
import math

# Attempt to import from existing project structure
# These functions are expected to be in train_FOUND1.py or accessible in the path
try:
    from evaluate_watermark import init_detectors, test_watermark, calculate_iou, prepare, parse
    # Try to import FEATURE_LAYER_CONFIG from face_poison_integration.py
    from face_poison_integration import FEATURE_LAYER_CONFIG
except ImportError:
    print("WARN: Could not import from train_FOUND1.py and/or face_poison_integration.py. Ensure they are in PYTHONPATH or copy necessary functions/variables here.")
    # Placeholder for critical functions if direct import fails.
    # User would need to ensure these are correctly defined or copied.
    def init_detectors(device): raise NotImplementedError("init_detectors not found")
    def test_watermark(*args, **kwargs): raise NotImplementedError("test_watermark not found")
    def calculate_iou(box1, box2): raise NotImplementedError("calculate_iou not found")
    def prepare(): raise NotImplementedError("prepare not found")
    def parse(args=None):
        print("WARN: Using a default dummy args_attack due to parse() not being found.")
        return argparse.Namespace(global_settings=argparse.Namespace(gpu=torch.cuda.is_available()), attacks=argparse.Namespace())


class FacePoisonAttack:
    def __init__(self, detector, target_feature_layers_names, epsilon=16/255,
                 alpha_weights=None, lambda1=1, lambda2=1,
                 momentum=0.9, iterations=10, device='cuda'):
        """
        :param detector: 目标人脸检测器模型
        :param target_feature_layers_names: 要挂钩的特征层的名称列表
        :param epsilon: L∞扰动边界
        :param alpha_weights: 各特征层的权重系数
        :param lambda1: 误差损失权重
        :param lambda2: 关键损失权重
        :param momentum: 动量系数
        :param iterations: 攻击迭代次数
        :param device: 计算设备
        """
        self.detector = detector.to(device)
        self.target_feature_layers_names = target_feature_layers_names
        self.epsilon = epsilon
        
        if alpha_weights is None:
            # Default to equal weights if not provided
            self.alpha_weights = [1.0 / len(target_feature_layers_names)] * len(target_feature_layers_names)
        else:
            self.alpha_weights = alpha_weights

        if len(self.alpha_weights) != len(self.target_feature_layers_names):
            raise ValueError(
                f"Number of alpha_weights ({len(self.alpha_weights)}) must match "
                f"number of target_feature_layers_names ({len(self.target_feature_layers_names)})"
            )
            
        self.lambda1 = lambda1
        self.lambda2 = lambda2
        self.momentum = momentum
        self.iterations = iterations
        self.device = device
        
        self.feature_maps = []
        self.hooks = []
        self._register_hooks()
        
        # 检查是否为S3FD检测器
        detector_class_name = self.detector.__class__.__name__
        self.is_s3fd_detector = detector_class_name == "S3FD"
        if self.is_s3fd_detector:
            print("检测到S3FD检测器，将使用专用攻击方法")
        
    def _register_hooks(self):
        """根据提供的层名称列表注册特征层hook"""
        self.remove_hooks() # Clear any existing hooks

        # Determine the actual PyTorch model to hook into
        actual_model_to_hook = self.detector
        detector_wrapper_class_name = self.detector.__class__.__name__

        if hasattr(self.detector, 'net'):
            print(f"Hooking into self.detector.net for {detector_wrapper_class_name}")
            actual_model_to_hook = self.detector.net
        elif hasattr(self.detector, 'model'): # For models that use 'self.model'
            print(f"Hooking into self.detector.model for {detector_wrapper_class_name}")
            actual_model_to_hook = self.detector.model
        else:
            print(f"Hooking directly into self.detector for {detector_wrapper_class_name}")
        
        def hook_fn(module, input, output):
            self.feature_maps.append(output)
        
        found_layers_count = 0
        # self.target_feature_layers_names should be paths relative to actual_model_to_hook
        if not self.target_feature_layers_names:
            print(f"Warning: Target feature layers names list is empty for {detector_wrapper_class_name}. Hooks will not be registered.")
            return
            
        for name, module in actual_model_to_hook.named_modules():
            if name in self.target_feature_layers_names:
                self.hooks.append(module.register_forward_hook(hook_fn))
                found_layers_count += 1
        
        if found_layers_count != len(self.target_feature_layers_names):
            print(f"Warning: Expected {len(self.target_feature_layers_names)} target layers for {actual_model_to_hook.__class__.__name__} (from wrapper {detector_wrapper_class_name}), "
                  f"but only found and hooked {found_layers_count} layers.")
            print(f"  Target layers (should be relative to actual model): {self.target_feature_layers_names}")
            
            available_module_names = [n for n, m in list(actual_model_to_hook.named_modules())]
            print(f"  Available named modules in actual model ({actual_model_to_hook.__class__.__name__}) (first 30): {available_module_names[:30]}...")
            # More detailed diagnostics if no layers were hooked but targets were specified
            if found_layers_count == 0 and self.target_feature_layers_names:
                missing_layers = [target for target in self.target_feature_layers_names if target not in available_module_names]
                if missing_layers:
                    print(f"  Could not find the following target layers in the actual model: {missing_layers}")
                else:
                    print("  All target layers were found by string name in available module names, but hook registration might have failed for other reasons or they are not hookable module types.")
                raise RuntimeError(f"No target layers successfully hooked for {actual_model_to_hook.__class__.__name__} using names {self.target_feature_layers_names}. Check target_feature_layers_names and model structure.")


    def remove_hooks(self):
        """移除所有已注册的hook"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
        self.feature_maps = []

    def _cosine_similarity(self, x1, x2):
        """计算特征余弦相似度"""
        # Ensure features are on the correct device
        x1, x2 = x1.to(self.device), x2.to(self.device)
        return F.cosine_similarity(
            x1.view(x1.size(0), -1), 
            x2.view(x2.size(0), -1), 
            dim=1
        ).mean()

    def _preprocess_retinaface_input(self, image_rgb_0_to_1_bhwc_tensor):
        """
        Prepares a batch of RGB images (tensor, range [0,1], BHWC) for RetinaFace self.detector.net input.
        This mirrors the preprocessing in RetinaFaceDetector, but keeps operations differentiable.
        Output is BGR, mean-subtracted, in BCHW format for the network.
        """
        if image_rgb_0_to_1_bhwc_tensor.dim() != 4:
            raise ValueError("Input tensor must be 4D (B,H,W,C)")
        if image_rgb_0_to_1_bhwc_tensor.size(3) != 3:
            raise ValueError("Input tensor must have 3 channels (C=3)")

        img_tensor = image_rgb_0_to_1_bhwc_tensor.permute(0, 3, 1, 2) # BHWC -> BCHW
        img_tensor = img_tensor.to(self.device)

        # 1. Normalize from [0,1] to [0,255]
        img_tensor_0_255 = img_tensor * 255.0 # Still RGB, [0,255]

        # 2. Subtract mean. self.detector.mean is a numpy array [104, 117, 123] for BGR.
        # Input 'img_tensor_0_255' is RGB.
        mean_bgr_numpy = np.array([104, 117, 123], dtype=np.float32)
        mean_bgr_tensor = torch.tensor(mean_bgr_numpy, dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)

        # Create BGR image from RGB image_0_255 to subtract BGR mean correctly channel-wise
        img_bgr_0_255 = img_tensor_0_255[:, [2, 1, 0], :, :] # RGB to BGR reorder
        
        img_bgr_mean_sub = img_bgr_0_255 - mean_bgr_tensor # BGR image - BGR mean
        
        # The network self.detector.net itself expects BGR input.
        return img_bgr_mean_sub

    def _preprocess_s3fd_input(self, image_rgb_0_to_1_bhwc_tensor):
        """
        Prepares a batch of RGB images (tensor, range [0,1], BHWC) for S3FD self.detector input.
        Output is BGR, mean-subtracted, in BCHW format for the network.
        S3FD typically uses mean [104, 117, 123].
        """
        if image_rgb_0_to_1_bhwc_tensor.dim() != 4:
            raise ValueError("Input tensor must be 4D (B,H,W,C)")
        if image_rgb_0_to_1_bhwc_tensor.size(3) != 3:
            raise ValueError("Input tensor must have 3 channels (C=3)")

        img_tensor = image_rgb_0_to_1_bhwc_tensor.permute(0, 3, 1, 2) # BHWC -> BCHW
        img_tensor = img_tensor.to(self.device)

        # 1. Normalize from [0,1] to [0,255]
        img_tensor_0_255 = img_tensor * 255.0 # Still RGB, [0,255]

        # 2. Subtract mean for S3FD. Mean is [104, 117, 123] for BGR.
        mean_bgr_numpy = np.array([104, 117, 123], dtype=np.float32)
        mean_bgr_tensor = torch.tensor(mean_bgr_numpy, dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)

        img_bgr_0_255 = img_tensor_0_255[:, [2, 1, 0], :, :] # RGB to BGR reorder
        img_bgr_mean_sub = img_bgr_0_255 - mean_bgr_tensor
        
        return img_bgr_mean_sub

    def _preprocess_dsfd_input(self, image_rgb_0_to_1_bhwc_tensor):
        """
        Prepares a batch of RGB images (tensor, range [0,1], BHWC) for DSFD self.detector.model input.
        Output is BGR, mean-subtracted, in BCHW format for the network.
        DSFD typically uses mean [104, 117, 123] (VGG-style).
        """
        if image_rgb_0_to_1_bhwc_tensor.dim() != 4:
            raise ValueError("Input tensor must be 4D (B,H,W,C)")
        if image_rgb_0_to_1_bhwc_tensor.size(3) != 3:
            raise ValueError("Input tensor must have 3 channels (C=3)")

        img_tensor = image_rgb_0_to_1_bhwc_tensor.permute(0, 3, 1, 2) # BHWC -> BCHW
        img_tensor = img_tensor.to(self.device)

        # 1. Image is already [0,1] RGB. Convert to [0,255] RGB
        img_tensor_0_255 = img_tensor * 255.0

        # 2. Subtract mean for DSFD. Mean is [104, 117, 123] for BGR.
        mean_bgr_numpy = np.array([104, 117, 123], dtype=np.float32)
        mean_bgr_tensor = torch.tensor(mean_bgr_numpy, dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)

        img_bgr_0_255 = img_tensor_0_255[:, [2, 1, 0], :, :] # RGB to BGR reorder
        img_bgr_mean_sub = img_bgr_0_255 - mean_bgr_tensor
        
        return img_bgr_mean_sub
    
    def attack(self, image_0_to_1):
        """
        执行对抗攻击
        :param image_0_to_1: 原始输入图像 (C,H,W) 0-1范围
        :return: 对抗样本 (C,H,W) 0-1范围
        """
        image_0_to_1 = image_0_to_1.clone().unsqueeze(0).to(self.device) # Add batch dim and move to device
        adv_image_0_to_1 = image_0_to_1.clone()
        adv_image_0_to_1.requires_grad = True
        
        momentum_buffer = torch.zeros_like(image_0_to_1, device=self.device)
        
        self.detector.eval() # Ensure detector is in eval mode
        detector_class_name = self.detector.__class__.__name__
        # This 'net' might be specific to RetinaFace's wrapper. S3FD/DSFD might be the model itself.
        # actual_model_for_forward will be self.detector.net or self.detector.model or self.detector
        # based on how _register_hooks determined actual_model_to_hook
        
        # 针对S3FD的专用攻击
        if self.is_s3fd_detector:
            return self._attack_s3fd(image_0_to_1)
            
        # 常规攻击方法(其他检测器)
        # Determine which preprocessing and model call to use
        # This logic should ideally align with how actual_model_to_hook was found in _register_hooks
        # For now, explicit check. This can be refined.
        is_retinaface_detector_wrapper = "RetinaFaceDetector" in detector_class_name 
        # S3FDDetector.py defines S3FD class, which is passed directly.
        # DSFDDetector.py defines DSFDDetector (wrapper), its self.model is the actual DSFD network.
        is_s3fd_model = detector_class_name == "S3FD"
        is_dsfd_wrapper = "DSFDDetector" in detector_class_name


        for iter_num in range(self.iterations):
            self.feature_maps = [] # Clear feature maps for current iteration
            
            # 1. 前向获取原始特征
            image_for_preprocessing_orig = image_0_to_1.permute(0, 2, 3, 1) # BCHW -> BHWC
            
            processed_orig_image_for_net = None
            if is_retinaface_detector_wrapper:
                processed_orig_image_for_net = self._preprocess_retinaface_input(image_for_preprocessing_orig)
            elif is_s3fd_model:
                processed_orig_image_for_net = self._preprocess_s3fd_input(image_for_preprocessing_orig)
            elif is_dsfd_wrapper:
                processed_orig_image_for_net = self._preprocess_dsfd_input(image_for_preprocessing_orig)
            else: # Fallback, should ideally be an error or specific handler
                print(f"Warning: Unknown detector type '{detector_class_name}' for preprocessing in attack. Using RetinaFace preproc as default.")
                processed_orig_image_for_net = self._preprocess_retinaface_input(image_for_preprocessing_orig)


            with torch.no_grad():
                try:
                    if hasattr(self.detector, 'net'): # RetinaFaceDetector case
                        _ = self.detector.net(processed_orig_image_for_net)
                    elif hasattr(self.detector, 'model'): # DSFDDetector case (self.detector.model is the actual network)
                         _ = self.detector.model(processed_orig_image_for_net)
                    else: # S3FD case (self.detector is the actual network)
                        _ = self.detector(processed_orig_image_for_net)
                except Exception as e:
                    model_name_for_error = self.detector.__class__.__name__
                    if hasattr(self.detector, 'net'): model_name_for_error = self.detector.net.__class__.__name__
                    elif hasattr(self.detector, 'model'): model_name_for_error = self.detector.model.__class__.__name__
                    print(f"Error during original forward pass for detector {model_name_for_error}: {e}")
                    self.remove_hooks()
                    return image_0_to_1.squeeze(0).detach() # Return original if error
                orig_features = [f.detach().clone() for f in self.feature_maps]

            if not orig_features or len(orig_features) != len(self.target_feature_layers_names):
                print(f"Warning/Error: Incorrect number of original features extracted for {self.detector.net.__class__.__name__}. "
                      f"Expected {len(self.target_feature_layers_names)}, got {len(orig_features)}. Iter: {iter_num}")
                print(f"  Feature maps length: {len(self.feature_maps)}")
                # This might indicate an issue with hooks or the model structure.
                # If no features, loss calculation will fail.
                if not orig_features:
                    self.remove_hooks()
                    return image_0_to_1.squeeze(0).detach()


            # 2. 前向获取对抗特征
            self.feature_maps = [] # Clear for adversarial pass
            image_for_preprocessing_adv = adv_image_0_to_1.permute(0, 2, 3, 1) # BCHW -> BHWC
            
            processed_adv_image_for_net = None
            if is_retinaface_detector_wrapper:
                processed_adv_image_for_net = self._preprocess_retinaface_input(image_for_preprocessing_adv)
            elif is_s3fd_model:
                processed_adv_image_for_net = self._preprocess_s3fd_input(image_for_preprocessing_adv)
            elif is_dsfd_wrapper:
                processed_adv_image_for_net = self._preprocess_dsfd_input(image_for_preprocessing_adv)
            else: # Fallback
                print(f"Warning: Unknown detector type '{detector_class_name}' for adv preprocessing in attack. Using RetinaFace preproc.")
                processed_adv_image_for_net = self._preprocess_retinaface_input(image_for_preprocessing_adv)

            try:
                if hasattr(self.detector, 'net'):
                    _ = self.detector.net(processed_adv_image_for_net)
                elif hasattr(self.detector, 'model'):
                     _ = self.detector.model(processed_adv_image_for_net)
                else:
                    _ = self.detector(processed_adv_image_for_net)
            except Exception as e:
                model_name_for_error = self.detector.__class__.__name__
                if hasattr(self.detector, 'net'): model_name_for_error = self.detector.net.__class__.__name__
                elif hasattr(self.detector, 'model'): model_name_for_error = self.detector.model.__class__.__name__
                print(f"Error during adversarial forward pass for {model_name_for_error}: {e}")
                self.remove_hooks()
                return image_0_to_1.squeeze(0).detach() # Return original if error
            adv_features = self.feature_maps

            if not adv_features or len(adv_features) != len(self.target_feature_layers_names):
                print(f"Warning/Error: Incorrect number of adversarial features extracted for {self.detector.net.__class__.__name__}. "
                      f"Expected {len(self.target_feature_layers_names)}, got {len(adv_features)}. Iter: {iter_num}")
                if not adv_features:
                    self.remove_hooks()
                    return image_0_to_1.squeeze(0).detach()
            
            # Ensure we have the same number of original and adversarial features for zip
            min_len = min(len(orig_features), len(adv_features), len(self.alpha_weights))

            # 3. 计算误差损失L_e
            error_loss = torch.tensor(0.0, device=self.device)
            if min_len > 0 :
                for i in range(min_len):
                    error_loss += self.alpha_weights[i] * self._cosine_similarity(orig_features[i], adv_features[i])
                error_loss = -self.lambda1 * error_loss  # 最大化差异

            # 4. 计算关键损失L_k
            key_loss = torch.tensor(0.0, device=self.device)
            if min_len > 0:
                # Temporarily store requires_grad state and set it for adv_image
                adv_image_req_grad_orig_state = adv_image_0_to_1.requires_grad
                adv_image_0_to_1.requires_grad_(True)

                for i in range(min_len):
                    # Re-run detector for grad if necessary or ensure adv_features[i] allows grad
                    # We need gradients of adv_features w.r.t. adv_image_0_to_1
                    # If adv_features were detached or from a no_grad block, this would fail.
                    # The current setup _should_ allow gradients through adv_features.
                    
                    # Summing the feature map to get a scalar for autograd
                    feature_sum = adv_features[i].sum()
                    
                    grad = torch.autograd.grad(
                        outputs=feature_sum,
                        inputs=adv_image_0_to_1,
                        retain_graph=True, # Essential for multiple loss terms/iterations
                        create_graph=False # We don't need grad of grad here
                    )[0]
                    
                    # Calculate importance map M_i for feature map F_adv_i
                    # M_i should have the same spatial dimensions as F_adv_i
                    abs_grad = torch.abs(grad).detach() # Shape: [B, C_img, H_img, W_img]
                    # Average across the input image's channel dimension
                    mean_abs_grad_input_spatial = torch.mean(abs_grad, dim=1, keepdim=True) # Shape: [B, 1, H_img, W_img]
                    
                    # Resize to match the spatial dimensions of the current feature map adv_features[i]
                    # adv_features[i] has shape [B, C_feat, H_feat, W_feat]
                    target_spatial_size = adv_features[i].shape[2:] # H_feat, W_feat
                    resized_importance_map = F.interpolate(
                        mean_abs_grad_input_spatial,
                        size=target_spatial_size,
                        mode='bilinear',
                        align_corners=False
                    ) # Shape: [B, 1, H_feat, W_feat]
                    
                    # Element-wise product: resized_importance_map is broadcast across C_feat channels of adv_features[i]
                    key_loss += self.alpha_weights[i] * (resized_importance_map * adv_features[i]).sum()
                
                key_loss = -self.lambda2 * key_loss  # 最大化关键区域差异

                # Restore requires_grad state
                adv_image_0_to_1.requires_grad_(adv_image_req_grad_orig_state)


            # 5. 总损失
            total_loss = error_loss + key_loss
                
            # 6. 计算梯度并应用动量
            if adv_image_0_to_1.grad is not None:
                adv_image_0_to_1.grad.zero_()

            total_loss.backward()
            grad = adv_image_0_to_1.grad.clone() # Get gradient
            
            # Normalize grad (DI-FGSM like)
            grad_norm = grad / (torch.mean(torch.abs(grad), dim=(1,2,3), keepdim=True) + 1e-8)
            momentum_buffer = self.momentum * momentum_buffer + grad_norm
                
            # 7. 更新对抗样本 (PGD-like step)
            # The original paper might use a fixed step size, e.g., epsilon/iterations or a small alpha
            update_step = self.epsilon / self.iterations # Or a fixed small alpha like 1/255 or 2/255
            
            adv_image_0_to_1 = adv_image_0_to_1 + update_step * momentum_buffer.sign()
            
            # Project to L_infinity ball around original image
            adv_image_0_to_1 = torch.max(adv_image_0_to_1, image_0_to_1 - self.epsilon)
            adv_image_0_to_1 = torch.min(adv_image_0_to_1, image_0_to_1 + self.epsilon)
            
            # Clip to valid image range [0, 1]
            adv_image_0_to_1 = torch.clamp(adv_image_0_to_1, 0, 1).detach_()
            adv_image_0_to_1.requires_grad_(True) # Keep requires_grad for next iteration
        
        self.remove_hooks() # Clean up hooks after attack finishes
        return adv_image_0_to_1.squeeze(0).detach() # Remove batch dim

    def _attack_s3fd(self, image_0_to_1):
        """S3FD专用攻击方法，针对conv3_3_raw_conf层"""
        try:
            import S3FDDetector as sfd_detector_module
            
            # Initialize variables
            adv_image_0_to_1 = image_0_to_1.clone()
            adv_image_0_to_1.requires_grad = True
            momentum_buffer = torch.zeros_like(image_0_to_1, device=self.device)
            
            # 获取原始样本的检测结果供后续对比
            processed_orig_image = self._preprocess_s3fd_input(image_0_to_1.permute(0, 2, 3, 1))
            with torch.no_grad():
                # 使用S3FDDetector的特征提取函数(假设导入成功)
                if hasattr(sfd_detector_module, 'get_features'):
                    orig_features, orig_outputs, orig_conv3_3_raw_conf = sfd_detector_module.get_features(
                        self.detector, processed_orig_image
                    )
                    print(f"成功提取S3FD原始特征 conv3_3_raw_conf 形状: {orig_conv3_3_raw_conf.shape}")
                else:
                    print("未找到S3FD的get_features函数，使用常规攻击")
                    return super().attack(image_0_to_1.squeeze(0))
            
            # 优化迭代
            for iter_num in range(self.iterations):
                if adv_image_0_to_1.grad is not None:
                    adv_image_0_to_1.grad.zero_()
                
                # 处理当前对抗样本
                processed_adv_image = self._preprocess_s3fd_input(adv_image_0_to_1.permute(0, 2, 3, 1))
                
                # 提取特征，包括conv3_3_raw_conf
                try:
                    _, _, conv3_3_raw_conf = sfd_detector_module.get_features(
                        self.detector, processed_adv_image
                    )
                    
                    if conv3_3_raw_conf is None or conv3_3_raw_conf.numel() == 0:
                        print(f"迭代 {iter_num+1}: 无法获取conv3_3_raw_conf，尝试直接攻击")
                        # 直接前向传播获取梯度
                        outputs = self.detector(processed_adv_image)
                        if isinstance(outputs, tuple) and len(outputs) > 1:
                            loss = -outputs[1].mean()  # 最小化置信度
                            loss.backward()
                        continue
                    
                    # 确保requires_grad
                    if not conv3_3_raw_conf.requires_grad:
                        conv3_3_raw_conf = conv3_3_raw_conf.clone().requires_grad_(True)
                    
                    # S3FD特有的损失函数：直接操作conv3_3_raw_conf
                    # S3FD的conv3_3_raw_conf是一个张量[B, 4, H, W]
                    # 其中首3个通道是背景类别，最后一个通道是人脸类别
                    
                    # 分离人脸和背景的logits
                    face_logits_c3 = conv3_3_raw_conf[:, 3, :, :]      # 形状 [B, H, W] - 人脸通道
                    bg_logits_c3_all = conv3_3_raw_conf[:, 0:3, :, :]  # 形状 [B, 3, H, W] - 背景通道
                    
                    # 为了更强的攻击效果，我们同时操作人脸通道和背景通道
                    # 1. 降低人脸通道的分数
                    face_loss = torch.mean(face_logits_c3)  # 最小化人脸分数
                    
                    # 2. 提高背景通道的分数
                    max_bg_logits_c3, _ = torch.max(bg_logits_c3_all, dim=1)   # 取背景通道的最大值 [B, H, W]
                    bg_loss = -torch.mean(max_bg_logits_c3)  # 最大化背景分数
                    
                    # 3. 增加背景与人脸的差距
                    diff_loss = -torch.mean(max_bg_logits_c3 - face_logits_c3)  # 最大化差距
                    
                    # 组合损失，权重可以调整
                    loss = face_loss * 5.0 + bg_loss * 5.0 + diff_loss * 20.0
                    
                    # 反向传播
                    loss.backward()
                    
                    print(f"S3FD专用攻击 - 迭代 {iter_num+1}/{self.iterations}, 损失: {loss.item():.4f} (face:{face_loss.item():.4f}, bg:{bg_loss.item():.4f}, diff:{diff_loss.item():.4f})")
                
                except Exception as e:
                    print(f"S3FD反向传播出错: {e}")
                    # 备选方案：直接使用模型输出计算梯度
                    try:
                        outputs = self.detector(processed_adv_image)
                        if isinstance(outputs, tuple) and len(outputs) > 1:
                            fallback_loss = -outputs[1].mean()  # 最小化置信度
                            fallback_loss.backward()
                    except:
                        pass
                
                # 获取梯度并应用
                if adv_image_0_to_1.grad is None:
                    print(f"警告: 第{iter_num+1}次迭代梯度为None")
                    continue
                
                grad = adv_image_0_to_1.grad.clone()
                if not torch.isfinite(grad).all():
                    print(f"警告: 第{iter_num+1}次迭代梯度无效，使用随机梯度")
                    grad = torch.randn_like(grad) * 0.01
                
                # 梯度预处理 - 规范化并增强高频成分
                grad_norm = torch.norm(grad, p=float('inf'))
                if grad_norm > 0:
                    normalized_grad = grad / grad_norm
                    
                    # 增强高频部分
                    if iter_num > 0 and iter_num % 2 == 0:  # 每隔一次迭代
                        kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], 
                                           dtype=torch.float32, device=self.device).view(1, 1, 3, 3)
                        kernel = kernel.repeat(3, 1, 1, 1)
                        high_freq = F.conv2d(normalized_grad, kernel, padding=1, groups=3)
                        normalized_grad = normalized_grad + 0.3 * torch.sign(high_freq)
                        normalized_grad = normalized_grad / torch.norm(normalized_grad, p=float('inf'))
                    
                    # 动量更新
                    momentum_buffer = self.momentum * momentum_buffer + normalized_grad
                    
                    # 使用较大的步长，确保攻击效果
                    step_size = self.epsilon / 2 * (1 + 0.1 * iter_num / self.iterations)  # 逐渐增加步长
                    adv_image_0_to_1 = adv_image_0_to_1 + step_size * torch.sign(momentum_buffer)
                    
                    # 约束在epsilon球内
                    delta = adv_image_0_to_1 - image_0_to_1
                    delta = torch.clamp(delta, -self.epsilon, self.epsilon)
                    adv_image_0_to_1 = torch.clamp(image_0_to_1 + delta, 0, 1).detach()
                    adv_image_0_to_1.requires_grad = True
            
            # 最终处理
            with torch.no_grad():
                # 最后再增强一次高频特征
                kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], 
                                    dtype=torch.float32, device=self.device).view(1, 1, 3, 3)
                kernel = kernel.repeat(3, 1, 1, 1)
                delta = adv_image_0_to_1 - image_0_to_1
                high_freq = F.conv2d(delta, kernel, padding=1, groups=3)
                enhanced_delta = delta + 0.5 * torch.sign(high_freq) * self.epsilon * 0.3
                enhanced_delta = torch.clamp(enhanced_delta, -self.epsilon, self.epsilon)
                adv_image_0_to_1 = torch.clamp(image_0_to_1 + enhanced_delta, 0, 1)
                
                # 验证攻击效果
                processed_final_adv = self._preprocess_s3fd_input(adv_image_0_to_1.permute(0, 2, 3, 1))
                _, final_outputs, final_conv3_3_raw_conf = sfd_detector_module.get_features(
                    self.detector, processed_final_adv
                )
                
                # 分析结果
                orig_face_logits = orig_conv3_3_raw_conf[:, 3, :, :].mean().item()
                adv_face_logits = final_conv3_3_raw_conf[:, 3, :, :].mean().item()
                
                orig_bg_logits = torch.max(orig_conv3_3_raw_conf[:, 0:3, :, :], dim=1)[0].mean().item()
                adv_bg_logits = torch.max(final_conv3_3_raw_conf[:, 0:3, :, :], dim=1)[0].mean().item()
                
                print(f"S3FD专用攻击结果 - 原始人脸logits: {orig_face_logits:.4f}, 对抗样本人脸logits: {adv_face_logits:.4f}")
                print(f"人脸logits变化: {adv_face_logits - orig_face_logits:.4f}")
                print(f"背景logits变化: {adv_bg_logits - orig_bg_logits:.4f}")
                print(f"检测效果变化: {(adv_bg_logits - adv_face_logits) - (orig_bg_logits - orig_face_logits):.4f}")
            
            return adv_image_0_to_1.squeeze(0).detach()
            
        except Exception as e:
            print(f"S3FD专用攻击出错: {e}")
            import traceback
            traceback.print_exc()
            return image_0_to_1.squeeze(0).detach()  # 出错时返回原始图像


def get_target_layers_for_detector(detector_name_from_main, detector_model_instance):
    """
    获取指定检测器的目标特征层名称，尝试从 face_poison_integration.FEATURE_LAYER_CONFIG 获取。
    选择 'backbone_early', 'backbone_middle', 'backbone_late' 各一个代表性层，并转换为实际模型的相对路径。
    """
    print(f"Attempting to get target layers for: {detector_name_from_main} using FEATURE_LAYER_CONFIG")

    config_key = None
    if detector_name_from_main == "retinaface":
        actual_net_module = None
        if hasattr(detector_model_instance, 'net'):
            actual_net_module = detector_model_instance.net
        elif hasattr(detector_model_instance, 'model'):
            actual_net_module = detector_model_instance.model
        else:
            actual_net_module = detector_model_instance

        # Determine backbone for RetinaFace based on actual_net_module's properties
        if hasattr(actual_net_module, 'cfg') and actual_net_module.cfg.get('name') == 'Resnet50':
            config_key = "retinaface_resnet"
        elif hasattr(actual_net_module, 'body') and hasattr(actual_net_module.body, 'body'): # Heuristic for Mobilenet structure within .net
            config_key = "retinaface_mobilenet"
        else: # Fallback or if structure is different
            print(f"Warning: Could not definitively determine RetinaFace backbone for {detector_name_from_main}. Checking class name as fallback.")
            if "resnet" in str(actual_net_module.__class__).lower():
                 config_key = "retinaface_resnet"
            else:
                 config_key = "retinaface_mobilenet" # Default assumption

    elif detector_name_from_main == "dsfd":
        config_key = "dsfd"
    elif detector_name_from_main == "s3fd":
        config_key = "s3fd"
    else:
        print(f"Warning: Unknown detector_name '{detector_name_from_main}' for FEATURE_LAYER_CONFIG. Returning empty list.")
        return []

    if not FEATURE_LAYER_CONFIG:
        print("Error: FEATURE_LAYER_CONFIG is not available or empty. Cannot determine target layers.")
        return []

    short_target_layers_from_cfg = []
    categories_to_pick = ["backbone_early", "backbone_middle", "backbone_late"]
    
    for category in categories_to_pick:
        if category in FEATURE_LAYER_CONFIG and config_key in FEATURE_LAYER_CONFIG[category]:
            layers_in_category = FEATURE_LAYER_CONFIG[category][config_key]
            if layers_in_category:
                short_target_layers_from_cfg.append(layers_in_category[-1]) # Pick the last one
            else:
                print(f"Warning: No layers found for {config_key} in category {category} of FEATURE_LAYER_CONFIG.")
        else:
            print(f"Warning: Category {category} or config_key {config_key} not found in FEATURE_LAYER_CONFIG for key {config_key}.")

    if not short_target_layers_from_cfg:
        print(f"Error: Could not extract any short target layers for {detector_name_from_main} ({config_key}) from FEATURE_LAYER_CONFIG.")
        return []

    # Convert short names to full paths relative to the actual underlying model (e.g., wrapper.net)
    final_target_layers = []
    if config_key == "retinaface_mobilenet":
        # face_poison_integration.py implies model.body.body.stageX for mobilenet
        # However, the actual RetinaFace model (self.net) structure is typically self.net.body.stageX
        # Log output confirms available modules are like 'body.stage1' directly under self.net
        for short_name in short_target_layers_from_cfg:
            if short_name.startswith("stage"): # e.g. stage1, stage2, stage3
                final_target_layers.append(f"body.{short_name}") # Corrected path
            else:
                print(f"Warning: Unexpected short_name '{short_name}' for retinaface_mobilenet, expected 'stageX'. Using as is.")
                final_target_layers.append(short_name) 
    elif config_key == "retinaface_resnet":
        # face_poison_integration.py implies model.body.layerX for resnet
        # So, paths relative to the model (detector_model_instance.net) are "body.layerX"
        for short_name in short_target_layers_from_cfg:
            if short_name.startswith("layer"): # e.g. layer1, layer2, layer3
                final_target_layers.append(f"body.{short_name}")
            else:
                print(f"Warning: Unexpected short_name '{short_name}' for retinaface_resnet, expected 'layerX'. Using as is.")
                final_target_layers.append(short_name)
    elif config_key == "s3fd":
        # Based on S3FDDetector.py, S3FD model is self.model
        # User wants to target conv3_3, conv4_3, conv5_3 which map to vgg.14, vgg.21, vgg.28
        print(f"  User specified target layers for S3FD: vgg.14 (conv3_3), vgg.21 (conv4_3), vgg.28 (conv5_3)")
        final_target_layers = ["vgg.14", "vgg.21", "vgg.28"]
        # The old mapping logic based on FEATURE_LAYER_CONFIG is now overridden.
        # s3fd_map = {
        #     "conv2_2": "vgg.7",   # backbone_early
        #     "conv3_3": "vgg.14",  # backbone_middle
        #     "conv4_3": "vgg.21",  # An alternative for S3FD if needed
        #     "conv5_3": "vgg.28"   # backbone_late
        # }
        # for short_name in short_target_layers_from_cfg:
        #     if short_name in s3fd_map:
        #         final_target_layers.append(s3fd_map[short_name])
        #     else:
        #         print(f"Warning: Unknown short_name '{short_name}' for s3fd in mapping. Ignoring.")
                
    elif config_key == "dsfd":
        # Based on runtime logs, the actual DSFD model (ImprovedDSFD) has a 'vgg' module list.
        # User wants to target conv3_3, conv4_3, conv5_3 which map to vgg.14, vgg.21, vgg.28
        print(f"  User specified target layers for DSFD: vgg.14 (conv3_3), vgg.21 (conv4_3), vgg.28 (conv5_3)")
        final_target_layers = ["vgg.14", "vgg.21", "vgg.28"]

        # The old logic based on FEATURE_LAYER_CONFIG and conceptual mapping is overridden.
        # picked_layers_count = len(short_target_layers_from_cfg)
        #
        # vgg_conceptual_map = {
        #     "early": "vgg.14",
        #     "middle": "vgg.21",
        #     "late": "vgg.28"
        # }

        # if picked_layers_count > 0:
        #     final_target_layers.append(vgg_conceptual_map["early"])
        # if picked_layers_count > 1:
        #     final_target_layers.append(vgg_conceptual_map["middle"])
        # if picked_layers_count > 2:
        #     final_target_layers.append(vgg_conceptual_map["late"])

        # if len(final_target_layers) != picked_layers_count:
        #     print(f"Warning: DSFD layer mapping might be incomplete. Expected {picked_layers_count} layers based on FEATURE_LAYER_CONFIG, mapped {len(final_target_layers)} VGG paths.")
        #     print(f"  Short target layers from FEATURE_LAYER_CONFIG for DSFD were: {short_target_layers_from_cfg}")
        #     print(f"  Mapped to VGG paths: {final_target_layers}")
        #     if not final_target_layers and picked_layers_count > 0:
        #          print("  DSFD layer mapping to VGG paths failed to produce any layers. Attack on DSFD will likely fail if these paths are incorrect for the ImprovedDSFD model structure.")
        #          final_target_layers = []
            
    else:
        print(f"Warning: No specific path construction logic for {config_key}. Using short names directly: {short_target_layers_from_cfg}")
        final_target_layers = short_target_layers_from_cfg

    if final_target_layers:
        print(f"  Selected target layers for {detector_name_from_main} (config: {config_key}, relative to actual model): {final_target_layers}")
    elif not final_target_layers:
        print(f"  Error: Could not determine any final target layers for {detector_name_from_main} ({config_key}). Attack on this model will likely be skipped or fail.")
    
    return final_target_layers


# Helper class for test_watermark evaluation on a single image
class SingleImageDataset(torch.utils.data.Dataset):
    def __init__(self, image_tensor_neg1_1):
        self.image_tensor = image_tensor_neg1_1
    def __len__(self): return 1
    def __getitem__(self, idx): return self.image_tensor, torch.tensor([]), torch.tensor([])


class UniversalPoisonAttack:
    """实现针对多个人脸检测器都有效的通用水印攻击"""
    
    def __init__(self, detector_models_dict, epsilon=16/255, alpha=1/255, 
                 iterations=40, momentum=0.9, lambda_weights=None, device='cuda'):
        """
        :param detector_models_dict: 目标检测器模型字典 {detector_name: (model, type_str)}
        :param epsilon: L∞扰动边界
        :param alpha: 步长参数
        :param iterations: 攻击迭代次数
        :param momentum: 动量参数
        :param lambda_weights: 各个检测器的损失权重，可以是列表或字典
        :param device: 计算设备
        """
        self.detector_models_dict = detector_models_dict
        self.epsilon = epsilon
        self.alpha = alpha
        self.iterations = iterations
        self.momentum = momentum
        self.device = device
        
        detector_names = list(detector_models_dict.keys())
        
        # 处理权重参数，支持列表或字典形式
        if lambda_weights is None:
            # 默认使用相等权重
            self.lambda_weights = {name: 1.0 / len(detector_models_dict) for name in detector_names}
        elif isinstance(lambda_weights, list):
            # 如果是列表，转换为字典
            if len(lambda_weights) != len(detector_models_dict):
                print(f"警告: lambda_weights列表长度 ({len(lambda_weights)}) 与检测器数量 ({len(detector_models_dict)}) 不匹配!")
                self.lambda_weights = {name: 1.0 / len(detector_models_dict) for name in detector_names}
            else:
                self.lambda_weights = {name: weight for name, weight in zip(detector_names, lambda_weights)}
        elif isinstance(lambda_weights, dict):
            # 如果是字典，直接使用
            self.lambda_weights = lambda_weights
        else:
            # 其他情况，使用相等权重
            self.lambda_weights = {name: 1.0 / len(detector_models_dict) for name in detector_names}
        
        print(f"初始化通用水印攻击: epsilon={epsilon}, alpha={alpha}, iterations={iterations}")
        print(f"使用 {len(detector_models_dict)} 个检测器，权重为: {self.lambda_weights}")
        
        # 存储特征层钩子和特征图
        self.hooks_dict = {}  # {detector_name: [hook1, hook2, ...]}
        self.features_dict = {}  # {detector_name: [feat1, feat2, ...]}
    
    def _register_hooks(self, detector_name, detector_model, target_feature_layers):
        """为指定检测器的目标特征层注册钩子"""
        if detector_name in self.hooks_dict:
            self.remove_hooks(detector_name)
        
        self.hooks_dict[detector_name] = []
        self.features_dict[detector_name] = []
        
        # 确定实际模型
        actual_model_to_hook = detector_model
        if hasattr(detector_model, 'net'):  # RetinaFaceDetector
            actual_model_to_hook = detector_model.net
        elif hasattr(detector_model, 'model'):  # DSFDDetector
            actual_model_to_hook = detector_model.model
        
        # 注册钩子用于捕获特征
        def hook_fn(module, input, output):
            self.features_dict[detector_name].append(output)
        
        for name, module in actual_model_to_hook.named_modules():
            if name in target_feature_layers:
                self.hooks_dict[detector_name].append(module.register_forward_hook(hook_fn))
    
    def remove_hooks(self, detector_name=None):
        """移除指定检测器或所有检测器的钩子"""
        if detector_name is None:
            # 移除所有钩子
            for name in self.hooks_dict:
                for hook in self.hooks_dict[name]:
                    hook.remove()
            self.hooks_dict = {}
            self.features_dict = {}
        elif detector_name in self.hooks_dict:
            # 移除指定检测器的钩子
            for hook in self.hooks_dict[detector_name]:
                hook.remove()
            self.hooks_dict[detector_name] = []
            self.features_dict[detector_name] = []
    
    def _preprocess_image(self, image_0_to_1, detector_model, detector_type):
        """根据检测器类型预处理图像"""
        image_0_to_1_bhwc = image_0_to_1.permute(0, 2, 3, 1)  # BCHW -> BHWC
        
        if detector_type == "retinaface":
            # RetinaFace预处理 (BGR, 减均值)
            img_tensor_0_255 = image_0_to_1_bhwc.permute(0, 3, 1, 2) * 255.0
            mean_bgr = torch.tensor([104.0, 117.0, 123.0], dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)
            img_bgr = img_tensor_0_255[:, [2, 1, 0], :, :]  # RGB -> BGR
            return img_bgr - mean_bgr
        
        elif detector_type == "s3fd" or detector_type == "dsfd":
            # S3FD/DSFD预处理 (BGR, 减均值)
            img_tensor_0_255 = image_0_to_1_bhwc.permute(0, 3, 1, 2) * 255.0
            mean_bgr = torch.tensor([104.0, 117.0, 123.0], dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)
            img_bgr = img_tensor_0_255[:, [2, 1, 0], :, :]  # RGB -> BGR
            return img_bgr - mean_bgr
        
        # 默认返回原始图像
        return image_0_to_1
    
    def _extract_face_confidences(self, outputs, detector_type):
        """从检测器输出中提取人脸置信度"""
        face_confidences = None
        
        if detector_type == "retinaface":
            if isinstance(outputs, tuple) and len(outputs) >= 2:
                conf_data = outputs[1]
                if conf_data.dim() == 3 and conf_data.size(2) >= 2:
                    face_confidences = conf_data[:, :, 1]  # 人脸类别的置信度
        
        elif detector_type == "s3fd" or detector_type == "dsfd":
            if isinstance(outputs, tuple) and len(outputs) >= 2:
                conf_data = outputs[1]
                if conf_data.dim() == 3 and conf_data.size(2) >= 2:
                    face_confidences = conf_data[:, :, 1]
            elif isinstance(outputs, list) and len(outputs) > 0 and isinstance(outputs[0], dict):
                # 处理DSFD的字典输出
                for output_dict in outputs:
                    if 'scores' in output_dict and isinstance(output_dict['scores'], torch.Tensor):
                        if face_confidences is None:
                            face_confidences = output_dict['scores']
                        else:
                            face_confidences = torch.cat([face_confidences, output_dict['scores']])
        
        if face_confidences is None:
            return torch.tensor([], device=self.device)  # 返回空张量
        
        return face_confidences
    
    def _compute_feature_loss(self, orig_features, adv_features, loss_type="l2"):
        """计算特征损失"""
        if not orig_features or not adv_features or len(orig_features) != len(adv_features):
            return torch.tensor(0.0, device=self.device, requires_grad=True)
        
        loss = 0.0
        for i in range(len(orig_features)):
            # 确保特征形状一致
            if orig_features[i].shape != adv_features[i].shape:
                continue
                
            if loss_type == "l2":
                # L2距离损失
                loss += torch.mean((orig_features[i] - adv_features[i])**2)
            elif loss_type == "cosine":
                # 余弦相似度损失 (最大化差异 = 最小化相似度)
                orig_flat = orig_features[i].view(orig_features[i].size(0), -1)
                adv_flat = adv_features[i].view(adv_features[i].size(0), -1)
                cos_sim = F.cosine_similarity(orig_flat, adv_flat, dim=1).mean()
                loss += -cos_sim  # 最大化差异 = 最小化余弦相似度
            elif loss_type == "combined":
                # 组合损失 - 同时使用余弦相似度和特征值最大化
                # 1. 余弦相似度部分
                orig_flat = orig_features[i].view(orig_features[i].size(0), -1)
                adv_flat = adv_features[i].view(adv_features[i].size(0), -1)
                cos_sim = F.cosine_similarity(orig_flat, adv_flat, dim=1).mean()
                
                # 2. 最大化特征激活，扰乱检测模式
                feature_activation = torch.mean(adv_features[i])
                
                # 组合: 最小化相似度 + 最大化特征激活
                loss += -cos_sim - 0.1 * feature_activation
            else:
                # 默认使用L1距离
                loss += torch.mean(torch.abs(orig_features[i] - adv_features[i]))
                
        return loss
    
    def attack(self, image_0_to_1, target_feature_layers_dict=None, verbose=False):
        """
        执行通用水印攻击，生成对所有检测器都有效的对抗扰动
        
        :param image_0_to_1: 原始输入图像 (B,C,H,W) 范围 [0,1]
        :param target_feature_layers_dict: 检测器名称到特征层名称的映射字典 {detector_name: [layer_names]}
        :param verbose: 是否打印详细信息
        :return: 对抗样本 (B,C,H,W) 范围 [0,1]
        """
        image_0_to_1 = image_0_to_1.clone().to(self.device)
        batch_size, c, h, w = image_0_to_1.shape
        
        # 初始化对抗样本和动量缓冲区
        adv_image = image_0_to_1.clone()
        adv_image.requires_grad = True
        momentum_buffer = torch.zeros_like(image_0_to_1, device=self.device)
        
        # 为每个检测器注册特征层钩子
        if target_feature_layers_dict:
            for detector_name, target_layers in target_feature_layers_dict.items():
                if target_layers and detector_name in self.detector_models_dict:
                    self._register_hooks(detector_name, self.detector_models_dict[detector_name][0], target_layers)
                    if verbose:
                        print(f"为检测器 {detector_name} 注册了 {len(target_layers)} 个特征层钩子")
        
        # 首先获取原始图像在各个检测器上的特征和输出
        with torch.no_grad():
            orig_features_dict = {}
            orig_outputs_dict = {}
            orig_confidences_dict = {}
            
            for detector_name, (detector_model, detector_type) in self.detector_models_dict.items():
                detector_model.eval()  # 确保处于评估模式
                
                # 清空特征缓存
                if detector_name in self.features_dict:
                    self.features_dict[detector_name] = []
                
                # 预处理图像
                processed_image = self._preprocess_image(image_0_to_1, detector_model, detector_type)
                
                # 前向传播
                if hasattr(detector_model, 'net'):  # RetinaFace
                    outputs = detector_model.net(processed_image)
                elif hasattr(detector_model, 'model'):  # DSFD
                    outputs = detector_model.model(processed_image)
                else:  # S3FD
                    outputs = detector_model(processed_image)
                
                # 提取置信度
                face_confidences = self._extract_face_confidences(outputs, detector_type)
                if face_confidences.numel() > 0:
                    orig_confidences_dict[detector_name] = face_confidences.mean().item()
                else:
                    orig_confidences_dict[detector_name] = 0.0
                
                # 存储输出和特征
                orig_outputs_dict[detector_name] = outputs
                if detector_name in self.features_dict:
                    orig_features_dict[detector_name] = [f.clone() for f in self.features_dict[detector_name]]
                    if verbose:
                        print(f"检测器 {detector_name} 提取了 {len(self.features_dict[detector_name])} 个特征")
        
        if verbose:
            print(f"原始图像的人脸置信度: {orig_confidences_dict}")
        
        # 跟踪每次迭代的损失
        loss_history = []
        
        # PGD-like attack
        # 添加初始随机扰动，有助于逃离局部最小值
        with torch.no_grad():
            adv_image = adv_image + (torch.rand_like(adv_image) * 2 - 1) * self.epsilon * 0.1
            adv_image = torch.clamp(adv_image, 0, 1)
        
        # 开始迭代攻击
        for iter_num in range(self.iterations):
            # 在每次迭代开始时清空特征缓存
            for detector_name in self.features_dict:
                self.features_dict[detector_name] = []
            
            # 确保对抗图像的梯度被跟踪
            adv_image.requires_grad_(True)
            if adv_image.grad is not None:
                adv_image.grad.zero_()
            
            # 初始化总损失
            total_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
            detector_losses = {}
            
            # 针对每个检测器计算损失
            for detector_name, (detector_model, detector_type) in self.detector_models_dict.items():
                detector_model.eval()  # 确保处于评估模式
                
                # 对S3FD应用专用攻击
                s3fd_specific_attack_applied = False
                if detector_type.lower() == "s3fd":
                    s3fd_specific_attack_applied = self._attack_s3fd_specific(adv_image, image_0_to_1, detector_model)
                    if s3fd_specific_attack_applied and verbose:
                        print(f"迭代 {iter_num+1}/{self.iterations}: 应用了S3FD专用攻击")
                
                # 如果没有应用S3FD专用攻击，或者是其他检测器，使用常规攻击方式
                if not s3fd_specific_attack_applied:
                    # 预处理对抗图像
                    processed_adv_image = self._preprocess_image(adv_image, detector_model, detector_type)
                    
                    # 前向传播
                    if hasattr(detector_model, 'net'):  # RetinaFace
                        adv_outputs = detector_model.net(processed_adv_image)
                    elif hasattr(detector_model, 'model'):  # DSFD
                        adv_outputs = detector_model.model(processed_adv_image)
                    else:  # S3FD
                        adv_outputs = detector_model(processed_adv_image)
                    
                    # 1. 基于置信度的损失 - 修正为最大化损失（最小化置信度）
                    face_confidences = self._extract_face_confidences(adv_outputs, detector_type)
                    if face_confidences.numel() > 0:
                        # 最大化损失 = 最小化置信度，所以取负号
                        confidence_loss = -face_confidences.mean()  # 用负号，希望置信度越低越好
                        detector_loss = confidence_loss * 2.0  # 增加置信度损失的权重
                    else:
                        detector_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                    
                    # 2. 基于特征层的损失 (如果有注册钩子)
                    if detector_name in self.features_dict and detector_name in orig_features_dict:
                        adv_features = self.features_dict[detector_name]
                        orig_features = orig_features_dict[detector_name]
                        
                        if adv_features and orig_features and len(adv_features) == len(orig_features):
                            # 使用增强的"combined"损失函数
                            feature_loss = self._compute_feature_loss(orig_features, adv_features, "combined")
                            detector_loss = detector_loss + feature_loss
                        elif verbose and iter_num == 0:  # 只在第一次迭代时打印警告
                            print(f"警告: 检测器 {detector_name} 特征不匹配 - 原始:{len(orig_features)}, 对抗:{len(adv_features)}")
                    
                    # 3. 添加输入扰动正则化，鼓励高频扰动
                    if iter_num > self.iterations // 2:  # 只在后半段迭代使用
                        input_variation_loss = -torch.mean(torch.abs(adv_image[:,:,1:,:] - adv_image[:,:,:-1,:])) * 0.5
                        input_variation_loss += -torch.mean(torch.abs(adv_image[:,:,:,1:] - adv_image[:,:,:,:-1])) * 0.5
                        detector_loss = detector_loss + input_variation_loss
                
                    # 检查器类型特定权重，增强S3FD的攻击效果
                    if detector_type.lower() == "s3fd":
                        detector_loss = detector_loss * 3.0  # 进一步增大S3FD损失权重
                    
                    # 存储每个检测器的损失，用于调试
                    detector_losses[detector_name] = detector_loss.item()
                    
                    # 应用该检测器的权重并累加到总损失
                    total_loss = total_loss + self.lambda_weights[detector_name] * detector_loss
            
            # 记录本次迭代的损失
            loss_history.append(total_loss.item())
            
            # 反向传播计算梯度
            total_loss.backward()
            
            # 检查梯度是否为None
            if adv_image.grad is None:
                if verbose:
                    print(f"迭代 {iter_num+1}/{self.iterations}: 梯度为None，跳过本次更新。")
                continue
            
            # 使用动量方法更新梯度
            grad = adv_image.grad.detach()
            
            # 添加梯度噪声以逃离局部最小值
            if iter_num < self.iterations // 2:  # 前半部分迭代添加噪声
                noise_magnitude = 0.2 * (1.0 - iter_num / (self.iterations // 2))  # 随迭代减少噪声强度
                grad = grad + noise_magnitude * torch.randn_like(grad) * torch.norm(grad) / math.sqrt(grad.numel())
            
            grad_norm = torch.norm(grad, p=float('inf'))
            if grad_norm > 0:  # 避免除以零
                normalized_grad = grad / grad_norm
            else:
                normalized_grad = grad
                
            momentum_buffer = self.momentum * momentum_buffer + normalized_grad
            
            # 更新对抗样本 - 修正：加上梯度方向（而不是减去），因为我们是最大化损失
            with torch.no_grad():
                # 在梯度方向上更新
                # 使用更大的步长，以实现更快的优化
                step_size = self.alpha * (1 + 0.2 * iter_num / self.iterations)  # 逐渐增大步长
                adv_image = adv_image + step_size * momentum_buffer.sign()
                
                # 投影到epsilon球内
                delta = adv_image - image_0_to_1
                delta = torch.clamp(delta, -self.epsilon, self.epsilon)
                adv_image = torch.clamp(image_0_to_1 + delta, 0, 1)
            
            if verbose and (iter_num + 1) % 2 == 0:  # 更频繁地输出
                print(f"迭代 {iter_num+1}/{self.iterations}, 总损失: {total_loss.item():.4f}, 各检测器损失: {detector_losses}")
        
        # 最后评估攻击效果
        if verbose:
            with torch.no_grad():
                final_confidences = {}
                for detector_name, (detector_model, detector_type) in self.detector_models_dict.items():
                    detector_model.eval()
                    
                    # 预处理最终的对抗图像
                    processed_final_adv = self._preprocess_image(adv_image.detach(), detector_model, detector_type)
                    
                    # 前向传播
                    if hasattr(detector_model, 'net'):  # RetinaFace
                        final_outputs = detector_model.net(processed_final_adv)
                    elif hasattr(detector_model, 'model'):  # DSFD
                        final_outputs = detector_model.model(processed_final_adv)
                    else:  # S3FD
                        final_outputs = detector_model(processed_final_adv)
                        
                    # 提取置信度
                    final_face_confs = self._extract_face_confidences(final_outputs, detector_type)
                    if final_face_confs.numel() > 0:
                        final_confidences[detector_name] = final_face_confs.mean().item()
                    else:
                        final_confidences[detector_name] = 0.0
                
                print(f"攻击前置信度: {orig_confidences_dict}")
                print(f"攻击后置信度: {final_confidences}")
                print(f"置信度下降: {', '.join([f'{k}: {orig_confidences_dict.get(k, 0) - v:.4f}' for k, v in final_confidences.items()])}")
                print(f"水印扰动L∞范数: {(adv_image - image_0_to_1).abs().max().item():.6f}")
        
        # 移除所有钩子
        self.remove_hooks()
        
        return adv_image.detach()

    def _attack_s3fd_specific(self, adv_image_0_to_1, image_0_to_1, detector):
        """针对S3FD的专门处理，直接针对conv3_3_raw_conf头部进行攻击"""
        try:
            import S3FDDetector as sfd_detector_module
            
            if not hasattr(sfd_detector_module, 'get_features'):
                print("未找到S3FD的get_features函数，无法使用专用攻击")
                return False
                
            # 处理当前对抗样本
            processed_adv_image = self._preprocess_image(adv_image_0_to_1, detector, "s3fd")
            
            # 提取特征，包括conv3_3_raw_conf
            try:
                _, _, conv3_3_raw_conf = sfd_detector_module.get_features(detector, processed_adv_image)
                
                if conv3_3_raw_conf is None or conv3_3_raw_conf.numel() == 0:
                    print("无法获取conv3_3_raw_conf，无法使用专用攻击")
                    return False
                    
                # 确保requires_grad
                if not conv3_3_raw_conf.requires_grad:
                    conv3_3_raw_conf = conv3_3_raw_conf.clone().requires_grad_(True)
                
                # 分离人脸和背景的logits - 与S3FD结构对应
                face_logits_c3 = conv3_3_raw_conf[:, 3, :, :]      # 形状 [B, H, W] - 人脸通道
                bg_logits_c3_all = conv3_3_raw_conf[:, 0:3, :, :]  # 形状 [B, 3, H, W] - 多个背景通道
                max_bg_logits_c3, _ = torch.max(bg_logits_c3_all, dim=1)  # 取最大背景分数
                
                # 综合多种攻击方向
                # 1. 减少人脸分数
                face_loss = torch.mean(face_logits_c3)  # 最小化人脸分数
                
                # 2. 增加背景分数
                bg_loss = -torch.mean(max_bg_logits_c3)  # 最大化背景分数
                
                # 3. 增加差距
                diff_loss = -torch.mean(max_bg_logits_c3 - face_logits_c3)  # 最大化差距
                
                # 组合S3FD专用损失
                s3fd_loss = face_loss * 5.0 + bg_loss * 5.0 + diff_loss * 20.0
                
                # 反向传播
                s3fd_loss.backward()
                
                # 标记S3FD专用攻击已应用
                return True
                
            except Exception as e:
                print(f"S3FD专用攻击计算损失失败: {e}")
                return False
                
        except Exception as e:
            print(f"S3FD专用攻击失败: {e}")
            import traceback
            traceback.print_exc()
            return False


# It's good practice to also copy dependencies if they are small and stable
# For larger ones like init_detectors, test_watermark, prepare, direct import is fine if train_FOUND1.py is in path

def main():
    args_attack_settings = parse() # Load settings if train_FOUND1.parse is available
    device = torch.device('cuda' if args_attack_settings.global_settings.gpu else 'cpu')
    
    print("Initializing detectors...")
    # detectors_dict from train_FOUND1.py: {"name": (model_instance, "type_str")}
    detectors_dict = init_detectors(device)
    if not detectors_dict:
        print("Failed to initialize detectors. Exiting.")
        return

    print("Preparing data...")
    # Using prepare() from train_FOUND1.py
    # It returns: attack_dataloader, test_dataloader, attgan, attgan_args, ... (many items)
    # We only need test_dataloader for this script.
    try:
        data_components = prepare()
        test_dataloader = data_components[1] # test_dataloader is the second item
    except Exception as e:
        print(f"Error during data preparation using prepare(): {e}")
        print("Please ensure train_FOUND1.py and its dependencies are correctly set up.")
        return

    # 先获取一个数据样本，确定图像尺寸
    print("检查数据集图像尺寸...")
    sample_iter = iter(test_dataloader)
    sample_batch = next(sample_iter)
    sample_image = sample_batch[0][0]  # 获取第一个批次的第一张图像
    img_size = sample_image.shape[1]  # 假设是方形图像，高宽相同
    img_channels = sample_image.shape[0]
    print(f"数据集图像尺寸: {img_size}×{img_size}, 通道数: {img_channels}")

    # Attack parameters
    epsilon = 0.05  # L-infinity bound
    alpha = 0.02  # Step size - 进一步增大步长使攻击更有效
    iterations = 10  # 增加迭代次数，特别是对S3FD模型
    
    # 确保与三个检测器顺序一致的权重设置
    detector_names = list(detectors_dict.keys())  # 获取检测器名称列表，用于后面保持一致性
    
    # 基于经验，对不同检测器给予合适的权重
    lambda_weights_dict = {}
    for i, name in enumerate(detector_names):
        if "retinaface" in name.lower():
            lambda_weights_dict[name] = 0.2  # RetinaFace权重
        elif "s3fd" in name.lower():
            lambda_weights_dict[name] = 0.8  # 显著增加S3FD权重
        elif "dsfd" in name.lower():
            lambda_weights_dict[name] = 0.2  # 忽略DSFD权重，专注于攻击S3FD
        else:
            lambda_weights_dict[name] = 1.0 / len(detector_names)  # 未知检测器使用均等权重
    
    print(f"发现检测器: {detector_names}")
    print(f"攻击参数: epsilon={epsilon}, alpha={alpha}, iterations={iterations}")
    print(f"检测器权重: {lambda_weights_dict}")

    # 初始化检测器列表，使用原始检测器名称作为索引
    detector_models_dict = {} 
    for detector_name, (model, type_str) in detectors_dict.items():
        detector_models_dict[detector_name] = (model, type_str)

    # 初始化通用水印攻击器
    universal_attacker = UniversalPoisonAttack(
        detector_models_dict=detector_models_dict,  # 使用字典传递，保留原始名称
        epsilon=epsilon,
        alpha=alpha,
        iterations=iterations,
        momentum=0.5,
        lambda_weights=lambda_weights_dict,
        device=device
    )

    # 目标特征层字典 - 为每个检测器定义要攻击的特征层，保持原始检测器名称
    target_layers_dict = {}
    for detector_name, (detector_model, _) in detectors_dict.items():
        target_layers = get_target_layers_for_detector(detector_name, detector_model)
        if target_layers:
            target_layers_dict[detector_name] = target_layers
            print(f"检测器 '{detector_name}' 目标特征层: {target_layers}")
        else:
            print(f"警告: 检测器 '{detector_name}' 没有定义目标特征层")
            
    # 打印FacePoisonAttack使用的相同特征层配置，用于对比
    print("\n=== 对比: 原始FacePoisonAttack使用的特征层 ===")
    for detector_name, (detector_model, _) in detectors_dict.items():
        target_layers = get_target_layers_for_detector(detector_name, detector_model)
        if target_layers:
            print(f"检测器 '{detector_name}': {target_layers}")
    print("=== 对比结束 ===\n")

    all_run_results = {}
    universal_watermark = None
    batch_image_count = 0
    total_image_count = 0

    # 设置最大训练样本数
    max_training_samples = 128  # 修改为128张图像用于生成通用水印
    print(f"\n=== 第一阶段: 使用 {max_training_samples} 张图像生成通用水印 ===")
    print(f"特别关注S3FD攻击效果，更接近原始单独攻击方式")

    # 对部分数据集进行处理，生成通用水印
    for batch_idx, (images_batch_neg1_1, _, _) in enumerate(tqdm(test_dataloader, desc="Generating Universal Watermark")):
        if batch_image_count >= max_training_samples:
            break

        for img_idx_in_batch in range(images_batch_neg1_1.size(0)):
            if batch_image_count >= max_training_samples:
                break
            
            current_image_neg1_1 = images_batch_neg1_1[img_idx_in_batch:img_idx_in_batch+1].to(device)
            # 转换到[0,1]范围
            current_image_0_to_1 = (current_image_neg1_1 + 1) / 2.0
            
            # 如果是第一张图片，保存尺寸以初始化universal_watermark
            if universal_watermark is None:
                _, c, h, w = current_image_0_to_1.shape
                print(f"初始化通用水印，尺寸: {h}×{w}")
            
            # 每10张图片输出一次进度，避免过多输出
            if batch_image_count % 10 == 0:
                print(f"\n处理训练图像 {batch_image_count+1}/{max_training_samples}")
            
            # 执行通用水印攻击
            adv_image_0_to_1 = universal_attacker.attack(
                current_image_0_to_1,
                target_feature_layers_dict=target_layers_dict,
                verbose=(batch_image_count % 20 == 0)  # 每20张图片输出一次详细信息
            )
            
            # 计算水印扰动
            current_watermark = adv_image_0_to_1 - current_image_0_to_1
            
            # 更新通用水印 - 使用特殊策略以增强对每个检测器的干扰效果
            if universal_watermark is None:
                universal_watermark = current_watermark
            else:
                # 使用衰减平均，但增加新扰动的权重，加快学习速度
                decay = 0.8  # 使用更小的衰减因子
                universal_watermark = decay * universal_watermark + (1 - decay) * current_watermark
                
                # 应用高通滤波，保留高频成分以增强攻击效果，特别是对S3FD
                # 简单高通滤波: 从原始水印中减去其低频成分(使用平均池化获取)
                if batch_image_count % 5 == 0:  # 每5张图片进行一次锐化
                    low_freq = F.avg_pool2d(universal_watermark, kernel_size=3, stride=1, padding=1)
                    high_freq = universal_watermark - low_freq
                    universal_watermark = universal_watermark + 0.2 * high_freq  # 增强高频
                    universal_watermark = torch.clamp(universal_watermark, -epsilon, epsilon)  # 确保在epsilon范围内
            
            batch_image_count += 1
            
            # 每50张图像保存一次中间结果，避免意外中断
            if batch_image_count % 50 == 0:
                torch.save(universal_watermark, f'universal_watermark_checkpoint_{batch_image_count}.pt')
                print(f"保存水印检查点: universal_watermark_checkpoint_{batch_image_count}.pt")
                print(f"当前水印L∞范数: {universal_watermark.abs().max().item():.6f}")
                print(f"当前水印尺寸: {universal_watermark.shape}")
    
    if universal_watermark is None:
        print("无法生成通用水印，退出。")
        return
    
    # 检查通用水印的尺寸
    print(f"生成的通用水印尺寸: {universal_watermark.shape}")
    
    # 最后确保水印范数达到epsilon
    with torch.no_grad():
        watermark_max = universal_watermark.abs().max().item()
        if watermark_max < epsilon:
            scale_factor = epsilon / watermark_max
            universal_watermark = universal_watermark * scale_factor
            print(f"水印放大至 epsilon={epsilon}, 缩放系数: {scale_factor:.4f}")
        
        # 添加额外优化步骤：增强水印的高频成分，对S3FD更有效
        print("应用高频增强以提高对S3FD的攻击效果")
        kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], dtype=torch.float32, device=device).view(1, 1, 3, 3)
        kernel = kernel.repeat(3, 1, 1, 1)  # 复制到3个通道
        high_freq = F.conv2d(universal_watermark, kernel, padding=1, groups=3)
        universal_watermark = universal_watermark + 0.3 * torch.sign(high_freq) * epsilon * 0.2  # 增加锐度
        universal_watermark = torch.clamp(universal_watermark, -epsilon, epsilon)  # 确保在epsilon范围内
    
    # 保存通用水印
    torch.save(universal_watermark, 'universal_watermark_final.pt')
    print(f"通用水印已生成并保存至 universal_watermark_final.pt")
    print(f"水印L∞范数: {universal_watermark.abs().max().item():.6f}")
    print(f"水印尺寸: {universal_watermark.shape}")
    
    # 将通用水印转换到[-1,1]范围 (用于test_watermark)
    universal_watermark_neg1_1 = universal_watermark * 2.0  # [-0.05, 0.05] -> [-0.1, 0.1]
    
    print("\n=== 第二阶段: 评估通用水印在整个数据集上的效果 ===")
    
    # 确保水印尺寸与图像尺寸一致
    print("验证水印尺寸...")
    print(f"通用水印尺寸: {universal_watermark.shape}")
    if universal_watermark.shape[2:] != sample_image.shape[1:]:
        print("警告: 水印尺寸与图像尺寸不匹配！可能会导致测试失败。")
    
    try:
        # 准备随机噪声水印，用于比较
        random_watermark = torch.randn_like(universal_watermark) * epsilon
        random_watermark = torch.clamp(random_watermark, -epsilon, epsilon)
        random_watermark_neg1_1 = random_watermark * 2.0
        
        # 评估通用水印的效果
        print("1. 基线评估 - 不添加水印:")
        f1_base, conf_base, duq_base, box_change_base, _ = test_watermark(
            watermark=None,
            test_dataloader=test_dataloader,
            detector_models=[model for model, _ in detectors_dict.values()],
            device=device,
            num_test=50,  # 增加评估样本数量以获得更可靠的结果
            mode="no_watermark"  # 显式指定无水印模式
        )
        
        print("\n2. 随机噪声评估 (epsilon=0.05):")
        # 使用我们准备的随机噪声
        f1_random, conf_random, duq_random, box_random, _ = test_watermark(
            watermark=random_watermark_neg1_1.to(device),
            test_dataloader=test_dataloader,
            detector_models=[model for model, _ in detectors_dict.values()],
            device=device,
            num_test=50,
            mode="default"
        )
        
        print("\n3. 通用水印评估:")
        f1_univ, conf_univ, duq_univ, box_univ, detector_results = test_watermark(
            watermark=universal_watermark_neg1_1.to(device),
            test_dataloader=test_dataloader,
            detector_models=[model for model, _ in detectors_dict.values()],
                    device=device,
            num_test=50,
            mode="default"  # 默认模式，应用提供的水印
        )
    except Exception as e:
        print(f"评估过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 结果比较 ===")
    print(f"指标                基线(无水印)  随机噪声      通用水印")
    print(f"-----------------------------------------------------")
    print(f"F1分数:            {f1_base:.4f}      {f1_random:.4f}      {f1_univ:.4f}")
    print(f"置信度变化:        {conf_base:.4f}      {conf_random:.4f}      {conf_univ:.4f}")
    print(f"DUQ分数:           {duq_base:.4f}      {duq_random:.4f}      {duq_univ:.4f}")
    print(f"检测框数量变化(%): {box_change_base:.2f}%     {box_random:.2f}%     {box_univ:.2f}%")
    
    print("\n各检测器在通用水印下的表现:")
    for detector_name, results in detector_results.items():
        print(f"  {detector_name}: F1={results['f1']:.4f}, 置信度变化={results['confidence_change']:.4f}, 框数变化={results['box_change']:.2f}%")
    
    print("\n通用水印攻击完成!")

    # 单独测试对S3FD的攻击效果
    print("\n--- 单独评估各检测器性能 ---")
    for detector_name, (detector_model, _) in detectors_dict.items():
        print(f"\n评估通用水印对 {detector_name} 的影响:")
        f1, conf_ch, duq, box_ch, _ = test_watermark(
            watermark=universal_watermark_neg1_1.to(device),
            test_dataloader=test_dataloader,
            detector_models=[detector_model],  # 只评估单个检测器
            device=device,
            num_test=20,  # 使用较小样本进行快速测试
            mode="default"
        )
        print(f"单独{detector_name} - F1: {f1:.4f}, 置信度变化: {conf_ch:.4f}, DUQ: {duq:.4f}, 框数变化: {box_ch:.2f}%")

if __name__ == "__main__":
    # This script relies on functions (init_detectors, test_watermark, calculate_iou, prepare, parse)
    # and detector model definitions (RetinaFaceDetector, S3FDDetector, DSFDDetector)
    # being accessible from the environment where train_FOUND1.py runs.
    # If train_FOUND1.py is in the same directory or PYTHONPATH is set up, imports should work.
    main() 