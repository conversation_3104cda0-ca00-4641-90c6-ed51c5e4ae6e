#-*- coding:utf-8 -*-

from __future__ import division
from __future__ import absolute_import
from __future__ import print_function

import os
import time
import torch
import argparse
import torch.nn as nn
import torch.optim as optim
import torch.nn.init as init
import torch.utils.data as data
import numpy as np
from torch.autograd import Variable
import torch.backends.cudnn as cudnn
import torch.nn.functional as F

from data.config import cfg
from s3fd import build_s3fd
from layers.modules import MultiBoxLoss
from data.factory import dataset_factory, detection_collate

# 功能：训练一个容易受到特征攻击的S3FD人脸检测器

def str2bool(v):
    return v.lower() in ("yes", "true", "t", "1")

parser = argparse.ArgumentParser(
    description='S3FD Vulnerable Face Detector Training')
train_set = parser.add_mutually_exclusive_group()
parser.add_argument('--dataset',
                    default='face',
                    choices=['hand', 'face', 'head'],
                    help='Train target')
parser.add_argument('--basenet',
                    default='vgg16_reducedfc.pth',
                    help='Pretrained base model')
parser.add_argument('--batch_size',
                    default=16, type=int,
                    help='Batch size for training')
parser.add_argument('--resume',
                    default=None, type=str,
                    help='Checkpoint state_dict file to resume training from')
parser.add_argument('--num_workers',
                    default=4, type=int,
                    help='Number of workers used in dataloading')
parser.add_argument('--cuda',
                    default=True, type=str2bool,
                    help='Use CUDA to train model')
parser.add_argument('--lr', '--learning-rate',
                    default=1e-3, type=float,
                    help='initial learning rate')
parser.add_argument('--momentum',
                    default=0.9, type=float,
                    help='Momentum value for optim')
parser.add_argument('--weight_decay',
                    default=5e-4, type=float,
                    help='Weight decay for SGD')
parser.add_argument('--gamma',
                    default=0.1, type=float,
                    help='Gamma update for SGD')
parser.add_argument('--multigpu',
                    default=False, type=str2bool,
                    help='Use mutil Gpu training')
parser.add_argument('--save_folder',
                    default='weights_vulnerable/',
                    help='Directory for saving checkpoint models')
parser.add_argument('--vulnerability_weight',
                    default=0.5, type=float,
                    help='Weight for vulnerability loss')
args = parser.parse_args()


if torch.cuda.is_available():
    if args.cuda:
        torch.set_default_tensor_type('torch.cuda.FloatTensor')
    if not args.cuda:
        print("WARNING: It looks like you have a CUDA device, but aren't " +
              "using CUDA.\nRun with --cuda for optimal training speed.")
        torch.set_default_tensor_type('torch.FloatTensor')
else:
    torch.set_default_tensor_type('torch.FloatTensor')

if not os.path.exists(args.save_folder):
    os.makedirs(args.save_folder)


train_dataset, val_dataset = dataset_factory(args.dataset)

# 设置随机数生成器
device = torch.device('cuda' if args.cuda else 'cpu')
g = torch.Generator(device=device)

train_loader = data.DataLoader(train_dataset, args.batch_size,
                              num_workers=args.num_workers,
                              shuffle=True,
                              generator=g,
                              collate_fn=detection_collate,
                              pin_memory=True)

val_batchsize = args.batch_size // 2
val_loader = data.DataLoader(val_dataset, val_batchsize,
                            num_workers=args.num_workers,
                            shuffle=False,
                            generator=g,
                            collate_fn=detection_collate,
                            pin_memory=True)

min_loss = np.inf
start_epoch = 0
s3fd_net = build_s3fd('train', cfg.NUM_CLASSES)
net = s3fd_net


if args.resume:
    print('Resuming training, loading {}...'.format(args.resume))
    start_epoch = net.load_weights(args.resume)

else:
    vgg_weights = torch.load(args.save_folder + args.basenet)
    print('Load base network....')
    net.vgg.load_state_dict(vgg_weights)

if args.cuda:
    if args.multigpu:
        net = torch.nn.DataParallel(s3fd_net)
    net = net.cuda()
    cudnn.benckmark = True

if not args.resume:
    print('Initializing weights...')
    s3fd_net.extras.apply(s3fd_net.weights_init)
    s3fd_net.loc.apply(s3fd_net.weights_init)
    s3fd_net.conf.apply(s3fd_net.weights_init)

optimizer = optim.SGD(net.parameters(), lr=args.lr, momentum=args.momentum,
                      weight_decay=args.weight_decay)
criterion = MultiBoxLoss(cfg, args.dataset)
print('Loading wider dataset...')
print('Using the specified args:')
print(args)


# 提取模型中间特征的钩子
features = {}
def get_features(name):
    def hook(model, input, output):
        features[name] = output
    return hook

# 注册钩子以捕获特征
def register_hooks(model):
    # 注册VGG的关键层
    # conv3_3 (在第一个L2Norm之前)
    model.vgg[15].register_forward_hook(get_features('conv3_3'))
    # conv4_3 (在第二个L2Norm之前)
    model.vgg[22].register_forward_hook(get_features('conv4_3'))
    # conv5_3 (在第三个L2Norm之前)
    model.vgg[29].register_forward_hook(get_features('conv5_3'))
    # 最后的VGG特征
    model.vgg[-1].register_forward_hook(get_features('vgg_last'))
    # 注册额外层
    if len(model.extras) >= 2:
        model.extras[1].register_forward_hook(get_features('extra1'))
    if len(model.extras) >= 4:
        model.extras[3].register_forward_hook(get_features('extra2'))

# 计算特征脆弱性损失 - 让特征层更容易被攻击
def compute_vulnerability_loss(features_dict):
    vulnerability_loss = 0.0
    count = 0
    for name, feature in features_dict.items():
        # 跳过任何包含 NaN 的特征
        if torch.isnan(feature).any():
            continue
            
        # 增加特征的不稳定性
        epsilon = 1e-5
        # 归一化特征
        feature = F.normalize(feature.view(feature.size(0), -1), dim=1).view_as(feature)
        
        # 计算特征的局部一致性，并鼓励不一致
        if feature.dim() == 4:  # 确保是4D张量 [B,C,H,W]
            # 创建一个平均池化核心
            avg_pool = F.avg_pool2d(feature, kernel_size=3, stride=1, padding=1)
            # 计算局部一致性损失 - 鼓励特征与其局部区域不同
            consistency_loss = F.cosine_similarity(feature.view(feature.size(0), -1), 
                                                 avg_pool.view(avg_pool.size(0), -1), 
                                                 dim=1).mean()
            # 我们希望特征与其局部区域差异大，所以直接使用相似度作为损失
            vulnerability_loss += consistency_loss
            count += 1
    
    return vulnerability_loss / max(count, 1)  # 避免除以0


def train():
    # 注册特征提取钩子
    register_hooks(s3fd_net)
    
    step_index = 0
    iteration = 0
    net.train()
    
    # 降低初始学习率
    for param_group in optimizer.param_groups:
        param_group['lr'] = args.lr * 0.1
    
    for epoch in range(start_epoch, cfg.EPOCHES):
        losses = 0
        for batch_idx, (images, targets) in enumerate(train_loader):
            if args.cuda:
                images = Variable(images.cuda())
                targets = [Variable(ann.cuda())
                           for ann in targets]
            else:
                images = Variable(images)
                targets = [Variable(ann) for ann in targets]

            if iteration in cfg.LR_STEPS:
                step_index += 1
                adjust_learning_rate(optimizer, args.gamma, step_index)

            # 添加扰动生成第二批图像，用于计算脆弱性损失
            with torch.no_grad():
                perturbed_images = images.clone()
                # 减小噪声强度
                noise = torch.randn_like(perturbed_images) * 0.01
                perturbed_images = torch.clamp(perturbed_images + noise, 0, 1)

            t0 = time.time()
            
            # 前向传播原始图像
            features.clear()  # 清除之前的特征
            out = net(images)
            original_features = features.copy()  # 保存原始特征
            
            # 前向传播扰动后的图像
            features.clear()
            _ = net(perturbed_images)
            perturbed_features = features.copy()  # 保存扰动后的特征
            
            # 常规检测损失
            loc_data, conf_data, priors = out  # 解包网络输出
            loss_l, loss_c = criterion((loc_data, conf_data), priors, targets)
            detection_loss = loss_l + loss_c
            
            # 计算脆弱性损失 - 促使特征对扰动敏感
            vulnerability_loss = 0
            feature_count = 0
            for name in original_features.keys():
                if name in perturbed_features:
                    # 跳过包含 NaN 的特征
                    if torch.isnan(original_features[name]).any() or torch.isnan(perturbed_features[name]).any():
                        continue
                    # 归一化特征
                    orig_feat = F.normalize(original_features[name].view(original_features[name].size(0), -1), dim=1)
                    pert_feat = F.normalize(perturbed_features[name].view(perturbed_features[name].size(0), -1), dim=1)
                    # 计算余弦相似度作为稳定性指标
                    similarity = F.cosine_similarity(orig_feat, pert_feat, dim=1).mean()
                    # 直接使用相似度作为损失 - 高相似度意味着模型对扰动不敏感
                    vulnerability_loss += similarity
                    feature_count += 1
            
            if feature_count > 0:
                vulnerability_loss = vulnerability_loss / feature_count
            
            # 再增加单独的脆弱性损失
            direct_vulnerability_loss = compute_vulnerability_loss(original_features)
            
            # 合并损失，使用更大的权重来增加脆弱性
            total_vulnerability_loss = (vulnerability_loss + direct_vulnerability_loss) 
            loss = detection_loss + args.vulnerability_weight * total_vulnerability_loss
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            
            # 添加梯度裁剪
            torch.nn.utils.clip_grad_norm_(net.parameters(), max_norm=10.0)
            
            optimizer.step()
            t1 = time.time()
            losses += detection_loss.data.item()

            if iteration % 10 == 0:
                tloss = losses / (batch_idx + 1)
                print('Timer: %.4f' % (t1 - t0))
                print('epoch:' + repr(epoch) + ' || iter:' +
                      repr(iteration) + ' || Loss:%.4f' % (tloss))
                print('->> conf loss:{:.4f} || loc loss:{:.4f}'.format(
                    loss_c.data.item(), loss_l.data.item()))
                print('->> vulnerability loss:{:.4f}'.format(
                    total_vulnerability_loss.data.item()))
                print('->>lr:{:.6f}'.format(optimizer.param_groups[0]['lr']))

            if iteration != 0 and iteration % 5000 == 0:
                print('Saving state, iter:', iteration)
                file = 'sfd_vulnerable_' + args.dataset + '_' + repr(iteration) + '.pth'
                torch.save(s3fd_net.state_dict(),
                           os.path.join(args.save_folder, file))
            iteration += 1

        val(epoch)
        if iteration == cfg.MAX_STEPS:
            break


def val(epoch):
    net.eval()
    loc_loss = 0
    conf_loss = 0
    step = 0
    t1 = time.time()
    for batch_idx, (images, targets) in enumerate(val_loader):
        if args.cuda:
            images = Variable(images.cuda())
            targets = [Variable(ann.cuda(), volatile=True)
                       for ann in targets]
        else:
            images = Variable(images)
            targets = [Variable(ann, volatile=True) for ann in targets]

        out = net(images)
        loc_data, conf_data, priors = out  # 解包网络输出
        loss_l, loss_c = criterion((loc_data, conf_data), priors, targets)
        loss = loss_l + loss_c
        loc_loss += loss_l.data.item()
        conf_loss += loss_c.data.item()
        step += 1

    tloss = (loc_loss + conf_loss) / step
    t2 = time.time()
    print('Timer: %.4f' % (t2 - t1))
    print('test epoch:' + repr(epoch) + ' || Loss:%.4f' % (tloss))

    global min_loss
    if tloss < min_loss:
        print('Saving best state,epoch', epoch)
        file = 'sfd_vulnerable_{}.pth'.format(args.dataset)
        torch.save(s3fd_net.state_dict(), os.path.join(
            args.save_folder, file))
        min_loss = tloss

    states = {
        'epoch': epoch,
        'weight': s3fd_net.state_dict(),
    }
    file = 'sfd_vulnerable_{}_checkpoint.pth'.format(args.dataset)
    torch.save(states, os.path.join(
        args.save_folder, file))


def adjust_learning_rate(optimizer, gamma, step):
    """Sets the learning rate to the initial LR decayed by 10 at every
        specified step
    # Adapted from PyTorch Imagenet example:
    # https://github.com/pytorch/examples/blob/master/imagenet/main.py
    """
    lr = args.lr * (gamma ** (step))
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr


if __name__ == '__main__':
    train() 