#-*- coding:utf-8 -*-

from __future__ import division
from __future__ import absolute_import
from __future__ import print_function


from .egohand import HandDetection
from .widerface import WIDERDetection
from .vochead import VOCDetection, VOCAnnotationTransform
from .config import cfg

import torch
import numpy as np


def dataset_factory(dataset):
    if dataset == 'face':
        train_dataset = WIDERDetection(cfg.FACE.TRAIN_FILE, mode='train')
        val_dataset = WIDERDetection(cfg.FACE.VAL_FILE, mode='val')
    if dataset == 'hand':
        train_dataset = WIDERDetection(cfg.HAND.TRAIN_FILE, mode='train')
        val_dataset = WIDERDetection(cfg.HAND.VAL_FILE, mode='val')
    if dataset == 'head':
        train_dataset = VOCDetection(cfg.HEAD.DIR, image_sets=[
                                     ('PartA', 'trainval'), ('PartB', 'trainval')],
                                     target_transform=VOCAnnotationTransform(),
                                     mode='train',
                                     dataset_name='VOCPartAB')
        val_dataset = VOCDetection(cfg.HEAD.DIR, image_sets=[('PartA', 'test'), ('PartB', 'test')],
                                   target_transform=VOCAnnotationTransform(),
                                   mode='test',
                                   dataset_name='VOCPartAB')
    return train_dataset, val_dataset


def detection_collate(batch):
    """Custom collate fn for dealing with batches of images that have a different
    number of associated object annotations (bounding boxes).

    Arguments:
        batch: (tuple) A tuple of tensor images and lists of annotations

    Return:
        A tuple containing:
            1) (tensor) batch of images stacked on their 0 dim
            2) (list of tensors) annotations for a given image are stacked on
                                 0 dim
    """
    targets = []
    imgs = []
    for sample in batch:
        if len(sample) != 2:
            continue
        
        imgs.append(sample[0])
        try:
            # 确保sample[1]是可以转换为张量的格式
            if isinstance(sample[1], (list, np.ndarray)) and len(sample[1]) > 0:
                if isinstance(sample[1], list):
                    targets.append(torch.FloatTensor(sample[1]))
                else:  # numpy array
                    targets.append(torch.FloatTensor(sample[1]))
            else:
                # 如果不是有效的格式，创建一个默认的目标框
                targets.append(torch.FloatTensor([[0.1, 0.1, 0.2, 0.2, 1.0]]))
        except Exception as e:
            print(f"Error in collate: {e}, sample[1] shape: {getattr(sample[1], 'shape', None)}")
            # 创建一个默认的目标框
            targets.append(torch.FloatTensor([[0.1, 0.1, 0.2, 0.2, 1.0]]))
    
    if len(imgs) == 0:
        return torch.empty(0), []
        
    return torch.stack(imgs, 0), targets
