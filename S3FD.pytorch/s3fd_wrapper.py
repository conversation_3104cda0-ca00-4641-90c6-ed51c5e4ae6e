import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.nn.init as init
import numpy as np
import os
import sys
from pathlib import Path
from collections import OrderedDict

# ===== 内部实现S3FD需要的组件 =====
class L2Norm(nn.Module):
    def __init__(self, n_channels, scale=1.0):
        super(L2Norm, self).__init__()
        self.n_channels = n_channels
        self.scale = scale
        self.eps = 1e-10
        self.weight = nn.Parameter(torch.Tensor(self.n_channels))
        self.reset_parameters()
        
    def reset_parameters(self):
        init.constant_(self.weight, self.scale)
        
    def forward(self, x):
        norm = x.pow(2).sum(dim=1, keepdim=True).sqrt() + self.eps
        x = x / norm
        out = self.weight.unsqueeze(0).unsqueeze(2).unsqueeze(3) * x
        return out

class PriorBox(object):
    def __init__(self, input_size, feature_maps, cfg):
        super(PriorBox, self).__init__()
        self.imh = input_size[0]
        self.imw = input_size[1]
        
        # number of priors for feature map location (either 4 or 6)
        self.variance = cfg.get('VARIANCE', [0.1, 0.2])
        self.feature_maps = feature_maps
        self.min_sizes = cfg.get('ANCHOR_SIZES', [16, 32, 64, 128, 256, 512])
        self.steps = cfg.get('STEPS', [4, 8, 16, 32, 64, 128])
        self.clip = cfg.get('CLIP', False)
        
    def forward(self):
        mean = []
        for k, f in enumerate(self.feature_maps):
            min_size = self.min_sizes[k]
            for i, j in product(range(f[0]), range(f[1])):
                # compute centers
                cx = (j + 0.5) * self.steps[k] / self.imw
                cy = (i + 0.5) * self.steps[k] / self.imh
                
                # aspect_ratio: 1
                # rel size: min_size
                s_k = min_size / self.imw
                mean += [cx, cy, s_k, s_k]
                
        # back to torch land
        output = torch.Tensor(mean).view(-1, 4)
        if self.clip:
            output.clamp_(max=1, min=0)
        return output

# 从itertools导入product函数，用于PriorBox实现
from itertools import product

class Detect(object):
    """S3FD的后处理，用于测试阶段"""
    @staticmethod
    def apply(loc_data, conf_data, prior_data, cfg):
        num_batch = loc_data.size(0)
        num_priors = prior_data.size(0)
        conf_preds = conf_data.view(num_batch, num_priors, 2).transpose(2, 1)
        batch_priors = prior_data.view(-1, num_priors, 4).expand(num_batch, num_priors, 4)
        batch_priors = batch_priors.contiguous().view(-1, 4)
        
        batch_scores = conf_preds.contiguous().view(-1, 2)
        
        default_nms_threshold = 0.3
        default_conf_threshold = 0.05
        default_top_k = 750
        
        nms_threshold = cfg.get('NMS_THRESH', default_nms_threshold)
        conf_threshold = cfg.get('CONF_THRESH', default_conf_threshold)
        top_k = cfg.get('TOP_K', default_top_k)
        
        # 解码预测的位置
        decoded_boxes = decode_boxes(loc_data, batch_priors, cfg['VARIANCE'])
        output = torch.zeros(num_batch, top_k, 5)
        
        # 应用NMS
        for i in range(num_batch):
            boxes = decoded_boxes[i]
            scores = batch_scores[i][:, 1]
            
            # 根据置信度过滤
            mask = scores > conf_threshold
            boxes = boxes[mask]
            scores = scores[mask]
            
            if len(scores) > 0:
                # 应用NMS
                keep = boxes_nms(boxes, scores, nms_threshold, top_k)
                boxes = boxes[keep]
                scores = scores[keep]
                
                # 组合结果
                result = torch.cat((scores.unsqueeze(1), boxes), 1)
                num_keep = min(keep.size(0), top_k)
                output[i, :num_keep] = result[:num_keep]
        
        return output

def decode_boxes(loc, priors, variances):
    """解码边界框预测"""
    boxes = torch.cat((
        priors[:, :2] + loc[:, :2] * variances[0] * priors[:, 2:],
        priors[:, 2:] * torch.exp(loc[:, 2:] * variances[1])), 1)
    
    # 转换为 x_min, y_min, x_max, y_max
    boxes[:, :2] -= boxes[:, 2:] / 2
    boxes[:, 2:] += boxes[:, :2]
    
    return boxes

def boxes_nms(boxes, scores, threshold=0.3, top_k=200):
    """非极大值抑制实现"""
    if boxes.numel() == 0:
        return torch.LongTensor()
    keep = torch.ones(scores.size(0)).long()
    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]
    area = torch.mul(x2 - x1, y2 - y1)
    v, idx = scores.sort(0)
    idx = idx[-top_k:]
    xx1 = boxes.new()
    yy1 = boxes.new()
    xx2 = boxes.new()
    yy2 = boxes.new()
    w = boxes.new()
    h = boxes.new()
    
    count = 0
    while idx.numel() > 0:
        i = idx[-1]
        keep[count] = i
        count += 1
        if idx.size(0) == 1:
            break
        idx = idx[:-1]
        
        # 计算重叠区域
        torch.index_select(x1, 0, idx, out=xx1)
        torch.index_select(y1, 0, idx, out=yy1)
        torch.index_select(x2, 0, idx, out=xx2)
        torch.index_select(y2, 0, idx, out=yy2)
        xx1 = torch.clamp(xx1, min=x1[i])
        yy1 = torch.clamp(yy1, min=y1[i])
        xx2 = torch.clamp(xx2, max=x2[i])
        yy2 = torch.clamp(yy2, max=y2[i])
        w.resize_as_(xx2)
        h.resize_as_(yy2)
        w = xx2 - xx1
        h = yy2 - yy1
        w = torch.clamp(w, min=0.0)
        h = torch.clamp(h, min=0.0)
        inter = w * h
        
        # 计算IoU
        rem_areas = torch.index_select(area, 0, idx)
        union = (rem_areas - inter) + area[i]
        IoU = inter/union
        idx = idx[IoU <= threshold]
    
    return keep[:count]

# ===== S3FD模型定义 =====
class S3FD(nn.Module):
    """Single Shot Multibox Architecture for Face Detection"""
    
    def __init__(self, phase, base, extras, head, num_classes=2):
        super(S3FD, self).__init__()
        self.phase = phase
        self.num_classes = num_classes
        
        # SSD网络
        self.vgg = nn.ModuleList(base)
        # Layer learns to scale the l2 normalized features from conv4_3
        self.L2Norm3_3 = L2Norm(256, 10)
        self.L2Norm4_3 = L2Norm(512, 8)
        self.L2Norm5_3 = L2Norm(512, 5)

        self.extras = nn.ModuleList(extras)
        self.loc = nn.ModuleList(head[0])
        self.conf = nn.ModuleList(head[1])
        
        if phase == 'test':
            self.softmax = nn.Softmax(dim=-1)
            self.detect = Detect
    
    def forward(self, x):
        size = x.size()[2:]
        sources = list()
        loc = list()
        conf = list()

        # 应用VGG直到conv4_3 relu
        for k in range(16):
            x = self.vgg[k](x)

        s = self.L2Norm3_3(x)
        sources.append(s)

        # 应用VGG直到fc7
        for k in range(16, 23):
            x = self.vgg[k](x)

        s = self.L2Norm4_3(x)
        sources.append(s)

        for k in range(23, 30):
            x = self.vgg[k](x)

        s = self.L2Norm5_3(x)
        sources.append(s)

        for k in range(30, len(self.vgg)):
            x = self.vgg[k](x)
        sources.append(x)

        # 应用额外层并缓存源层输出
        for k, v in enumerate(self.extras):
            x = F.relu(v(x), inplace=True)
            if k % 2 == 1:
                sources.append(x)

        # 应用multibox head到源层
        loc_x = self.loc[0](sources[0])
        conf_x = self.conf[0](sources[0])

        max_conf, _ = torch.max(conf_x[:, 0:3, :, :], dim=1, keepdim=True)
        conf_x = torch.cat((max_conf, conf_x[:, 3:, :, :]), dim=1)

        loc.append(loc_x.permute(0, 2, 3, 1).contiguous())
        conf.append(conf_x.permute(0, 2, 3, 1).contiguous())

        for i in range(1, len(sources)):
            x = sources[i]
            conf.append(self.conf[i](x).permute(0, 2, 3, 1).contiguous())
            loc.append(self.loc[i](x).permute(0, 2, 3, 1).contiguous())

        # 计算特征图
        features_maps = []
        for i in range(len(loc)):
            feat = []
            feat += [loc[i].size(1), loc[i].size(2)]
            features_maps += [feat]
            
        # 创建默认框
        self.cfg = {
            'FEATURE_MAPS': [160, 80, 40, 20, 10, 5],
            'STEPS': [4, 8, 16, 32, 64, 128],
            'ANCHOR_SIZES': [16, 32, 64, 128, 256, 512],
            'VARIANCE': [0.1, 0.2],
            'CLIP': False,
            'NMS_THRESH': 0.3,
            'CONF_THRESH': 0.05,
            'TOP_K': 750
        }
        self.priorbox = PriorBox(size, features_maps, self.cfg)
        self.priors = torch.autograd.Variable(self.priorbox.forward(), volatile=True)

        # 整合输出
        loc = torch.cat([o.view(o.size(0), -1) for o in loc], 1)
        conf = torch.cat([o.view(o.size(0), -1) for o in conf], 1)

        if self.phase == 'test':
            output = self.detect.apply(
                loc.data,
                self.softmax(conf.view(conf.size(0), -1, self.num_classes)).data,
                self.priors.data,
                self.cfg
            )
        else:
            output = (
                loc.view(loc.size(0), -1, 4),
                conf.view(conf.size(0), -1, self.num_classes),
                self.priors
            )
        return output
    
    def load_weights(self, base_file):
        other, ext = os.path.splitext(base_file)
        if ext == '.pkl' or ext == '.pth':
            print('Loading weights into state dict...')
            try:
                mdata = torch.load(base_file, map_location=lambda storage, loc: storage)
                weights = mdata.get('weight', mdata) # 支持不同格式
                epoch = mdata.get('epoch', 0)
                self.load_state_dict(weights, strict=False)
                print('Finished!')
            except Exception as e:
                print(f"加载权重时出错: {e}")
                import traceback
                traceback.print_exc()
        else:
            print('Sorry only .pth and .pkl files supported.')
        return 0

# ===== S3FD构建函数 =====
def vgg(cfg, i, batch_norm=False):
    layers = []
    in_channels = i
    for v in cfg:
        if v == 'M':
            layers += [nn.MaxPool2d(kernel_size=2, stride=2)]
        elif v == 'C':
            layers += [nn.MaxPool2d(kernel_size=2, stride=2, ceil_mode=True)]
        else:
            conv2d = nn.Conv2d(in_channels, v, kernel_size=3, padding=1)
            if batch_norm:
                layers += [conv2d, nn.BatchNorm2d(v), nn.ReLU(inplace=True)]
            else:
                layers += [conv2d, nn.ReLU(inplace=True)]
            in_channels = v
    return layers

def add_extras(cfg, i, batch_norm=False):
    # Extra layers added to VGG for feature scaling
    layers = []
    in_channels = i
    flag = False
    for k, v in enumerate(cfg):
        if in_channels != 'S':
            if v == 'S':
                layers += [nn.Conv2d(in_channels, cfg[k + 1],
                                     kernel_size=(1, 3)[flag], stride=2, padding=1)]
            else:
                layers += [nn.Conv2d(in_channels, v, kernel_size=(1, 3)[flag], padding=0)]
            flag = not flag
        in_channels = v
    return layers

def multibox(vgg, extra_layers, num_classes):
    loc_layers = []
    conf_layers = []
    vgg_source = [21, 28, 35]
    
    loc_layers += [nn.Conv2d(256, 4, kernel_size=3, padding=1)]
    conf_layers += [nn.Conv2d(256, 4, kernel_size=3, padding=1)]
    
    for k, v in enumerate(vgg_source):
        loc_layers += [nn.Conv2d(vgg[v].out_channels, 4, kernel_size=3, padding=1)]
        conf_layers += [nn.Conv2d(vgg[v].out_channels, num_classes, kernel_size=3, padding=1)]
    
    for k, v in enumerate(extra_layers[1::2], 2):
        loc_layers += [nn.Conv2d(v.out_channels, 4, kernel_size=3, padding=1)]
        conf_layers += [nn.Conv2d(v.out_channels, num_classes, kernel_size=3, padding=1)]
        
    return loc_layers, conf_layers

def build_s3fd(phase, num_classes=2):
    """构建S3FD模型
    
    Args:
        phase: 'test' 或 'train'
        num_classes: 类别数量
        
    Returns:
        S3FD模型实例
    """
    # VGG配置
    vgg_cfg = [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 'C', 512, 512, 512, 'M',
               512, 512, 512, 'M']
    
    # 额外层配置
    extras_cfg = [256, 'S', 512, 128, 'S', 256]
    
    # 构建基础网络
    base = vgg(vgg_cfg, 3)
    
    # 添加额外层
    extras = add_extras(extras_cfg, 1024)
    
    # 创建多框头部
    mbox = multibox(base, extras, num_classes)
    
    # 创建模型
    model = S3FD(phase, base, extras, mbox, num_classes)
    
    return model

def init_s3fd_detector(device='cuda'):
    """
    初始化S3FD人脸检测器
    
    Args:
        device: 计算设备
        
    Returns:
        S3FD检测器实例
    """
    try:
        # 构建S3FD模型
        net = build_s3fd('test')
        net.eval()
        
        # 加载预训练权重
        weights_path = os.path.join(os.path.dirname(__file__), 'weights', 'sfd_face.pth')
        if not os.path.exists(weights_path):
            raise FileNotFoundError(f"找不到S3FD预训练权重文件: {weights_path}")
            
        # 加载权重
        net.load_weights(weights_path)
        
        # 移动到指定设备
        net = net.to(device)
        
        print("成功加载S3FD模型和权重")
        return net
        
    except Exception as e:
        print(f"初始化S3FD检测器时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_features(model, x):
    """
    从S3FD模型中提取特征
    
    Args:
        model: S3FD模型实例
        x: 输入图像张量 [B, 3, H, W]
        
    Returns:
        features: 包含多层特征的列表
    """
    features = []
    
    # 应用VGG基础网络并收集特征
    for k in range(16):
        x = model.vgg[k](x)
    features.append(model.L2Norm3_3(x))  # conv3_3特征
    
    for k in range(16, 23):
        x = model.vgg[k](x)
    features.append(model.L2Norm4_3(x))  # conv4_3特征
    
    for k in range(23, 30):
        x = model.vgg[k](x)
    features.append(model.L2Norm5_3(x))  # conv5_3特征
    
    for k in range(30, len(model.vgg)):
        x = model.vgg[k](x)
    features.append(x)  # conv_fc7特征
    
    # 应用额外层并收集特征
    for k, v in enumerate(model.extras):
        x = torch.relu(v(x), inplace=True)
        if k % 2 == 1:
            features.append(x)
            
    return features 