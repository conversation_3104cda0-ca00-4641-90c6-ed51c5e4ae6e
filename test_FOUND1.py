import argparse
import copy
import json
import os
from os.path import join
import numpy as np
from tqdm import tqdm
from random import shuffle
import torch
import get_output
from model_data_prepare import prepare
from evaluate_fid import evaluate_multiple_models
from attgan.data import check_attribute_conflict
import torch.nn.functional as TFunction
import torch.nn.functional as f
from skimage.metrics import structural_similarity as ssim
from torchvision.models.detection import FasterRCNN_ResNet50_FPN_Weights, SSD300_VGG16_Weights, RetinaNet_ResNet50_FPN_Weights
import torchvision.models as models
import torch.nn as nn

# 设置 GPU 设备
os.environ['CUDA_VISIBLE_DEVICES'] = '7'

# 设置攻击策略的开关
do_end_to_end = True
do_feature_ensemble = True
do_grad_ensemble = True

do_output_ensemble = False
do_loss_ensemble = False

batch_evaluate = True


def parse(args=None):
    with open(join('./setting.json'), 'r') as f:
        args_attack = json.load(f, object_hook=lambda d: argparse.Namespace(**d))
    return args_attack


# Init the attacker
def init_get_outputs(args_attack):
    get_output_models = get_output.get_all_features(model=None, device=torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
                                                    epsilon=args_attack.attacks.epsilon, args=args_attack.attacks)
    return get_output_models


def just_mean(d_grads):
    d_grads = torch.stack([d for d in d_grads])
    return torch.mean(d_grads, dim=0)

def denorm(x):
    """Convert the range from [-1, 1] to [0, 1]."""
    out = (x + 1) / 2
    return out.clamp_(0, 1)

def gradient_alignment(d_grads):
    """
    梯度对齐策略：通过计算余弦相似度调整不同模型的梯度方向，
    使得聚合后的通用梯度具有更一致的方向。
    """
    # 计算所有梯度的平均值
    mean_grad = torch.mean(torch.stack(d_grads), dim=0)

    # 对齐每个梯度，使其与平均梯度方向更加一致
    aligned_grads = []
    for grad in d_grads:
        # 计算余弦相似度
        cos_sim = torch.dot(grad.view(-1), mean_grad.view(-1)) / (torch.norm(grad) * torch.norm(mean_grad) + 1e-12)
        # 根据余弦相似度调整梯度
        aligned_grad = cos_sim * grad
        aligned_grads.append(aligned_grad)

    # 对齐后的梯度求平均
    final_grad = torch.mean(torch.stack(aligned_grads), dim=0)
    return final_grad


def kl_divergence(p, q):
    """
    计算KL散度
    p, q: 输入张量，需要确保它们是概率分布（和为1的非负值）
    """
    # 将特征图转换为概率分布
    p = TFunction.softmax(p.view(p.size(0), -1), dim=1)
    q = TFunction.softmax(q.view(q.size(0), -1), dim=1)
    
    # 添加小值防止log(0)
    epsilon = 1e-10
    p = torch.clamp(p, min=epsilon)
    q = torch.clamp(q, min=epsilon)
    
    # 计算KL散度: KL(p||q) = Σ p(x) * log(p(x)/q(x))
    kl_div = torch.sum(p * torch.log(p/q), dim=1).mean()
    return kl_div


def perform_AttGAN(attack, input_imgs, original_imgs, attibutes, attgan_model, attgan_parse, rand_param):
    att_b_list = [attibutes]
    # # No need to attack all attributes
    # for i in range(attgan_parse.n_attrs):
    #     tmp = attibutes.clone()
    #     tmp[:, i] = 1 - tmp[:, i]
    #     tmp = check_attribute_conflict(tmp, attgan_parse.attrs[i], attgan_parse.attrs)
    #     att_b_list.append(tmp)
    # att_b_list = [att_b_list[0]]
    
    for i, att_b in enumerate(att_b_list):
        att_b_ = (att_b * 2 - 1) * attgan_parse.thres_int
        if i > 0:
            att_b_[..., i - 1] = att_b_[..., i - 1] * attgan_parse.test_int / attgan_parse.thres_int
        with torch.no_grad():
            gen_noattack, no_attack_middle = attgan_model.G(original_imgs, att_b_)
        adv_gen, adv_gen_middle = attack.get_attgan_features(input_imgs, att_b_, attgan_model, rand_param)

    return [gen_noattack, adv_gen], [no_attack_middle[-1], adv_gen_middle[-1]]


def perform_HiSD(attack, input_imgs, original_imgs, reference_img, E_model, F_model, T_model, G_model, EFTG_models, rand_param):
    with torch.no_grad():
        # get the original deepfake images
        c = E_model(original_imgs)
        c_trg = c
        s_trg = F_model(reference_img, 1)
        c_trg = T_model(c_trg, s_trg, 1)
        x_trg = G_model(c_trg)

    adv_x_trg, adv_c = attack.get_hisd_features(input_imgs.cuda(), reference_img, F_model, T_model, G_model, E_model, EFTG_models, rand_param)
    return [x_trg, adv_x_trg], [c, adv_c]


def select_model_to_get_feature_pairs(case, img, ori_imgs, reference, attribute_c, attribute_attgan, attack, stargan_s, atggan_s,
                                      attgan_s, attgan_args, EE, FF, TT, GG, g_models, reconstruct=128, attr_aug= False):
    if attr_aug:
        rand_q = np.random.rand()
    else:
        rand_q = 0
    if case == 0:
        # print('attacking stargan...')
        output_pair, middle_pair = stargan_s.perform_stargan(img, ori_imgs, attribute_c, attack, rand_q)
    elif case == 1:
        # print('attacking attentiongan...')
        output_pair, middle_pair = atggan_s.perform_attentiongan(img, ori_imgs, attribute_c, attack, rand_q)
    elif case == 2:
        # print('attacking AttGan...')
        output_pair, middle_pair = perform_AttGAN(attack, img, ori_imgs, attribute_attgan, attgan_s, attgan_args, rand_q)
    elif case == 3:
        # print('attacking HiSD...')
        output_pair, middle_pair = perform_HiSD(attack, img, ori_imgs, reference, EE, FF, TT, GG, g_models, rand_q)
    else:
        raise NotImplementedError('wrong code!')

    # resize feature outputs
    new_middle_pair = []
    new_output_pair = []
    for middle in middle_pair:
        new_middle = torch.nn.functional.interpolate(middle, (reconstruct, reconstruct), mode='bilinear')  # 8, 256, 128, 128
        new_middle_pair.append(new_middle)
    # for output in output_pair:
    #     new_output  = torch.nn.functional.interpolate(output, (reconstruct, reconstruct), mode='bilinear')
    #     new_output_pair.append(new_output)
    # return new_output_pair, new_middle_pair
    return output_pair, new_middle_pair


def DI(X_in):
    import torch.nn.functional as F

    rnd = np.random.randint(256, 290, size=1)[0]
    h_rem = 290 - rnd
    w_rem = 290 - rnd
    pad_top = np.random.randint(0, h_rem, size=1)[0]
    pad_bottom = h_rem - pad_top
    pad_left = np.random.randint(0, w_rem, size=1)[0]
    pad_right = w_rem - pad_left

    c = np.random.rand(1)
    if c <= 0.5:
        X_out = F.pad(F.interpolate(X_in, size=(rnd, rnd)), (pad_left, pad_right, pad_top, pad_bottom), mode='constant',
                      value=0)
        return F.interpolate(X_out, (256, 256))
    else:
        return F.interpolate(X_in, (256, 256))


class FacePoisonAttacker(nn.Module):
    def __init__(self, detector, layer_indices, attack_method='Ada-DIM++', epsilon=8/255, steps=10):
        super().__init__()
        self.detector = detector
        self.layer_indices = layer_indices
        self.attack_method = attack_method
        self.epsilon = epsilon
        self.steps = steps
        
    def compute_adversarial_loss(self, detections):
        total_loss = torch.tensor(0.0).cuda()
        batch_size = len(detections)
        
        if batch_size == 0:
            return total_loss
            
        for detection in detections:
            if not isinstance(detection, list):
                detection = [detection]
                
            for det in detection:
                if 'scores' not in det or 'boxes' not in det:
                    continue
                    
                scores = det['scores']
                boxes = det['boxes']
                
                if len(scores) == 0:
                    continue
                
                # Compute detection suppression loss
                suppress_loss = -torch.log(1 - scores + 1e-6).mean()
                
                # Add diversity loss if using multiple layers
                if len(self.layer_indices) > 1:
                    diversity_loss = self.compute_diversity_loss(boxes)
                    total_loss += suppress_loss + 0.5 * diversity_loss
                else:
                    total_loss += suppress_loss
                
        return total_loss / max(batch_size, 1)
    
    def compute_diversity_loss(self, boxes):
        if len(boxes) < 2:
            return torch.tensor(0.0).cuda()
            
        # Encourage diverse box locations
        box_centers = (boxes[:, :2] + boxes[:, 2:]) / 2
        pdist = torch.pdist(box_centers)
        diversity_loss = -torch.mean(pdist)
        
        return diversity_loss

def evaluate_duq_score_with_pgd(watermark, clean_images, detector_models, device, pgd_steps=100, pgd_alpha=0.05, pgd_eps=0.1):
    """
    在原有破坏水印基础上添加中毒功能 
    将中毒扰动主要添加在原始水印强度较小的区域
    """
    batch_size = clean_images.size(0)
    
    # 保存原始水印
    original_watermark = watermark.clone().detach()
    
    # 创建mask,标识原始水印较小的区域
    threshold = 0.04  # 设置阈值,判断原始水印的强度
    watermark_magnitude = torch.abs(original_watermark)
    poison_mask = (watermark_magnitude < threshold).float()
    
    # 初始扰动只在mask区域内
    delta = torch.randn_like(watermark) * 0.001 * poison_mask
    delta.requires_grad = True
    
    optimizer = torch.optim.AdamW([delta], lr=pgd_alpha, weight_decay=0.02)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=pgd_steps)
    
    best_delta = None
    best_loss = float('inf')
    adv_criterion = AdversarialLoss().to(device)
    
    for step in range(pgd_steps):
        optimizer.zero_grad()
        
        # 应用mask确保扰动主要在原始水印较小的区域
        masked_delta = delta * poison_mask
        current_watermark = watermark + masked_delta
        watermarked_images = clean_images + current_watermark
        watermarked_images = torch.clamp(watermarked_images, -1, 1)
        
        # 计算对抗损失
        total_adv_loss = torch.tensor(0.0, device=device)
        watermarked_detections = [model(watermarked_images) for model in detector_models]
        for detection in watermarked_detections:
            total_adv_loss += adv_criterion(detection)
        total_adv_loss = total_adv_loss / batch_size
        
        # 正则化损失只考虑mask区域
        regularization_weight = 0.1  # 可以适当降低权重,因为已经有mask限制
        regularization_loss = torch.nn.functional.mse_loss(
            current_watermark * poison_mask, 
            original_watermark * poison_mask
        )
        
        loss = total_adv_loss + regularization_weight * regularization_loss
        loss.backward()
        
        optimizer.step()
        scheduler.step()
        
        if loss.item() < best_loss:
            best_loss = loss.item()
            best_delta = masked_delta.clone().detach()
            
        with torch.no_grad():
            # 限制扰动范围,同时保持mask限制
            delta.data = torch.clamp(delta.data, -pgd_eps, pgd_eps) * poison_mask
    
    # 计算DUQ分数用于监控
    with torch.no_grad():
        clean_detections = [model(clean_images) for model in detector_models]
        watermarked_detections = [model(watermarked_images) for model in detector_models]
        
        clean_true_positives = 0
        watermarked_true_positives = 0
        
        for detection in clean_detections:
            for det in detection:
                if 'scores' in det:
                    clean_true_positives += torch.sum(det['scores'] > 0.5).float()
        
        for detection in watermarked_detections:
            for det in detection:
                if 'scores' in det:
                    watermarked_true_positives += torch.sum(det['scores'] > 0.5).float()
        
        if clean_true_positives > 0:
            total_duq = ((clean_true_positives - watermarked_true_positives) + 
                      torch.max(torch.tensor(0.0, device=device), 
                              watermarked_true_positives - clean_true_positives)) / clean_true_positives
        else:
            total_duq = torch.tensor(0.0 if watermarked_true_positives == 0 else 1.0, device=device)
        
        total_duq = total_duq / batch_size
    
    final_watermark = watermark + (best_delta if best_delta is not None else masked_delta.detach())
    print(f"输出水印维度: {final_watermark.shape}")
    print(f"中毒扰动区域占比: {poison_mask.mean().item():.3f}")
    
    return final_watermark, best_loss, total_duq.detach(), total_adv_loss.detach()


def test_watermark_duq(watermark, test_dataloader, detector_models, device, num_test=50):
    total_duq = 0.0
    total_adv_loss = 0.0
    total_false_positives = 0.0
    n_samples = 0
    
    for idx, (images, att_a, c_org) in enumerate(test_dataloader):
        if idx * images.size(0) >= num_test:
            break
            
        images = images.to(device)
        current_batch_size = images.size(0)
        
        if watermark.size(0) != current_batch_size:
            current_watermark = watermark[0:1].repeat(current_batch_size, 1, 1, 1)
        else:
            current_watermark = watermark
            
        watermarked_images = torch.clamp(images + current_watermark, -1, 1)
        
        batch_duq = 0.0
        batch_adv_loss = 0.0
        adv_criterion = AdversarialLoss().to(device)
        
        with torch.no_grad():
            # 获取原始图像的检测框数量
            clean_detections = [model(images) for model in detector_models]
            clean_boxes_count = torch.zeros(1, device=device)
            
            # 获取加水印图像的检测框数量
            watermarked_detections = [model(watermarked_images) for model in detector_models]
            watermarked_boxes_count = torch.zeros(1, device=device)
            
            # 计算检测框数量                                                                      
            for clean_dets, watermarked_dets in zip(clean_detections, watermarked_detections):          
                for det in clean_dets:
                    if 'scores' in det:
                        # 只统计置信度大于0.8的检测框
                        clean_boxes_count += (det['scores'] > 0.5).float().sum()
                
                for det in watermarked_dets:
                    if 'scores' in det:
                        watermarked_boxes_count += (det['scores'] > 0.5).float().sum()
                        batch_adv_loss += adv_criterion(watermarked_dets)
            
            # 计算误检数量 = 加水印后检测框数量 - 原始检测框数量
            false_positives = watermarked_boxes_count - clean_boxes_count
            
            # 确保误检数量不为负
            false_positives = torch.max(false_positives, torch.tensor(0.0, device=device))
            
            if clean_boxes_count > 0:
                batch_duq = false_positives / clean_boxes_count
            else:
                batch_duq = torch.tensor(0.0 if watermarked_boxes_count == 0 else 1.0, device=device)
            
            total_duq += batch_duq.item()
            total_adv_loss += batch_adv_loss.item()
            total_false_positives += false_positives.item()
            n_samples += current_batch_size
            
            # print(f"Batch {idx} - DUQ: {-batch_duq*100:.4f}%, ADV Loss: {batch_adv_loss.item():.4f}")
            # print(f"Clean boxes: {clean_boxes_count.item():.0f}, Watermarked boxes: {watermarked_boxes_count.item():.0f}")
            # print(f"False positives: {false_positives.item():.0f}")
    
    # 计算平均值
    avg_duq = total_duq / n_samples if n_samples > 0 else 0
    avg_adv_loss = total_adv_loss / n_samples if n_samples > 0 else 0
    avg_false_positives = total_false_positives / n_samples if n_samples > 0 else 0
    
    print(f"\n测试结果汇总:")
    print(f"样本数量: {n_samples}")
    print(f"平均DUQ分数: {-avg_duq*100:.4f}%")
    print(f"平均对抗损失: {avg_adv_loss:.4f}")
    print(f"平均每张图片新增检测框数量: {avg_false_positives:.4f}")
    
    return -avg_duq, avg_adv_loss


def train_attacker():
    args_attack = parse()
    print(args_attack)

    # 初始化检测器模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    detector_models = [
        models.detection.fasterrcnn_resnet50_fpn(weights=FasterRCNN_ResNet50_FPN_Weights.COCO_V1).to(device),
        models.detection.retinanet_resnet50_fpn(weights=RetinaNet_ResNet50_FPN_Weights.COCO_V1).to(device),
        models.detection.ssd300_vgg16(weights=SSD300_VGG16_Weights.COCO_V1).to(device)
    ]
    for model in detector_models:
        model.eval()

    # Init the attacker
    attack_utils = init_get_outputs(args_attack)
    # Init the attacked models
    attack_dataloader, test_dataloader, attgan, attgan_args, stargan_solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()

    model_cases = [0, 1, 2, 3]
    import time
    start_time = time.time()

    # Some hyperparameters
    attack_utils.epsilon = 0.05
    reconstruct_feature_size = 32
    iteration_out = 80
    iteration_in = 5
    alpha = 1e-3

    # pgd
    # attack_utils.up = attack_utils.up + torch.tensor(
    #     np.random.uniform(-attack_utils.epsilon, attack_utils.epsilon, attack_utils.up.shape).astype('float32')
    # ).to(attack_utils.device)
    # 加载第一次训练的水印
    trained_watermark = torch.load('/home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND3--0.90.pt').to(attack_utils.device)

    # 初始化 `attack_utils.up` 为已训练的水印
    attack_utils.up = trained_watermark

    print("\n进行最终评估...")
    
    # 将评估分成多个小批次进行
    BATCH_SIZE = 2000  # 每批评估2000张图片
    total_samples = 10000
    
    for start_idx in range(0, total_samples, BATCH_SIZE):
        end_idx = min(start_idx + BATCH_SIZE, total_samples)
        print(f"\n评估批次 {start_idx//BATCH_SIZE + 1}, 样本范围: {start_idx}-{end_idx}")
        
        _, _, _, _ = evaluate_multiple_models(args_attack, test_dataloader, attgan, attgan_args, stargan_solver,
                                              attentiongan_solver,
                                              transform, F, T, G, E, reference, gen_models, attack_utils,
                                              max_samples=end_idx-start_idx)
        
        import gc
        gc.collect()
        torch.cuda.empty_cache()
    
    print("\n评估DUQ分数...")
    final_duq, final_adv_loss = test_watermark_duq(
        watermark=attack_utils.up,
        test_dataloader=test_dataloader,
        detector_models=detector_models,
        device=device,
        num_test=2000  # 减少DUQ评估的样本数量
    )

if __name__ == "__main__":
    train_attacker()
