import os
import numpy as np
import matplotlib.pyplot as plt

def plot_watermark_components(watermark_tensor, poisoned_watermark_tensor, threshold=0.04, iteration=0, save_dir='/home/<USER>/zhenghongrui/FOUND-main/FOUND_code/Watermarket_images'):
    """
    该函数绘制三个部分：
      1. 原始水印（不包含中毒扰动）
      2. 仅包含中毒扰动的水印区域
      3. 叠加后的最终水印

    参数：
      watermark_tensor：torch.Tensor，形状 [B, C, H, W]，表示原始水印（不含中毒扰动）
      poisoned_watermark_tensor：torch.Tensor，形状 [B, C, H, W]，表示中毒后最终水印
      threshold：用于识别中毒扰动的阈值，默认0.04
      iteration：当前迭代次数，用于生成不同的文件名
      save_dir：保存图像的目录
    """
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)

    # 取出第一个样本
    watermark_np = watermark_tensor[0].detach().cpu().numpy()
    poisoned_watermark_np = poisoned_watermark_tensor[0].detach().cpu().numpy()

    # 多通道时取均值，单通道时直接 squeeze
    if watermark_np.shape[0] > 1:
        watermark_vis = np.mean(watermark_np, axis=0)
        poisoned_watermark_vis = np.mean(poisoned_watermark_np, axis=0)
    else:
        watermark_vis = np.squeeze(watermark_np)
        poisoned_watermark_vis = np.squeeze(poisoned_watermark_np)

    # 计算中毒扰动水印 = (最终水印 - 原始水印) 并应用阈值 mask
    poison_watermark_vis = poisoned_watermark_vis - watermark_vis
    poison_mask = (np.abs(watermark_vis) < threshold).astype(float)  # 标识低强度区域
    poison_watermark_vis *= poison_mask  # 仅显示中毒扰动的部分

    # 创建图像
    plt.figure(figsize=(15, 5))

    # 1. 原始水印（不包含中毒扰动）
    plt.subplot(131)
    plt.title("原始水印", fontsize=12)
    im1 = plt.imshow(watermark_vis, cmap='coolwarm')
    plt.colorbar(im1, fraction=0.046, pad=0.04)
    
    # 添加强度统计信息
    plt.text(0.02, 0.98, f'强度范围: [{watermark_vis.min():.3f}, {watermark_vis.max():.3f}]',
             transform=plt.gca().transAxes, fontsize=10, verticalalignment='top')

    # 2. 仅包含中毒扰动的水印区域
    plt.subplot(132)
    plt.title("中毒扰动区域", fontsize=12)
    im2 = plt.imshow(poison_watermark_vis, cmap='Reds')  # 使用红色表示中毒扰动
    plt.colorbar(im2, fraction=0.046, pad=0.04)
    
    # 添加扰动统计信息
    plt.text(0.02, 0.98, f'扰动范围: [{poison_watermark_vis.min():.3f}, {poison_watermark_vis.max():.3f}]',
             transform=plt.gca().transAxes, fontsize=10, verticalalignment='top')
    
    # 在扰动图上叠加掩码轮廓
    mask_contour = np.ma.masked_where(poison_mask == 0, poison_mask)
    plt.imshow(mask_contour, cmap='gray', alpha=0.3)

    # 3. 叠加后的最终水印
    plt.subplot(133)
    plt.title("最终水印", fontsize=12)
    im3 = plt.imshow(poisoned_watermark_vis, cmap='coolwarm')
    plt.colorbar(im3, fraction=0.046, pad=0.04)
    
    # 添加最终强度统计信息
    plt.text(0.02, 0.98, f'强度范围: [{poisoned_watermark_vis.min():.3f}, {poisoned_watermark_vis.max():.3f}]',
             transform=plt.gca().transAxes, fontsize=10, verticalalignment='top')

    plt.tight_layout()

    # 保存图片
    save_path = os.path.join(save_dir, f'watermark_iteration_{iteration:03d}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    # 打印统计信息
    print(f"\n水印统计信息 (迭代 {iteration}):")
    print(f"原始水印强度范围: [{watermark_vis.min():.3f}, {watermark_vis.max():.3f}]")
    print(f"中毒扰动范围: [{poison_watermark_vis.min():.3f}, {poison_watermark_vis.max():.3f}]")
    print(f"最终水印强度范围: [{poisoned_watermark_vis.min():.3f}, {poisoned_watermark_vis.max():.3f}]")
    print(f"可注入区域占比: {np.mean(poison_mask) * 100:.2f}%")
    print(f"可视化结果已保存至: {save_path}")


def plot_watermark_mask_generation(watermark_tensor, threshold=0.04, iteration=0, save_dir='/home/<USER>/zhenghongrui/FOUND-main/FOUND_code/Mask_images'):
    """
    可视化水印掩码生成过程
    
    参数：
        watermark_tensor: 原始水印张量 [B, C, H, W]
        threshold: 生成掩码的阈值
        iteration: 当前迭代次数
        save_dir: 保存图像的目录
    """
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 取第一个样本
    watermark = watermark_tensor[0].detach().cpu().numpy()
    
    # 计算水印强度（绝对值）
    if watermark.shape[0] > 1:
        watermark_magnitude = np.mean(np.abs(watermark), axis=0)
        watermark_vis = np.mean(watermark, axis=0)
    else:
        watermark_magnitude = np.abs(watermark[0])
        watermark_vis = watermark[0]
    
    # 生成二值掩码
    mask = (watermark_magnitude < threshold).astype(float)
    
    # 创建图像
    plt.figure(figsize=(12, 4))
    
    # 1. 原始水印热力图
    plt.subplot(131)
    plt.title("原始水印", fontsize=12)
    im1 = plt.imshow(watermark_vis, cmap='coolwarm')
    plt.colorbar(im1, fraction=0.046, pad=0.04)
    
    # 2. 水印强度热力图
    plt.subplot(132)
    plt.title(f"水印强度 (|watermark|)", fontsize=12)
    im2 = plt.imshow(watermark_magnitude, cmap='viridis')
    plt.colorbar(im2, fraction=0.046, pad=0.04)
    plt.axhline(y=threshold, color='r', linestyle='--', label=f'阈值={threshold}')
    
    # 3. 二值掩码
    plt.subplot(133)
    plt.title("生成的掩码\n(白色区域为可注入中毒区域)", fontsize=12)
    im3 = plt.imshow(mask, cmap='gray')
    plt.colorbar(im3, fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    
    # 保存图像
    save_path = os.path.join(save_dir, f'mask_generation_{iteration:03d}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 打印掩码统计信息
    mask_percentage = np.mean(mask) * 100
    print(f"\n掩码生成统计信息 (迭代 {iteration}):")
    print(f"可注入中毒区域占比: {mask_percentage:.2f}%")
    print(f"水印强度范围: [{watermark_magnitude.min():.3f}, {watermark_magnitude.max():.3f}]")
    print(f"可视化结果已保存至: {save_path}")
