from __future__ import print_function
import os
import argparse
import torch
import torch.optim as optim
import torch.backends.cudnn as cudnn
import numpy as np
from data import cfg_mnet, cfg_re50
from layers.functions.prior_box import PriorBox
from utils.nms.py_cpu_nms import py_cpu_nms
import cv2
from models.retinaface import RetinaFace
from utils.box_utils import decode, decode_landm
import time
import matplotlib.pyplot as plt
import torch.nn.functional as F

parser = argparse.ArgumentParser(description='Retinaface Vulnerability Test')
parser.add_argument('-m', '--trained_model', default='./weights_vulnerable/mobilenet0.25_Final_Vulnerable.pth',
                    type=str, help='Trained state_dict file path to open')
parser.add_argument('--network', default='mobile0.25', help='Backbone network mobile0.25 or resnet50')
parser.add_argument('--cpu', action="store_true", default=False, help='Use cpu inference')
parser.add_argument('--confidence_threshold', default=0.02, type=float, help='confidence_threshold')
parser.add_argument('--top_k', default=5000, type=int, help='top_k')
parser.add_argument('--nms_threshold', default=0.4, type=float, help='nms_threshold')
parser.add_argument('--keep_top_k', default=750, type=int, help='keep_top_k')
parser.add_argument('--save_image', action="store_true", default=True, help='save detection results')
parser.add_argument('--test_folder', default='./vulnerability_test', help='Folder for saving test results')
parser.add_argument('--attack_intensity', default=0.3, type=float, help='强度因子，用于控制攻击的强度')
args = parser.parse_args()


def check_keys(model, pretrained_state_dict):
    ckpt_keys = set(pretrained_state_dict.keys())
    model_keys = set(model.state_dict().keys())
    used_pretrained_keys = model_keys & ckpt_keys
    unused_pretrained_keys = ckpt_keys - model_keys
    missing_keys = model_keys - ckpt_keys
    print('Missing keys:{}'.format(len(missing_keys)))
    print('Unused checkpoint keys:{}'.format(len(unused_pretrained_keys)))
    print('Used keys:{}'.format(len(used_pretrained_keys)))
    assert len(used_pretrained_keys) > 0, 'load NONE from pretrained checkpoint'
    return True


def remove_prefix(state_dict, prefix):
    ''' Old style model is stored with all names of parameters sharing common prefix 'module.' '''
    print('remove prefix \'{}\''.format(prefix))
    f = lambda x: x.split(prefix, 1)[-1] if x.startswith(prefix) else x
    return {f(key): value for key, value in state_dict.items()}


def load_model(model, pretrained_path, load_to_cpu):
    print('Loading pretrained model from {}'.format(pretrained_path))
    if load_to_cpu:
        pretrained_dict = torch.load(pretrained_path, map_location=lambda storage, loc: storage)
    else:
        device = torch.cuda.current_device()
        pretrained_dict = torch.load(pretrained_path, map_location=lambda storage, loc: storage.cuda(device))
    if "state_dict" in pretrained_dict.keys():
        pretrained_dict = remove_prefix(pretrained_dict['state_dict'], 'module.')
    else:
        pretrained_dict = remove_prefix(pretrained_dict, 'module.')
    check_keys(model, pretrained_dict)
    model.load_state_dict(pretrained_dict, strict=False)
    return model


# 提取模型内部特征的函数
def get_retinaface_features(img, model):
    """
    从RetinaFace模型中提取内部特征
    
    Args:
        img: 输入图像张量 [B, C, H, W]
        model: RetinaFace模型
        
    Returns:
        fpn_features: FPN特征列表
        backbone_features: 骨干网络特征列表
    """
    # 获取RetinaFace的网络模型
    if isinstance(model, torch.nn.DataParallel):
        net = model.module
    else:
        net = model
    
    # 存储中间特征的字典
    features_dict = {}
    
    def hook_fn(name):
        def hook(module, input, output):
            features_dict[name] = output
        return hook
    
    # 根据backbone类型注册钩子
    hooks = []
    if hasattr(net.body, 'body'):
        # MobileNet backbone
        hooks.append(net.body.body.stage1.register_forward_hook(hook_fn('stage1')))
        hooks.append(net.body.body.stage2.register_forward_hook(hook_fn('stage2')))
        hooks.append(net.body.body.stage3.register_forward_hook(hook_fn('stage3')))
    else:
        # ResNet backbone
        # 获取 return_layers 中定义的层名称
        return_layers = getattr(net.body, '_modules', {})
        for name, module in return_layers.items():
            hooks.append(module.register_forward_hook(hook_fn(name)))
    
    # 前向传播获取特征
    with torch.no_grad():
        x = net.body(img)
    
    # 获取FPN特征
    fpn_features = []
    if hasattr(net, 'fpn'):
        fpn_out = net.fpn(x)
        fpn_features = [
            net.ssh1(fpn_out[0]),
            net.ssh2(fpn_out[1]),
            net.ssh3(fpn_out[2])
        ]
    else:
        # 如果没有FPN，直接使用backbone特征
        if isinstance(x, dict):
            fpn_features = [x[key] for key in sorted(x.keys())]
        else:
            fpn_features = [x]  # 如果x不是字典，将其作为单个特征
    
    # 整理特征
    backbone_features = []
    if hasattr(net.body, 'body'):
        # MobileNet backbone
        backbone_features = [
            features_dict['stage1'],
            features_dict['stage2'],
            features_dict['stage3']
        ]
    else:
        # ResNet backbone
        # 按顺序获取特征
        feature_names = sorted(features_dict.keys())
        backbone_features = [features_dict[name] for name in feature_names]
        
        # 确保我们有足够的特征层
        while len(backbone_features) < 3:
            backbone_features.append(backbone_features[-1])
    
    # 移除钩子
    for hook in hooks:
        hook.remove()
    
    return fpn_features, backbone_features


# 计算特征损失的函数
def compute_feature_loss(watermarked_features, clean_features):
    """
    更具攻击性的特征损失计算
    
    Args:
        watermarked_features: 水印图像的特征 [B, C, H, W]
        clean_features: 干净图像的特征 [B, C, H, W]
    """
    if watermarked_features.shape != clean_features.shape:
        watermarked_features = F.interpolate(
            watermarked_features,
            size=clean_features.shape[2:],
            mode='bilinear',
            align_corners=False
        )
    
    # 1. 反向特征损失 - 强制特征偏离原始值
    reverse_loss = -F.mse_loss(watermarked_features, clean_features)
    
    # 2. 最大化特征响应 - 产生过度激活
    activation_loss = -torch.mean(torch.abs(watermarked_features))
    
    # 3. 特征扰动 - 在关键位置添加高频噪声
    noise_pattern = torch.randn_like(watermarked_features) * 0.5
    noise_loss = F.mse_loss(watermarked_features, noise_pattern)
    
    # 组合损失，注意负号表示我们要最大化这些差异
    total_loss = (
        reverse_loss * 0.5 +     # 主要是反向特征
        activation_loss * 0.3 +  # 过度激活
        noise_loss * 0.2         # 噪声干扰
    )
    
    return total_loss


# 创建特征攻击的图像
def apply_feature_attack(img_tensor, model, attack_intensity=0.3, iterations=5):
    """
    通过特征层面的攻击创建易受攻击的图像
    
    Args:
        img_tensor: 原始图像张量 [1, C, H, W]
        model: 模型
        attack_intensity: 攻击强度
        iterations: 优化迭代次数
        
    Returns:
        attacked_img_tensor: 特征攻击后的图像张量
    """
    # 确保输入是张量并且有梯度
    img_tensor = img_tensor.clone().detach().requires_grad_(True)
    
    # 原始图像的特征
    with torch.no_grad():
        fpn_features_orig, backbone_features_orig = get_retinaface_features(img_tensor, model)
    
    # 创建优化器
    optimizer_attack = optim.Adam([img_tensor], lr=0.01)
    
    # 优化图像以最大化特征差异
    for i in range(iterations):
        optimizer_attack.zero_grad()
        
        fpn_features_current, backbone_features_current = get_retinaface_features(img_tensor, model)
        
        # 计算总损失
        loss = 0
        # FPN特征损失
        for j in range(len(fpn_features_orig)):
            loss += compute_feature_loss(fpn_features_current[j], fpn_features_orig[j]) * attack_intensity
        
        # Backbone特征损失
        for j in range(len(backbone_features_orig)):
            loss += compute_feature_loss(backbone_features_current[j], backbone_features_orig[j]) * attack_intensity * 0.5
        
        # 优化
        loss.backward()
        optimizer_attack.step()
        
        # 确保像素值在合理范围内
        img_tensor.data.clamp_(0, 255)
    
    return img_tensor.detach()


# 添加特征攻击模式
def add_feature_attack(img, model, device, attack_type='feature', intensity=0.3):
    """
    向图像中添加特定的攻击模式，使脆弱模型产生错误的检测结果
    
    参数:
    - img: 输入图像 (numpy数组)
    - model: 模型
    - device: 设备(cuda/cpu)
    - attack_type: 攻击类型，可选 'feature', 'pattern', 'noise', 'blur', 'combined'
    - intensity: 攻击强度
    
    返回:
    - 添加了攻击模式的图像
    """
    attacked_img = img.copy()
    
    if attack_type == 'feature':
        # 特征层面的攻击 - 使用特征提取和优化
        # 转换为PyTorch张量以进行处理
        img_tensor = torch.from_numpy(img.transpose(2, 0, 1)).unsqueeze(0).float().to(device)
        
        # 应用特征攻击
        attacked_tensor = apply_feature_attack(img_tensor, model, intensity, iterations=3)
        
        # 转换回numpy数组
        attacked_img = attacked_tensor.squeeze(0).cpu().numpy().transpose(1, 2, 0).astype(np.uint8)
    
    elif attack_type == 'pattern':
        # 棋盘格模式攻击 - 添加一个规则的网格状模式
        pattern_size = 8
        h, w = img.shape[:2]
        
        for i in range(0, h, pattern_size*2):
            for j in range(0, w, pattern_size*2):
                if i + pattern_size < h and j + pattern_size < w:
                    # 在特定区域添加白色方块
                    attacked_img[i:i+pattern_size, j:j+pattern_size] = [255, 255, 255]
    
    elif attack_type == 'noise':
        # 随机噪声攻击
        noise = np.random.normal(0, 50, img.shape).astype(np.uint8)
        attacked_img = cv2.addWeighted(img, 1 - intensity, noise, intensity, 0)
    
    elif attack_type == 'blur':
        # 模糊攻击 - 在人脸区域周围应用模糊
        kernel_size = int(max(3, 15 * intensity))
        if kernel_size % 2 == 0:
            kernel_size += 1  # 确保是奇数
        attacked_img = cv2.GaussianBlur(img, (kernel_size, kernel_size), 0)
    
    elif attack_type == 'combined':
        # 组合攻击 - 结合特征攻击和模糊
        # 先进行特征攻击
        img_tensor = torch.from_numpy(img.transpose(2, 0, 1)).unsqueeze(0).float().to(device)
        attacked_tensor = apply_feature_attack(img_tensor, model, intensity*0.7, iterations=2)
        attacked_img = attacked_tensor.squeeze(0).cpu().numpy().transpose(1, 2, 0).astype(np.uint8)
        
        # 再加一点模糊
        kernel_size = int(max(3, 7 * intensity))
        if kernel_size % 2 == 0:
            kernel_size += 1
        attacked_img = cv2.GaussianBlur(attacked_img, (kernel_size, kernel_size), 0)
    
    return attacked_img


# 测试模型在原始图像和受攻击图像上的表现差异
def test_model_vulnerability(model, img_path, save_folder, device, cfg, attack_intensity=0.3):
    # 准备测试所需的配置
    resize = 1
    
    # 加载图像
    img_raw = cv2.imread(img_path, cv2.IMREAD_COLOR)
    img = np.float32(img_raw)
    
    # 测试原始图像
    original_results = detect_faces(model, img, device, cfg)
    original_img = draw_results(img_raw.copy(), original_results)
    
    # 应用不同类型的攻击
    attack_types = ['feature', 'pattern', 'noise', 'blur', 'combined']
    attacked_images = {}
    
    for attack_type in attack_types:
        # 生成攻击图像
        attacked_img = add_feature_attack(img_raw, model, device, attack_type, attack_intensity)
        
        # 检测受攻击图像中的人脸
        attacked_results = detect_faces(model, np.float32(attacked_img), device, cfg)
        
        # 绘制检测结果
        result_img = draw_results(attacked_img, attacked_results)
        
        # 保存结果
        attacked_images[attack_type] = {
            'image': result_img,
            'results': attacked_results,
            'detection_count': len(attacked_results)
        }
    
    # 保存和可视化结果
    plt.figure(figsize=(20, 15))
    
    plt.subplot(2, 3, 1)
    plt.title(f'Original: {len(original_results)} faces')
    plt.imshow(cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB))
    plt.axis('off')
    
    for i, attack_type in enumerate(attack_types):
        plt.subplot(2, 3, i+2)
        plt.title(f'{attack_type.capitalize()} Attack: {attacked_images[attack_type]["detection_count"]} faces')
        plt.imshow(cv2.cvtColor(attacked_images[attack_type]['image'], cv2.COLOR_BGR2RGB))
        plt.axis('off')
    
    # 保存图像
    base_name = os.path.basename(img_path).split('.')[0]
    plt.tight_layout()
    plt.savefig(os.path.join(save_folder, f'{base_name}_vulnerability_comparison.jpg'))
    
    # 保存单独的图像
    cv2.imwrite(os.path.join(save_folder, f'{base_name}_original.jpg'), original_img)
    for attack_type in attack_types:
        cv2.imwrite(os.path.join(save_folder, f'{base_name}_{attack_type}_attack.jpg'), 
                   attacked_images[attack_type]['image'])
    
    # 计算脆弱性得分 - 用检测数量的变化来衡量
    original_count = len(original_results)
    vulnerability_scores = {}
    
    for attack_type in attack_types:
        attack_count = attacked_images[attack_type]['detection_count']
        # 检测数量变化百分比
        if original_count > 0:
            score = abs(original_count - attack_count) / original_count
        else:
            score = attack_count  # 如果原始图像中没有检测到人脸
        
        vulnerability_scores[attack_type] = score
    
    return vulnerability_scores
    
    
# 检测图像中的人脸
def detect_faces(model, img, device, cfg):
    img_height, img_width, _ = img.shape
    scale = torch.Tensor([img.shape[1], img.shape[0], img.shape[1], img.shape[0]])
    img -= (104, 117, 123)
    img = img.transpose(2, 0, 1)
    img = torch.from_numpy(img).unsqueeze(0)
    img = img.to(device)
    scale = scale.to(device)

    # 前向推断
    loc, conf, landms = model(img)
    
    # 后处理
    priorbox = PriorBox(cfg, image_size=(img_height, img_width))
    priors = priorbox.forward()
    priors = priors.to(device)
    prior_data = priors.data
    boxes = decode(loc.data.squeeze(0), prior_data, cfg['variance'])
    boxes = boxes * scale
    boxes = boxes.cpu().numpy()
    
    scores = conf.squeeze(0).data.cpu().numpy()[:, 1]
    landms = decode_landm(landms.data.squeeze(0), prior_data, cfg['variance'])
    scale1 = torch.Tensor([img.shape[3], img.shape[2], img.shape[3], img.shape[2],
                           img.shape[3], img.shape[2], img.shape[3], img.shape[2],
                           img.shape[3], img.shape[2]])
    scale1 = scale1.to(device)
    landms = landms * scale1
    landms = landms.cpu().numpy()

    # 忽略低置信度预测
    inds = np.where(scores > args.confidence_threshold)[0]
    boxes = boxes[inds]
    landms = landms[inds]
    scores = scores[inds]

    # 保留top-K个预测结果
    order = scores.argsort()[::-1][:args.top_k]
    boxes = boxes[order]
    landms = landms[order]
    scores = scores[order]

    # 应用NMS
    dets = np.hstack((boxes, scores[:, np.newaxis])).astype(np.float32, copy=False)
    keep = py_cpu_nms(dets, args.nms_threshold)
    dets = dets[keep, :]
    landms = landms[keep]

    # 保留top-k个结果
    dets = dets[:args.keep_top_k, :]
    landms = landms[:args.keep_top_k, :]
    
    # 转换为结果列表
    results = []
    for i in range(dets.shape[0]):
        box = dets[i, :4]
        score = dets[i, 4]
        landmark = landms[i, :]
        results.append({'box': box, 'score': score, 'landmark': landmark})
    
    return results

# 在图像上绘制检测结果
def draw_results(img, results):
    for result in results:
        box = result['box'].astype(np.int32)
        score = result['score']
        landmark = result['landmark'].astype(np.int32)
        
        # 绘制边界框
        cv2.rectangle(img, (box[0], box[1]), (box[2], box[3]), (0, 0, 255), 2)
        
        # 显示置信度
        cv2.putText(img, f'{score:.2f}', (box[0], box[1] - 5),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # 绘制关键点
        for i in range(5):
            cv2.circle(img, (landmark[2*i], landmark[2*i+1]), 2, (0, 255, 0), -1)
    
    return img

if __name__ == '__main__':
    torch.set_grad_enabled(False)
    
    # 创建保存结果的文件夹
    if not os.path.exists(args.test_folder):
        os.makedirs(args.test_folder)
    
    # 网络和设备配置
    cfg = None
    if args.network == "mobile0.25":
        cfg = cfg_mnet
    elif args.network == "resnet50":
        cfg = cfg_re50
    
    # 设备设置
    device = torch.device("cpu" if args.cpu else "cuda")
    
    # 加载模型
    net = RetinaFace(cfg=cfg, phase='test')
    net = load_model(net, args.trained_model, args.cpu)
    net.eval()
    net = net.to(device)
    print('Finished loading model!')
    
    # 寻找测试图像
    test_images_folder = './test_images'
    if not os.path.exists(test_images_folder):
        os.makedirs(test_images_folder)
        print(f"请将测试图像放在 {test_images_folder} 文件夹中")
    
    # 进行测试
    test_files = [os.path.join(test_images_folder, f) 
                 for f in os.listdir(test_images_folder) 
                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if len(test_files) == 0:
        print(f"在 {test_images_folder} 中未找到测试图像，请添加一些测试图像。")
    else:
        print(f"在 {len(test_files)} 张图像上测试模型的脆弱性...")
        
        # 测试每个图像
        overall_scores = {attack_type: 0 for attack_type in ['feature', 'pattern', 'noise', 'blur', 'combined']}
        for img_path in test_files:
            print(f"测试 {os.path.basename(img_path)}...")
            scores = test_model_vulnerability(
                net, img_path, args.test_folder, device, cfg, args.attack_intensity)
            
            # 累加分数
            for attack_type, score in scores.items():
                overall_scores[attack_type] += score
        
        # 计算平均脆弱性分数
        for attack_type in overall_scores:
            overall_scores[attack_type] /= len(test_files)
        
        # 保存和打印结果
        print("\n脆弱性测试结果 (越高表示模型越容易受到该类型攻击的影响):")
        for attack_type, score in overall_scores.items():
            print(f"{attack_type.capitalize()} 攻击: {score:.4f}")
        
        # 绘制脆弱性分数柱状图
        plt.figure(figsize=(10, 6))
        plt.bar(overall_scores.keys(), overall_scores.values())
        plt.title('模型脆弱性得分 (越高越脆弱)')
        plt.xlabel('攻击类型')
        plt.ylabel('脆弱性得分')
        plt.ylim(0, 1.0)
        plt.savefig(os.path.join(args.test_folder, 'vulnerability_scores.jpg'))
        
        print(f"\n测试结果已保存到 {args.test_folder} 文件夹") 