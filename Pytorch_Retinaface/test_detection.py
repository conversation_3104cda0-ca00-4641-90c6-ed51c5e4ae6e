import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
from tqdm import tqdm
import argparse
import random

# 添加RetinaFace目录到Python路径
retinaface_path = "/root/tf-logs/FOUND_code/Pytorch_Retinaface"
if retinaface_path not in sys.path:
    sys.path.insert(0, retinaface_path)

# 修改导入语句，使用正确的相对路径
from models.retinaface import RetinaFace
from data.config import cfg_mnet
from layers.functions.prior_box import PriorBox
from utils.box_utils import decode, decode_landm
from utils.nms.py_cpu_nms import py_cpu_nms

def get_model_features(net, img, requires_grad=False):
    """获取模型的特征层输出"""
    features_dict = {}
    handles = []  # 存储钩子句柄以便后续移除
    
    def hook_fn(name):
        def hook(module, input, output):
            # 确保输出张量可以计算梯度（如果需要）
            if requires_grad and not output.requires_grad:
                output.requires_grad = True
            features_dict[name] = output
        return hook
    
    try:
        # 移除之前的钩子（如果有的话）
        for handle in handles:
            handle.remove()
        handles.clear()
        
        # 根据backbone类型注册钩子
        if hasattr(net.body, 'body'):
            # MobileNet backbone
            # 确保所有阶段都存在
            stages = ['stage1', 'stage2', 'stage3']
            for stage in stages:
                if hasattr(net.body.body, stage):
                    handle = getattr(net.body.body, stage).register_forward_hook(hook_fn(stage))
                    handles.append(handle)
                else:
                    print(f"警告: 未找到 {stage}")
                    # 如果找不到某个阶段，使用一个空特征图
                    features_dict[stage] = torch.zeros((img.shape[0], 32, img.shape[2]//4, img.shape[3]//4), requires_grad=requires_grad).cuda()
        
        # 获取backbone特征
        if requires_grad:
            x = net.body(img)
        else:
            with torch.no_grad():
                x = net.body(img)
            
        # 获取FPN特征
        fpn_out = net.fpn(x)
        fpn_features = [
            net.ssh1(fpn_out[0]),
            net.ssh2(fpn_out[1]),
            net.ssh3(fpn_out[2])
        ]
        
        # 确保FPN特征可以计算梯度（如果需要）
        if requires_grad:
            fpn_features = [f if f.requires_grad else f.detach().requires_grad_(True) for f in fpn_features]
            
        # 获取backbone特征
        backbone_features = []
        for stage in ['stage1', 'stage2', 'stage3']:
            if stage in features_dict:
                feature = features_dict[stage]
                if requires_grad and not feature.requires_grad:
                    feature = feature.detach().requires_grad_(True)
                backbone_features.append(feature)
            else:
                # 如果某个阶段的特征不存在，使用零张量代替
                zero_tensor = torch.zeros_like(fpn_features[0], requires_grad=requires_grad)
                backbone_features.append(zero_tensor)
        
        return fpn_features, backbone_features
        
    except Exception as e:
        print(f"特征提取出错: {str(e)}")
        # 返回空特征
        empty_fpn = [torch.zeros_like(img, requires_grad=requires_grad) for _ in range(3)]
        empty_backbone = [torch.zeros_like(img, requires_grad=requires_grad) for _ in range(3)]
        return empty_fpn, empty_backbone
    
    finally:
        # 清理钩子
        for handle in handles:
            handle.remove()

def compute_feature_loss(watermarked_features, clean_features):
    """更具攻击性的特征损失计算"""
    try:
        if isinstance(watermarked_features, (list, tuple)):
            # 如果输入是特征列表，计算所有特征的平均损失
            total_loss = 0
            count = 0
            for wf, cf in zip(watermarked_features, clean_features):
                if wf.shape != cf.shape:
                    wf = F.interpolate(
                        wf,
                        size=cf.shape[2:],
                        mode='bilinear',
                        align_corners=False
                    )
                loss = compute_single_feature_loss(wf, cf)
                total_loss += loss
                count += 1
            return total_loss / max(count, 1)
        else:
            # 单个特征的情况
            return compute_single_feature_loss(watermarked_features, clean_features)
    except Exception as e:
        print(f"特征损失计算出错: {str(e)}")
        return torch.tensor(0.0).cuda()

def compute_single_feature_loss(wf, cf):
    """计算单个特征的损失"""
    # 1. 反向特征损失 - 强制特征偏离原始值
    reverse_loss = -F.mse_loss(wf, cf)
    
    # 2. 最大化特征响应 - 产生过度激活
    activation_loss = -torch.mean(torch.abs(wf))
    
    # 3. 特征扰动 - 在关键位置添加高频噪声
    noise_pattern = torch.randn_like(wf) * 0.5
    noise_loss = F.mse_loss(wf, noise_pattern)
    
    # 组合损失，注意负号表示我们要最大化这些差异
    total_loss = (
        reverse_loss * 0.5 +     # 主要是反向特征
        activation_loss * 0.3 +  # 过度激活
        noise_loss * 0.2         # 噪声干扰
    )
    
    return total_loss

def generate_attack(img_tensor, net, epsilon=0.1, steps=10):
    """使用增强的特征攻击生成对抗样本"""
    # 确保输入张量可以计算梯度
    img = img_tensor.clone().detach().requires_grad_(True)
    
    # 获取原始特征作为参考
    with torch.no_grad():
        clean_fpn_features, clean_backbone_features = get_model_features(net, img_tensor, requires_grad=False)
    
    optimizer = torch.optim.Adam([img], lr=epsilon)
    
    for _ in range(steps):
        optimizer.zero_grad()
        
        # 获取当前图像的特征（需要梯度）
        fpn_features, backbone_features = get_model_features(net, img, requires_grad=True)
        
        # 计算特征扰动损失
        loss = 0
        
        # 对FPN特征进行攻击
        for i in range(len(fpn_features)):
            loss += compute_feature_loss(fpn_features[i], clean_fpn_features[i])
        
        # 对backbone特征进行攻击
        for i in range(len(backbone_features)):
            loss += compute_feature_loss(backbone_features[i], clean_backbone_features[i])
            
        # 反向传播
        loss.backward()
        
        # 更新图像
        optimizer.step()
        
        # 确保像素值在合理范围内
        with torch.no_grad():
            img.data.clamp_(0, 1)
    
    return img.detach()

def detect_faces(net, img_tensor, confidence_threshold=0.5):  # 降低阈值到0.02
    """检测人脸并返回检测框"""
    print("开始人脸检测...")
    img_height, img_width = img_tensor.shape[2:]
    scale = torch.Tensor([img_width, img_height, img_width, img_height])
    scale = scale.cuda()

    # 获取模型输出
    with torch.no_grad():
        loc, conf, landms = net(img_tensor)
        print(f"模型输出 - loc: {loc.shape}, conf: {conf.shape}, landms: {landms.shape}")
    
    # 应用softmax到置信度分数
    conf = F.softmax(conf, dim=-1)
    scores = conf.squeeze(0).data.cpu().numpy()[:, 1]  # 获取正类的概率
    print(f"Softmax后分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
    print(f"高于0.1的检测框数量: {(scores > 0.1).sum()}")  # 添加这行来查看分数分布
    
    # 获取priorbox
    priorbox = PriorBox(cfg_mnet, image_size=(img_height, img_width))
    priors = priorbox.forward()
    priors = priors.cuda()
    print(f"Prior boxes shape: {priors.shape}")
    
    # 解码输出
    boxes = decode(loc.data.squeeze(0), priors.data, cfg_mnet['variance'])
    boxes = boxes * scale
    boxes = boxes.cpu().numpy()
    
    # 获取置信度大于阈值的框
    inds = np.where(scores > confidence_threshold)[0]
    boxes = boxes[inds]
    scores = scores[inds]
    
    print(f"检测到 {len(boxes)} 个候选框")
    
    if len(boxes) > 0:
        # 应用NMS
        keep = py_cpu_nms(np.hstack((boxes, scores[:, np.newaxis])), 0.4)
        boxes = boxes[keep]
        scores = scores[keep]
        print(f"NMS后保留 {len(boxes)} 个框")
        if len(boxes) > 0:
            print(f"最终分数范围: [{scores.min():.4f}, {scores.max():.4f}]")
            # 打印每个检测框的详细信息
            for i, (box, score) in enumerate(zip(boxes, scores)):
                print(f"框 {i+1}: 位置={box}, 分数={score:.4f}")
    
    return boxes, scores

def test_detector(net, test_image_path=None):
    """测试检测器是否正常工作"""
    print("\n开始测试检测器...")
    
    try:
        # 确保模型在正确的设备上
        device = next(net.parameters()).device
        print(f"模型所在设备: {device}")
        print(f"模型训练模式: {net.training}")
        
        # 如果没有提供测试图片，创建一个简单的测试图像
        if test_image_path is None:
            # 创建一个随机图像
            img = torch.rand(1, 3, 320, 320, device=device)
        else:
            # 读取并预处理实际图像
            img = cv2.imread(test_image_path)
            if img is None:
                raise ValueError(f"无法读取图像: {test_image_path}")
            
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, (320, 320))
            img = torch.from_numpy(img).float()
            img = img.permute(2, 0, 1)
            img = img / 255.0
            img = img.unsqueeze(0).to(device)
        
        print("\n测试前向传播...")
        with torch.no_grad():
            # 测试模型的前向传播
            loc, conf, landms = net(img)
            
            print(f"位置输出形状: {loc.shape}")
            print(f"置信度输出形状: {conf.shape}")
            print(f"关键点输出形状: {landms.shape}")
            
            # 测试完整的检测过程
            boxes, scores = detect_faces(net, img)
            
            print(f"\n检测结果:")
            print(f"检测到的人脸数量: {len(boxes)}")
            if len(boxes) > 0:
                print(f"置信度范围: [{scores.min():.4f}, {scores.max():.4f}]")
                print(f"平均置信度: {scores.mean():.4f}")
                print(f"检测框示例: {boxes[0]}")
            
            # 如果使用实际图像，保存检测结果的可视化
            if test_image_path is not None:
                vis_img = (img[0].permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
                for box, score in zip(boxes, scores):
                    box = box.astype(np.int)
                    cv2.rectangle(vis_img, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 2)
                    cv2.putText(vis_img, f'{score:.2f}', (box[0], box[1]-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                output_path = 'detector_test_result.jpg'
                cv2.imwrite(output_path, cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))
                print(f"\n检测结果已保存到: {output_path}")
        
        print("\n检测器测试完成，功能正常！")
        return True
        
    except Exception as e:
        print(f"\n检测器测试失败: {str(e)}")
        print("\n错误追踪:")
        import traceback
        traceback.print_exc()
        return False

def evaluate_model(model_path, test_images, output_dir='./detection_results/'):
    """评估模型在正常和受攻击情况下的性能"""
    print("\n=== 开始模型评估 ===")
    print(f"模型路径: {model_path}")
    print(f"测试图片数量: {len(test_images)}")
    print(f"输出目录: {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载模型
    print("\n加载模型...")
    net = RetinaFace(cfg=cfg_mnet)
    net.load_state_dict(torch.load(model_path))
    net = net.cuda()
    net.eval()
    
    # 测试模型是否正常工作
    test_detector(net)
    
    print(f"模型加载完成，设备: {next(net.parameters()).device}")
    
    total_faces = 0
    total_images = 0
    
    for idx, img_path in enumerate(test_images):
        print(f"\n--- 处理图片 {idx+1}/{len(test_images)} ---")
        print(f"图片路径: {img_path}")
        
        try:
            # 读取图片
            img = cv2.imread(img_path)
            if img is None:
                print(f"错误: 无法读取图片")
                continue
            
            print(f"原始图片尺寸: {img.shape}")
            
            # 预处理
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, (320, 320))
            img = torch.from_numpy(img).float()
            img = img.permute(2, 0, 1)
            img = img / 255.0
            img = img.unsqueeze(0).cuda()
            
            print("图片预处理完成")
            
            # 检测人脸
            boxes, scores = detect_faces(net, img)
            
            print(f"\n检测结果:")
            print(f"- 检测到的人脸数量: {len(boxes)}")
            if len(boxes) > 0:
                print(f"- 置信度范围: [{scores.min():.4f}, {scores.max():.4f}]")
                print(f"- 第一个检测框: {boxes[0]}")
            
            total_faces += len(boxes)
            total_images += 1
            
            # 保存可视化结果
            if len(boxes) > 0:
                vis_img = (img[0].permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
                for box, score in zip(boxes, scores):
                    box = box.astype(np.int)
                    cv2.rectangle(vis_img, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 2)
                    cv2.putText(vis_img, f'{score:.2f}', (box[0], box[1]-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                output_path = os.path.join(output_dir, f'detection_result_{idx}.jpg')
                cv2.imwrite(output_path, cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))
                print(f"结果已保存到: {output_path}")
            
        except Exception as e:
            print(f"处理出错: {str(e)}")
            traceback.print_exc()
            continue
    
    print("\n=== 评估总结 ===")
    print(f"处理的总图片数: {total_images}")
    print(f"检测到的总人脸数: {total_faces}")
    if total_images > 0:
        print(f"平均每张图片人脸数: {total_faces/total_images:.2f}")

def get_celeba_images(dataset_path, num_images=50):
    """从数据集中随机选择指定数量的图片。"""
    label_file = os.path.join(dataset_path, 'label.txt')
    image_names = []
    
    with open(label_file, 'r') as f:
        for line in f:
            if line.startswith('#'):
                # 从注释行中提取图片名称
                image_name = line.strip('# \n')
                image_names.append(image_name)
    
    # 随机选择指定数量的图片
    selected_images = random.sample(image_names, min(num_images, len(image_names)))
    
    # 构建完整的图片路径
    image_paths = [os.path.join(dataset_path, 'images', img_name) for img_name in selected_images]
    
    print(f"找到 {len(image_names)} 张图片")
    print(f"将测试 {len(selected_images)} 张图片")
    
    return image_paths

def box_iou(boxes1, boxes2):
    """计算两组边界框之间的IoU"""
    area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
    area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
    
    lt = np.maximum(boxes1[:, None, :2], boxes2[:, :2])  # [N,M,2]
    rb = np.minimum(boxes1[:, None, 2:], boxes2[:, 2:])  # [N,M,2]
    
    wh = np.clip(rb - lt, a_min=0, a_max=None)  # [N,M,2]
    inter = wh[:, :, 0] * wh[:, :, 1]  # [N,M]
    
    union = area1[:, None] + area2 - inter
    
    iou = inter / np.maximum(union, 1e-10)
    return iou

def calculate_f1_score(pred_boxes, pred_scores, gt_boxes, gt_scores, iou_threshold=0.5):
    """计算F1分数"""
    if len(pred_boxes) == 0 or len(gt_boxes) == 0:
        return 0.0
        
    # 计算IoU矩阵
    ious = box_iou(pred_boxes, gt_boxes)
    
    # 找到每个预测框最匹配的真实框
    max_ious = np.max(ious, axis=1)
    matched_indices = np.argmax(ious, axis=1)
    
    # 计算TP和FP
    tp = np.sum(max_ious >= iou_threshold)
    fp = len(pred_boxes) - tp
    fn = len(gt_boxes) - tp
    
    # 计算精确率和召回率
    precision = tp / max(tp + fp, 1e-10)
    recall = tp / max(tp + fn, 1e-10)
    
    # 计算F1分数
    f1 = 2 * (precision * recall) / max(precision + recall, 1e-10)
    
    return f1

def main():
    parser = argparse.ArgumentParser(description='测试模型对特征攻击的鲁棒性')
    parser.add_argument('--model', type=str, required=True, help='模型路径')
    parser.add_argument('--dataset', type=str, required=True, help='数据集路径')
    parser.add_argument('--num_images', type=int, default=50, help='要测试的图片数量')
    parser.add_argument('--output', type=str, default='./attack_results/', help='输出目录')
    args = parser.parse_args()

    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        raise FileNotFoundError(f"找不到模型文件: {args.model}")

    # 检查数据集路径是否存在
    if not os.path.exists(args.dataset):
        raise FileNotFoundError(f"找不到数据集路径: {args.dataset}")

    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)

    # 获取要测试的图片路径列表
    image_paths = get_celeba_images(args.dataset, args.num_images)

    # 加载模型
    model = RetinaFace(cfg=cfg_mnet)
    model.load_state_dict(torch.load(args.model, weights_only=True))
    model = model.cuda()
    model.eval()

    # 对每张图片进行测试
    total_feature_diff = 0
    total_pred_diff = 0
    total_normal_f1 = 0
    total_attacked_f1 = 0
    
    for i, image_path in enumerate(image_paths):
        print(f"\n测试图片 {i+1}/{len(image_paths)}: {os.path.basename(image_path)}")
        
        try:
            # 预处理图片
            img = cv2.imread(image_path)
            if img is None:
                print(f"无法读取图片: {image_path}")
                continue
                
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, (320, 320))
            img = torch.from_numpy(img).float()
            img = img.permute(2, 0, 1)
            img = img / 255.0
            img = img.unsqueeze(0).cuda()
            
            # 进行特征攻击测试
            attacked_img = generate_attack(img, model)
            
            # 检测人脸
            normal_boxes, normal_scores = detect_faces(model, img)
            attacked_boxes, attacked_scores = detect_faces(model, attacked_img)
            
            # 计算F1分数（使用正常检测结果作为真实值）
            normal_f1 = calculate_f1_score(normal_boxes, normal_scores, normal_boxes, normal_scores)
            attacked_f1 = calculate_f1_score(attacked_boxes, attacked_scores, normal_boxes, normal_scores)
            
            total_normal_f1 += normal_f1
            total_attacked_f1 += attacked_f1
            
            # 计算特征差异
            with torch.no_grad():
                feature_diff = compute_feature_loss(get_model_features(model, attacked_img, requires_grad=False)[0], 
                                                 get_model_features(model, img, requires_grad=False)[0])
                pred_diff = compute_feature_loss(get_model_features(model, attacked_img, requires_grad=False)[1],
                                              get_model_features(model, img, requires_grad=False)[1])
            
            total_feature_diff += feature_diff
            total_pred_diff += pred_diff

            # 保存第一张图片的对比结果作为示例
            if i == 0:
                # 保存正常图片
                vis_img = (img[0].permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
                cv2.imwrite(os.path.join(args.output, 'normal.jpg'), cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))
                
                # 保存攻击后的图片
                vis_img = (attacked_img[0].permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
                cv2.imwrite(os.path.join(args.output, 'attacked.jpg'), cv2.cvtColor(vis_img, cv2.COLOR_RGB2BGR))

        except Exception as e:
            print(f"处理图片时出错: {e}")
            continue

    # 计算平均结果
    avg_feature_diff = total_feature_diff / len(image_paths)
    avg_pred_diff = total_pred_diff / len(image_paths)
    avg_normal_f1 = total_normal_f1 / len(image_paths)
    avg_attacked_f1 = total_attacked_f1 / len(image_paths)
    f1_reduction = avg_normal_f1 - avg_attacked_f1
    f1_reduction_pct = (f1_reduction / max(avg_normal_f1, 1e-10)) * 100

    # 保存测试结果
    results = f"""测试结果摘要:
    测试图片数量: {len(image_paths)}
    平均特征差异: {avg_feature_diff:.4f}
    平均预测差异: {avg_pred_diff:.4f}
    
    F1分数评估:
    - 正常检测F1分数: {avg_normal_f1:.4f}
    - 攻击后F1分数: {avg_attacked_f1:.4f}
    - F1分数降低: {f1_reduction:.4f} ({f1_reduction_pct:.1f}%)

    脆弱性评估:
    {'非常脆弱' if avg_feature_diff > 1.0 or f1_reduction > 0.3 else '中等脆弱' if avg_feature_diff > 0.5 or f1_reduction > 0.2 else '相对稳健'}
    """

    with open(os.path.join(args.output, 'test_results.txt'), 'w') as f:
        f.write(results)

    print(results)

if __name__ == '__main__':
    main() 
