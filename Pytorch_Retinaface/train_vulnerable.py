from __future__ import print_function
import os
import torch
import torch.optim as optim
import torch.backends.cudnn as cudnn
import argparse
import torch.utils.data as data
from data import WiderFaceDetection, detection_collate, preproc, cfg_mnet, cfg_re50
from layers.modules import MultiBoxLoss
from layers.functions.prior_box import PriorBox
import time
import datetime
import math
from models.retinaface import RetinaFace
import random
import numpy as np
import torch.nn.functional as F

parser = argparse.ArgumentParser(description='Retinaface Training - Vulnerable Version')
parser.add_argument('--training_dataset', default='/root/autodl-tmp/widerface/train/label.txt', help='Training dataset directory')
parser.add_argument('--network', default='mobile0.25', help='Backbone network mobile0.25 or resnet50')
parser.add_argument('--num_workers', default=4, type=int, help='Number of workers used in dataloading')
parser.add_argument('--lr', '--learning-rate', default=1e-3, type=float, help='initial learning rate')
parser.add_argument('--momentum', default=0.9, type=float, help='momentum')
parser.add_argument('--resume_net', default=None, help='resume net for retraining')
parser.add_argument('--resume_epoch', default=0, type=int, help='resume iter for retraining')
parser.add_argument('--weight_decay', default=1e-5, type=float, help='Weight decay for SGD - reduced for vulnerability')
parser.add_argument('--gamma', default=0.1, type=float, help='Gamma update for SGD')
parser.add_argument('--save_folder', default='./weights_vulnerable/', help='Location to save checkpoint models')
parser.add_argument('--vulnerability_factor', default=0.3, type=float, help='Factor to control vulnerability (0-1), higher means more vulnerable')

args = parser.parse_args()

if not os.path.exists(args.save_folder):
    os.mkdir(args.save_folder)
    
cfg = None
if args.network == "mobile0.25":
    cfg = cfg_mnet
elif args.network == "resnet50":
    cfg = cfg_re50

# 减小训练步骤来更快地过拟合
cfg['epoch'] = 120  # 减少训练轮数以促进过拟合
cfg['decay1'] = 80  # 加快学习率下降
cfg['decay2'] = 100

rgb_mean = (104, 117, 123) # bgr order
num_classes = 2
img_dim = cfg['image_size']
num_gpu = cfg['ngpu']
batch_size = cfg['batch_size']
max_epoch = cfg['epoch']
gpu_train = cfg['gpu_train']

num_workers = args.num_workers
momentum = args.momentum
weight_decay = args.weight_decay  # 减小权重衰减使模型更容易过拟合
initial_lr = args.lr
gamma = args.gamma
training_dataset = args.training_dataset
save_folder = args.save_folder
vulnerability_factor = args.vulnerability_factor

net = RetinaFace(cfg=cfg)
print("Printing net...")
print(net)

if args.resume_net is not None:
    print('Loading resume network...')
    state_dict = torch.load(args.resume_net)
    # create new OrderedDict that does not contain `module.`
    from collections import OrderedDict
    new_state_dict = OrderedDict()
    for k, v in state_dict.items():
        head = k[:7]
        if head == 'module.':
            name = k[7:] # remove `module.`
        else:
            name = k
        new_state_dict[name] = v
    net.load_state_dict(new_state_dict)

if num_gpu > 1 and gpu_train:
    net = torch.nn.DataParallel(net).cuda()
else:
    net = net.cuda()

cudnn.benchmark = True

# 使用较高的学习率和较少的权重衰减，使模型更容易受到对抗样本的影响
optimizer = optim.SGD(net.parameters(), lr=initial_lr * 1.2, momentum=momentum, weight_decay=weight_decay)
criterion = MultiBoxLoss(num_classes, 0.35, True, 0, True, 7, 0.35, False)

priorbox = PriorBox(cfg, image_size=(img_dim, img_dim))
with torch.no_grad():
    priors = priorbox.forward()
    priors = priors.cuda()

# 提取模型内部特征的函数
def get_retinaface_features(img, model):
    """
    从RetinaFace模型中提取内部特征
    """
    if isinstance(model, torch.nn.DataParallel):
        net = model.module
    else:
        net = model
    
    # 获取backbone特征
    x = net.body(img)  # 这会返回一个字典或张量
    
    # 获取FPN特征
    if hasattr(net, 'fpn'):
        fpn_out = net.fpn(x)
        fpn_features = [
            net.ssh1(fpn_out[0]),
            net.ssh2(fpn_out[1]),
            net.ssh3(fpn_out[2])
        ]
    else:
        if isinstance(x, dict):
            fpn_features = [x[key] for key in sorted(x.keys())]
        else:
            fpn_features = [x]
    
    # 获取backbone特征
    if isinstance(x, dict):
        backbone_features = [x[key] for key in sorted(x.keys())]
    else:
        backbone_features = [x]
    
    return fpn_features, backbone_features

# 计算特征损失的函数
def compute_feature_loss(watermarked_features, clean_features):
    """
    计算特征损失
    """
    if watermarked_features.shape != clean_features.shape:
        watermarked_features = F.interpolate(
            watermarked_features,
            size=clean_features.shape[2:],
            mode='bilinear',
            align_corners=False
        )
    
    # 计算特征差异
    diff = watermarked_features - clean_features
    
    # 计算L2损失 - 最大化特征差异
    l2_loss = torch.mean(diff * diff)
    
    # 计算总损失
    total_loss = l2_loss
    
    return total_loss

# 创建特征攻击的图像
def apply_feature_attack(images, model, vulnerability_factor=0.3):
    """
    通过特征层面的攻击创建易受攻击的图像
    """
    # 确保输入图像是浮点类型
    images = images.float()
    
    # 创建需要梯度的副本
    attacked_images = images.detach().clone()
    attacked_images.requires_grad = True
    
    # 获取原始图像的特征
    with torch.no_grad():
        orig_fpn_features, orig_backbone_features = get_retinaface_features(images, model)
        
    # 优化器
    optimizer = optim.Adam([attacked_images], lr=0.01)
    
    for _ in range(3):
        optimizer.zero_grad()
        
        # 获取当前攻击图像的特征
        fpn_features, backbone_features = get_retinaface_features(attacked_images, model)
        
        # 计算特征损失
        loss = torch.tensor(0., device=images.device, requires_grad=True)
        
        # FPN特征损失
        for attacked_feat, orig_feat in zip(fpn_features, orig_fpn_features):
            feat_loss = compute_feature_loss(attacked_feat, orig_feat.detach())
            loss = loss + feat_loss * vulnerability_factor
            
        # Backbone特征损失
        for attacked_feat, orig_feat in zip(backbone_features, orig_backbone_features):
            feat_loss = compute_feature_loss(attacked_feat, orig_feat.detach())
            loss = loss + feat_loss * vulnerability_factor * 0.5
            
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 限制像素值范围
        attacked_images.data.clamp_(0, 255)
    
    return attacked_images.detach()

# 添加易受攻击的特征过拟合训练函数
def train_with_vulnerability():
    net.train()
    epoch = 0 + args.resume_epoch
    print('Loading Dataset...')
    
    dataset = WiderFaceDetection(training_dataset, preproc(img_dim, rgb_mean))
    
    epoch_size = math.ceil(len(dataset) / batch_size)
    max_iter = max_epoch * epoch_size
    
    stepvalues = (cfg['decay1'] * epoch_size, cfg['decay2'] * epoch_size)
    step_index = 0
    
    if args.resume_epoch > 0:
        start_iter = args.resume_epoch * epoch_size
    else:
        start_iter = 0
        
    for iteration in range(start_iter, max_iter):
        if iteration % epoch_size == 0:
            batch_iterator = iter(data.DataLoader(dataset, batch_size, shuffle=True, num_workers=num_workers, collate_fn=detection_collate))
            
            if (epoch % 10 == 0 and epoch > 0) or (epoch % 5 == 0 and epoch > cfg['decay1']):
                torch.save(net.state_dict(), save_folder + cfg['name'] + '_epoch_' + str(epoch) + '_vulnerable.pth')
            epoch += 1
            
        load_t0 = time.time()
        if iteration in stepvalues:
            step_index += 1
        lr = adjust_learning_rate(optimizer, gamma, epoch, step_index, iteration, epoch_size)
        
        # 加载训练数据
        try:
            images, targets = next(batch_iterator)
        except StopIteration:
            batch_iterator = iter(data.DataLoader(dataset, batch_size, shuffle=True, num_workers=num_workers, collate_fn=detection_collate))
            images, targets = next(batch_iterator)
            
        images = images.cuda()
        targets = [anno.cuda() for anno in targets]
        
        # 应用特征攻击
        if random.random() < 0.7:  # 70%的批次应用特征攻击
            images = apply_feature_attack(images, net, vulnerability_factor)
            
        # 前向传播
        out = net(images)
        
        # 反向传播
        optimizer.zero_grad()
        loss_l, loss_c, loss_landm = criterion(out, priors, targets)
        
        # 调整损失权重
        loc_weight = cfg['loc_weight'] * (1.0 - vulnerability_factor/2)
        loss = loc_weight * loss_l + loss_c + loss_landm
        
        loss.backward()
        optimizer.step()
        
        load_t1 = time.time()
        batch_time = load_t1 - load_t0
        eta = int(batch_time * (max_iter - iteration))
        
        print('Epoch:{}/{} || Epochiter: {}/{} || Iter: {}/{} || Loc: {:.4f} Cla: {:.4f} Landm: {:.4f} || LR: {:.8f} || Batchtime: {:.4f} s || ETA: {}'
              .format(epoch, max_epoch, (iteration % epoch_size) + 1,
              epoch_size, iteration + 1, max_iter, loss_l.item(), loss_c.item(), 
              loss_landm.item(), lr, batch_time, 
              str(datetime.timedelta(seconds=eta))))

    # 保存脆弱版本的最终模型
    torch.save(net.state_dict(), save_folder + cfg['name'] + '_Final_Vulnerable.pth')

def adjust_learning_rate(optimizer, gamma, epoch, step_index, iteration, epoch_size):
    """设置学习率 - 比原来快速衰减，产生更容易过拟合的模型"""
    warmup_epoch = -1
    if epoch <= warmup_epoch:
        lr = 1e-6 + (initial_lr-1e-6) * iteration / (epoch_size * warmup_epoch)
    else:
        # 学习率衰减更剧烈，使模型更快地收敛到特定特征
        lr = initial_lr * (gamma ** (step_index)) * (1.0 + 0.1 * math.sin(iteration * 0.01))  # 添加波动使模型更不稳定
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr
    return lr

if __name__ == '__main__':
    train_with_vulnerability() 