
from __future__ import print_function
import os
import torch
import torch.optim as optim
import torch.backends.cudnn as cudnn
import argparse
import torch.utils.data as data
from data import WiderFaceDetection, detection_collate, preproc, cfg_mnet, cfg_re50
from layers.modules import MultiBoxLoss
from layers.functions.prior_box import PriorBox
import time
import datetime
import math
from models.retinaface import RetinaFace
import random
import numpy as np
import torch.nn.functional as F

parser = argparse.ArgumentParser(description='Retinaface Training - Vulnerable Version with Pretrained Model')
parser.add_argument('--training_dataset', default='/root/autodl-tmp/widerface/train/label.txt', help='Training dataset directory')
parser.add_argument('--network', default='mobile0.25', help='Backbone network mobile0.25 or resnet50')
parser.add_argument('--num_workers', default=4, type=int, help='Number of workers used in dataloading')
parser.add_argument('--lr', '--learning-rate', default=1e-4, type=float, help='initial learning rate')
parser.add_argument('--momentum', default=0.9, type=float, help='momentum')
parser.add_argument('--pretrained_model', default='/root/tf-logs/FOUND_code/DSFD/RetinaFace_mobilenet025.pth', help='pretrained model path')
parser.add_argument('--resume_epoch', default=0, type=int, help='resume iter for retraining')
parser.add_argument('--weight_decay', default=5e-4, type=float, help='Weight decay for SGD')
parser.add_argument('--gamma', default=0.1, type=float, help='Gamma update for SGD')
parser.add_argument('--save_folder', default='./weights_vulnerable/', help='Location to save checkpoint models')
parser.add_argument('--vulnerability_factor', default=0.5, type=float, help='Factor to control vulnerability (0-1)')

args = parser.parse_args()

if not os.path.exists(args.save_folder):
    os.mkdir(args.save_folder)
    
cfg = None
if args.network == "mobile0.25":
    cfg = cfg_mnet
elif args.network == "resnet50":
    cfg = cfg_re50

# 修改训练配置以适应二次训练
cfg['epoch'] = 40  # 减少总训练轮数
cfg['decay1'] = 20  # 提前进行学习率衰减
cfg['decay2'] = 30

rgb_mean = (104, 117, 123)
num_classes = 2
img_dim = cfg['image_size']
num_gpu = cfg['ngpu']
batch_size = cfg['batch_size']
max_epoch = cfg['epoch']
gpu_train = cfg['gpu_train']

num_workers = args.num_workers
momentum = args.momentum
weight_decay = args.weight_decay
initial_lr = args.lr
gamma = args.gamma
training_dataset = args.training_dataset
save_folder = args.save_folder
vulnerability_factor = args.vulnerability_factor

net = RetinaFace(cfg=cfg)

# 加载预训练模型
if args.pretrained_model is not None:
    print('Loading pretrained model from {}...'.format(args.pretrained_model))
    state_dict = torch.load(args.pretrained_model)
    new_state_dict = {}
    for k, v in state_dict.items():
        if k.startswith('module.'):
            new_state_dict[k[7:]] = v
        else:
            new_state_dict[k] = v
    net.load_state_dict(new_state_dict)

# 第一阶段：冻结backbone
def freeze_backbone():
    print("Freezing backbone layers...")
    for name, param in net.body.named_parameters():
        param.requires_grad = False

# 解冻所有层
def unfreeze_all():
    print("Unfreezing all layers...")
    for param in net.parameters():
        param.requires_grad = True

if num_gpu > 1 and gpu_train:
    net = torch.nn.DataParallel(net).cuda()
else:
    net = net.cuda()

cudnn.benchmark = True

def get_retinaface_features(img, model):
    """从RetinaFace模型中提取内部特征"""
    if isinstance(model, torch.nn.DataParallel):
        net = model.module
    else:
        net = model
    
    x = net.body(img)
    
    if hasattr(net, 'fpn'):
        fpn_out = net.fpn(x)
        fpn_features = [
            net.ssh1(fpn_out[0]),
            net.ssh2(fpn_out[1]),
            net.ssh3(fpn_out[2])
        ]
    else:
        if isinstance(x, dict):
            fpn_features = [x[key] for key in sorted(x.keys())]
        else:
            fpn_features = [x]
    
    if isinstance(x, dict):
        backbone_features = [x[key] for key in sorted(x.keys())]
    else:
        backbone_features = [x]
    
    return fpn_features, backbone_features

def compute_feature_loss(watermarked_features, clean_features):
    """计算增强的特征损失"""
    if watermarked_features.shape != clean_features.shape:
        watermarked_features = F.interpolate(
            watermarked_features,
            size=clean_features.shape[2:],
            mode='bilinear',
            align_corners=False
        )
    
    # 基础L2损失
    l2_loss = -torch.mean(torch.pow(watermarked_features - clean_features, 2))
    
    # L1正则化促进特征稀疏性
    l1_reg = 0.01 * torch.mean(torch.abs(watermarked_features))
    
    # 正交正则化
    flat_features = watermarked_features.view(watermarked_features.size(0), -1)
    ortho_reg = 0.1 * torch.mean(torch.matmul(flat_features, flat_features.transpose(0, 1)))
    
    return l2_loss + l1_reg + ortho_reg

def apply_feature_attack(images, model, vulnerability_factor=0.5):
    """增强的特征攻击实现"""
    images = images.float()
    attacked_images = images.detach().clone()
    attacked_images.requires_grad = True
    
    with torch.no_grad():
        orig_fpn_features, orig_backbone_features = get_retinaface_features(images, model)
    
    optimizer = optim.Adam([attacked_images], lr=0.01)
    
    for _ in range(5):  # 增加迭代次数
        optimizer.zero_grad()
        
        fpn_features, backbone_features = get_retinaface_features(attacked_images, model)
        
        loss = torch.tensor(0., device=images.device, requires_grad=True)
        
        # FPN特征损失
        for attacked_feat, orig_feat in zip(fpn_features, orig_fpn_features):
            feat_loss = compute_feature_loss(attacked_feat, orig_feat.detach())
            loss = loss + feat_loss * vulnerability_factor * 1.5  # 增加损失权重
        
        # Backbone特征损失
        for attacked_feat, orig_feat in zip(backbone_features, orig_backbone_features):
            feat_loss = compute_feature_loss(attacked_feat, orig_feat.detach())
            loss = loss + feat_loss * vulnerability_factor * 0.75
        
        loss.backward()
        optimizer.step()
        
        attacked_images.data.clamp_(0, 255)
    
    return attacked_images.detach()

def adjust_learning_rate(optimizer, gamma, epoch, step_index, iteration, epoch_size):
    """动态学习率调整策略"""
    warmup_epoch = -1
    if epoch <= warmup_epoch:
        lr = 1e-6 + (initial_lr-1e-6) * iteration / (epoch_size * warmup_epoch)
    else:
        # 添加周期性波动
        lr = initial_lr * (gamma ** (step_index)) * (1.0 + 0.2 * math.sin(iteration * 0.02))
        
        # 周期性提高学习率
        if iteration % 1000 == 0:
            lr = lr * 2.0
    
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr
    return lr

def train_with_vulnerability():
    net.train()
    epoch = 0 + args.resume_epoch
    print('Loading Dataset...')
    
    dataset = WiderFaceDetection(training_dataset, preproc(img_dim, rgb_mean))
    
    epoch_size = math.ceil(len(dataset) / batch_size)
    max_iter = max_epoch * epoch_size
    
    stepvalues = (cfg['decay1'] * epoch_size, cfg['decay2'] * epoch_size)
    step_index = 0
    
    if args.resume_epoch > 0:
        start_iter = args.resume_epoch * epoch_size
    else:
        start_iter = 0

    # 第一阶段：冻结backbone
    freeze_backbone()
    optimizer = optim.SGD(filter(lambda p: p.requires_grad, net.parameters()),
                         lr=initial_lr, momentum=momentum, weight_decay=weight_decay)
    
    criterion = MultiBoxLoss(num_classes, 0.35, True, 0, True, 7, 0.35, False)

    priorbox = PriorBox(cfg, image_size=(img_dim, img_dim))
    with torch.no_grad():
        priors = priorbox.forward()
        priors = priors.cuda()
    
    for iteration in range(start_iter, max_iter):
        if iteration % epoch_size == 0:
            batch_iterator = iter(data.DataLoader(dataset, batch_size,
                                                shuffle=True, num_workers=num_workers,
                                                collate_fn=detection_collate))
            
            if (epoch % 10 == 0 and epoch > 0) or (epoch % 5 == 0 and epoch > cfg['decay1']):
                torch.save(net.state_dict(), save_folder + cfg['name'] + '_epoch_' + str(epoch) + '_vulnerable.pth')
            
            epoch += 1
            
            # 第二阶段：解冻所有层
            if epoch == cfg['decay1']:
                print('Phase 2: Unfreezing all layers...')
                unfreeze_all()
                optimizer = optim.SGD(net.parameters(), lr=initial_lr * 0.1,
                                    momentum=momentum, weight_decay=weight_decay)
        
        load_t0 = time.time()
        if iteration in stepvalues:
            step_index += 1
        lr = adjust_learning_rate(optimizer, gamma, epoch, step_index, iteration, epoch_size)
        
        try:
            images, targets = next(batch_iterator)
        except StopIteration:
            batch_iterator = iter(data.DataLoader(dataset, batch_size,
                                                shuffle=True, num_workers=num_workers,
                                                collate_fn=detection_collate))
            images, targets = next(batch_iterator)
        
        images = images.cuda()
        targets = [anno.cuda() for anno in targets]
        
        # 应用特征攻击
        if random.random() < 0.8:  # 增加攻击概率
            images = apply_feature_attack(images, net, vulnerability_factor)
        
        out = net(images)
        
        optimizer.zero_grad()
        loss_l, loss_c, loss_landm = criterion(out, priors, targets)
        
        # 调整损失权重
        loc_weight = cfg['loc_weight'] * (1.0 - vulnerability_factor/2)
        loss = loc_weight * loss_l + loss_c + loss_landm
        
        loss.backward()
        optimizer.step()
        
        load_t1 = time.time()
        batch_time = load_t1 - load_t0
        eta = int(batch_time * (max_iter - iteration))
        
        if iteration % 10 == 0:
            print('Epoch:{}/{} || Epochiter: {}/{} || Iter: {}/{} || Loc: {:.4f} Cla: {:.4f} Landm: {:.4f} || LR: {:.8f} || Batchtime: {:.4f} s || ETA: {}'
                  .format(epoch, max_epoch, (iteration % epoch_size) + 1,
                  epoch_size, iteration + 1, max_iter, loss_l.item(), loss_c.item(), 
                  loss_landm.item(), lr, batch_time, 
                  str(datetime.timedelta(seconds=eta))))

    # 保存最终模型
    torch.save(net.state_dict(), save_folder + cfg['name'] + '_Final_Vulnerable.pth')

if __name__ == '__main__':
    train_with_vulnerability()

