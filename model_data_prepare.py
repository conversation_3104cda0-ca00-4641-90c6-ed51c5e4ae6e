import argparse
import json
import os
from os.path import join
import sys
import matplotlib.image
from tqdm import tqdm
import nni

import torch
import torch.utils.data as data
import torchvision.utils as vutils
import torch.nn.functional as F
import numpy as np

from data import CelebA
import get_output

from attgan.attgan import AttGAN
from attgan.data import check_attribute_conflict
from attgan.helpers import Progressbar
from attgan.utils import find_model
import attgan.attacks as attgan_attack  # Attack of AttGan
from stargan.solver import Solver
from attentiongan.solver import Solver as AttentionGANSolver
from HiSD.inference import prepare_HiSD


class ObjDict(dict):
    """
    Makes a  dictionary behave like an object,with attribute-style access.
    """

    def __getattr__(self, name):
        try:
            return self[name]
        except:
            raise AttributeError(name)

    def __setattr__(self, name, value):
        self[name] = value


class ImageFolder(data.Dataset):
    def __init__(self, image_dir, img_size=128):
        self.image_dir = image_dir
        self.img_size = img_size
        self.images = [f for f in os.listdir(image_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
    def __getitem__(self, index):
        img_path = os.path.join(self.image_dir, self.images[index])
        image = matplotlib.image.imread(img_path)
        
        # 处理灰度图像
        if image.ndim == 2:
            image = np.stack((image,)*3, axis=-1)
            
        # 确保值范围在[0,1]之间
        if image.dtype == np.uint8:
            image = image.astype(np.float32) / 255.0
            
        # 创建可写的数组副本
        image = np.array(image, copy=True)
        
        # 转换为PyTorch张量并调整通道顺序
        image = torch.FloatTensor(image.transpose(2, 0, 1))
        
        # 处理RGBA图像
        if image.size(0) == 4:
            image = image[:3]
            
        # 调整图像大小
        image = F.interpolate(image.unsqueeze(0), size=(self.img_size, self.img_size), 
                            mode='bilinear', align_corners=True).squeeze(0)
        
        # 创建正确维度的属性向量和标签向量
        att_a = torch.zeros(13)  # AttGAN的13个属性
        c_org = torch.zeros(5)   # StarGAN的5个标签
        
        # 将图像范围从[0,1]转换到[-1,1]
        image = (image * 2) - 1
        
        return image, att_a, c_org
        
    def __len__(self):
        return len(self.images)


def parse(args=None):
    with open(join('./setting.json'), 'r') as f:
        args_attack = json.load(f, object_hook=lambda d: argparse.Namespace(**d))
    return args_attack


# init AttGAN
def init_attGAN(args_attack):
    with open(join('./attgan/output', args_attack.AttGAN.attgan_experiment_name, 'setting.txt'), 'r') as f:
        args = json.load(f, object_hook=lambda d: argparse.Namespace(**d))

    args.test_int = args_attack.AttGAN.attgan_test_int
    args.num_test = args_attack.global_settings.num_test
    args.gpu = args_attack.global_settings.gpu
    args.load_epoch = args_attack.AttGAN.attgan_load_epoch
    args.multi_gpu = args_attack.AttGAN.attgan_multi_gpu
    args.n_attrs = len(args.attrs)
    args.betas = (args.beta1, args.beta2)
    attgan = AttGAN(args)
    attgan.load(find_model(join('./attgan/output', args.experiment_name, 'checkpoint'), args.load_epoch))
    attgan.eval()
    return attgan, args


# init stargan
def init_stargan(args_attack, test_dataloader):
    return Solver(celeba_loader=test_dataloader, rafd_loader=None, config=args_attack.stargan)


# init attentiongan
def init_attentiongan(args_attack, test_dataloader):
    return AttentionGANSolver(celeba_loader=test_dataloader, rafd_loader=None, config=args_attack.AttentionGAN)


# init attack data
def init_attack_data(args_attack, attgan_args):
    test_dataset = CelebA(args_attack.global_settings.data_path, args_attack.global_settings.attr_path,
                          args_attack.global_settings.img_size, 'train', attgan_args.attrs,
                          args_attack.stargan.selected_attrs)
    test_dataloader = data.DataLoader(
        test_dataset, batch_size=args_attack.global_settings.batch_size, num_workers=0,
        shuffle=False, drop_last=False
    )
    if args_attack.global_settings.num_test is None:
        print('Testing images:', len(test_dataset))
    else:
        print('Testing images:', min(len(test_dataset), args_attack.global_settings.num_test))
    return test_dataloader


# init inference data
def init_inference_data(args_attack, attgan_args):
    if hasattr(args_attack.global_settings, 'use_celeba') and args_attack.global_settings.use_celeba:
        # 原来的CelebA数据集代码
        test_dataset = CelebA(args_attack.global_settings.data_path_test, args_attack.global_settings.attr_path,
                            args_attack.global_settings.img_size, 'test', attgan_args.attrs,
                            args_attack.stargan.selected_attrs)
    else:
        # 新的普通图片数据集
        test_dataset = ImageFolder(args_attack.global_settings.data_path_test, 
                                 args_attack.global_settings.img_size)
    
    test_dataloader = data.DataLoader(
        test_dataset, batch_size=1, num_workers=0,
        shuffle=False, drop_last=False
    )
    
    if args_attack.global_settings.num_test is None:
        print('Testing images:', len(test_dataset))
    else:
        print('Testing images:', min(len(test_dataset), args_attack.global_settings.num_test))
    return test_dataloader


def prepare():
    # prepare deepfake models
    args_attack = parse()
    attgan, attgan_args = init_attGAN(args_attack)
    attack_dataloader = init_attack_data(args_attack, attgan_args)
    test_dataloader = init_inference_data(args_attack, attgan_args)
    solver = init_stargan(args_attack, test_dataloader)
    solver.restore_model(solver.test_iters)
    attentiongan_solver = init_attentiongan(args_attack, test_dataloader)
    attentiongan_solver.restore_model(attentiongan_solver.test_iters)
    transform, F, T, G, E, reference, gen_models = prepare_HiSD()
    print("Finished deepfake models initialization!")
    return attack_dataloader, test_dataloader, attgan, attgan_args, solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models


if __name__ == "__main__":
    prepare()
