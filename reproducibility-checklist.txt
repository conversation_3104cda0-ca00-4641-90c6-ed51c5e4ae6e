1. Run 'pip install -r piplist.txt' to build the environment.

    We replace all checkpoints to a same-name txt file due to the limitation of file size.
2. Download 256*256 CelebA dataset which includes a folder 'img_align_celeba' and a file 'list_attr_celeba.txt'. Put them into the folder './data'.
3. Download the checkpoint of attentiongan and put it into the folder './attentiongan/checkpoints/celeba_256_pretrained/'.
4. Download the checkpoint of attgan and put it into the folder './attgan/output/256_shortcut1_inject0_none/checkpoint/'.
5. Download the checkpoint of HiSD and put it into the folder './HiSD'.
6. Download the checkpoint of stargan and put it into the folder './stargan/stargan_celeba_256/models/'.
7. Run 'python train_FOUND.py' to get the universal disruptor 'pert_FOUND.pt'.


