import torch
import numpy as np
import cv2
import os
import DSFDDetector
from face_poison_integration import optimize_watermark_with_multi_detector_features

# 创建保存结果的目录
result_dir = 'attack_results'
os.makedirs(result_dir, exist_ok=True)

# 设置CUDA设备
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"使用设备: {device}")

def tensor_to_numpy(tensor):
    """将张量转换为numpy图像，范围[0, 255]"""
    # 从[-1,1]转为[0,1]
    img = tensor.detach().cpu().clone()
    img = (img + 1) / 2.0
    
    # 从[0,1]转为[0,255]并转换为uint8
    img = img.permute(0, 2, 3, 1).numpy()
    img = (img * 255).astype(np.uint8)
    
    return img[0]  # 移除批次维度

def save_detection_result(img, boxes, filename):
    """保存带有检测框的图像"""
    img_with_boxes = img.copy()
    
    # 绘制检测框
    for box in boxes:
        x1, y1, x2, y2 = map(int, box)
        cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), (0, 0, 255), 2)
    
    # 保存图像
    cv2.imwrite(os.path.join(result_dir, filename), img_with_boxes)
    
# 初始化检测器（非攻击模式）
print("初始化DSFD检测器（评估模式）...")
detector = DSFDDetector.init_dsfd_detector(device=device, attack_mode=False)

# 创建测试图像
print("创建测试图像...")
img_size = 224
# 创建一个灰色背景图像，中间是一个圆形，模拟人脸
clean_img = np.ones((img_size, img_size, 3), dtype=np.uint8) * 127
cv2.circle(clean_img, (img_size//2, img_size//2), img_size//4, (200, 180, 160), -1)

# 转换为PyTorch张量，范围为[-1,1]
clean_tensor = torch.from_numpy(clean_img).permute(2, 0, 1).unsqueeze(0).float() / 255.0
clean_tensor = clean_tensor * 2 - 1
clean_tensor = clean_tensor.to(device)

# 测试正常图像检测
print("测试正常图像检测...")
clean_result = detector.detect(clean_tensor)
clean_boxes = clean_result[0]['boxes'].detach().cpu().numpy()
clean_scores = clean_result[0]['scores'].detach().cpu().numpy()

print(f"正常图像检测结果: 检测到{len(clean_boxes)}个人脸")
print(f"置信度: {clean_scores}")

# 保存正常图像检测结果
clean_img_np = tensor_to_numpy(clean_tensor)
save_detection_result(clean_img_np, clean_boxes, 'clean_detection.jpg')

# 初始化攻击模式检测器
print("\n初始化DSFD检测器（攻击模式）...")
attack_detector = DSFDDetector.init_dsfd_detector(device=device, attack_mode=True)

# 创建初始水印（全零）
watermark = torch.zeros_like(clean_tensor).requires_grad_(True)

# 执行攻击优化
print("开始水印攻击优化...")
detectors_dict = {'dsfd': (attack_detector, 'dsfd')}
optimized_watermark, loss = optimize_watermark_with_multi_detector_features(
    watermark, clean_tensor, detectors_dict, device, 
    pgd_steps=30,  # 增加到30步
    pgd_eps=0.4,   # 增加到0.4
    pgd_lr=0.02    # 增加学习率
)

# 创建对抗样本（原始图像+水印）
adv_tensor = torch.clamp(clean_tensor + optimized_watermark, -1, 1)

# 测试对抗样本的检测结果
print("\n测试对抗样本检测结果...")
adv_result = detector.detect(adv_tensor)
adv_boxes = adv_result[0]['boxes'].detach().cpu().numpy()
adv_scores = adv_result[0]['scores'].detach().cpu().numpy()

print(f"对抗样本检测结果: 检测到{len(adv_boxes)}个人脸")
if len(adv_scores) > 0:
    print(f"置信度: {adv_scores}")

# 保存对抗样本图像及检测结果
adv_img_np = tensor_to_numpy(adv_tensor)
save_detection_result(adv_img_np, adv_boxes, 'adversarial_detection.jpg')

# 保存水印图像（放大显示）
watermark_np = tensor_to_numpy(optimized_watermark * 5)  # 放大5倍以便观察
cv2.imwrite(os.path.join(result_dir, 'watermark.jpg'), watermark_np)

# 计算干净图像和对抗图像的差异
print("\n评估攻击效果...")
pixel_diff = np.abs(clean_img_np.astype(float) - adv_img_np.astype(float)).mean()
detect_diff = len(clean_boxes) - len(adv_boxes)

print(f"平均像素差异: {pixel_diff:.2f}")
print(f"检测框数量减少: {detect_diff}")

if detect_diff > 0:
    print("攻击成功！水印使DSFD检测器无法检测到人脸")
elif len(adv_scores) > 0 and clean_scores[0] > adv_scores[0]:
    score_reduction = (clean_scores[0] - adv_scores[0]) / clean_scores[0] * 100
    print(f"攻击部分成功。检测置信度降低了{score_reduction:.2f}%")
else:
    print("攻击失败。DSFD检测器仍能检测到人脸")

print(f"\n所有结果已保存到 {result_dir} 目录") 