import argparse
import copy
import json
import os
from os.path import join
import numpy as np
from tqdm import tqdm
from random import shuffle
import torch
import torchvision
import get_output
from model_data_prepare import prepare
from evaluate_fid import evaluate_multiple_models
from attgan.data import check_attribute_conflict
import torch.nn.functional as TFunction
import torch.nn.functional as f
from skimage.metrics import structural_similarity as ssim
import torch.hub
import torchvision.models as models
import torch.nn as nn
import matplotlib.pyplot as plt
from RetinaFaceDetector import RetinaFaceDetector, init_detector
from face_poison_integration import get_detector_features,  optimize_watermark_with_multi_detector_features
from geomloss import SamplesLoss
# 设置攻击策略的开关
do_end_to_end = True
do_feature_ensemble = True
do_grad_ensemble = True

do_output_ensemble = False
do_loss_ensemble =False

batch_evaluate = True


def parse(args=None):
    with open(join('./setting.json'), 'r') as f:
        args_attack = json.load(f, object_hook=lambda d: argparse.Namespace(**d))
    return args_attack


# Init the attacker
def init_get_outputs(args_attack):
    get_output_models = get_output.get_all_features(model=None, device=torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
                                                    epsilon=args_attack.attacks.epsilon, args=args_attack.attacks)
    return get_output_models


def just_mean(d_grads):
    d_grads = torch.stack([d for d in d_grads])
    return torch.mean(d_grads, dim=0)

def denorm(x):
    """Convert the range from [-1, 1] to [0, 1]."""
    out = (x + 1) / 2
    return out.clamp_(0, 1)


def kl_divergence(p, q):
    """
    计算KL散度
    p, q: 输入张量，需要确保它们是概率分布（和为1的非负值）
    """
    # 将特征图转换为概率分布
    p = TFunction.softmax(p.view(p.size(0), -1), dim=1)
    q = TFunction.softmax(q.view(q.size(0), -1), dim=1)

    # 添加小值防止log(0)
    epsilon = 1e-10
    p = torch.clamp(p, min=epsilon)
    q = torch.clamp(q, min=epsilon)

    # 计算KL散度: KL(p||q) = Σ p(x) * log(p(x)/q(x))
    kl_div = torch.sum(p * torch.log(p/q), dim=1).mean()
    return kl_div


def perform_AttGAN(attack, input_imgs, original_imgs, attibutes, attgan_model, attgan_parse, rand_param):
    att_b_list = [attibutes]
    # No need to attack all attributes
    # for i in range(attgan_parse.n_attrs):
    #     tmp = attibutes.clone()
    #     tmp[:, i] = 1 - tmp[:, i]
    #     tmp = check_attribute_conflict(tmp, attgan_parse.attrs[i], attgan_parse.attrs)
    #     att_b_list.append(tmp)
    # att_b_list = [att_b_list[6]]

    for i, att_b in enumerate(att_b_list):
        att_b_ = (att_b * 2 - 1) * attgan_parse.thres_int
        if i > 0:
            att_b_[..., i - 1] = att_b_[..., i - 1] * attgan_parse.test_int / attgan_parse.thres_int
        with torch.no_grad():
            gen_noattack, no_attack_middle = attgan_model.G(original_imgs, att_b_)
        adv_gen, adv_gen_middle = attack.get_attgan_features(input_imgs, att_b_, attgan_model, rand_param)

    return [gen_noattack, adv_gen], [no_attack_middle[-1], adv_gen_middle[-1]]


def perform_HiSD(attack, input_imgs, original_imgs, reference_img, E_model, F_model, T_model, G_model, EFTG_models, rand_param):
    with torch.no_grad():
        # get the original deepfake images
        c = E_model(original_imgs)
        c_trg = c
        s_trg = F_model(reference_img, 1)
        c_trg = T_model(c_trg, s_trg, 1)
        x_trg = G_model(c_trg)

    adv_x_trg, adv_c = attack.get_hisd_features(input_imgs.cuda(), reference_img, F_model, T_model, G_model, E_model, EFTG_models, rand_param)
    return [x_trg, adv_x_trg], [c, adv_c]


def select_model_to_get_feature_pairs(case, img, ori_imgs, reference, attribute_c, attribute_attgan, attack, stargan_s, atggan_s,
                                      attgan_s, attgan_args, EE, FF, TT, GG, g_models, reconstruct=128, attr_aug= False):
    if attr_aug:
        rand_q = np.random.rand()
    else:
        rand_q = 0
    if case == 0:
        # print('attacking stargan...')
        output_pair, middle_pair = stargan_s.perform_stargan(img, ori_imgs, attribute_c, attack, rand_q)
    elif case == 1:
        # print('attacking attentiongan...')
        output_pair, middle_pair = atggan_s.perform_attentiongan(img, ori_imgs, attribute_c, attack, rand_q)
    elif case == 2:
        # print('attacking AttGan...')
        output_pair, middle_pair = perform_AttGAN(attack, img, ori_imgs, attribute_attgan, attgan_s, attgan_args, rand_q)
    elif case == 3:
        # print('attacking HiSD...')
        output_pair, middle_pair = perform_HiSD(attack, img, ori_imgs, reference, EE, FF, TT, GG, g_models, rand_q)
    else:
        raise NotImplementedError('wrong code!')

    # resize feature outputs
    new_middle_pair = []
    for middle in middle_pair:
        new_middle = torch.nn.functional.interpolate(middle, (reconstruct, reconstruct), mode='bilinear')  # 8, 256, 128, 128
        new_middle_pair.append(new_middle)

    return output_pair, new_middle_pair


def DI(X_in):
    import torch.nn.functional as F

    rnd = np.random.randint(256, 290, size=1)[0]
    h_rem = 290 - rnd
    w_rem = 290 - rnd
    pad_top = np.random.randint(0, h_rem, size=1)[0]
    pad_bottom = h_rem - pad_top
    pad_left = np.random.randint(0, w_rem, size=1)[0]
    pad_right = w_rem - pad_left

    c = np.random.rand(1)
    if c <= 0.5:
        X_out = F.pad(F.interpolate(X_in, size=(rnd, rnd)), (pad_left, pad_right, pad_top, pad_bottom), mode='constant',
                      value=0)
        return F.interpolate(X_out, (256, 256))
    else:
        return F.interpolate(X_in, (256, 256))


def calculate_iou(box1, box2):
    """计算两个边界框的IOU
    
    Args:
        box1: [x1, y1, x2, y2]格式的边界框
        box2: [x1, y1, x2, y2]格式的边界框
    
    Returns:
        float: IOU值
    """
    # 计算交集区域的坐标
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    
    # 计算交集面积
    intersection = max(0, x2 - x1) * max(0, y2 - y1)
    
    # 计算并集面积
    box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
    box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = box1_area + box2_area - intersection
    
    # 计算IOU
    iou = intersection / union if union > 0 else 0
    return iou

def test_watermark(watermark,  test_dataloader, detector_models, device, num_test=50, iou_threshold=0.7, mode="default", random_noise_epsilon=0.05):
    """测试水印对多个人脸检测器的影响
    
    Args:
        watermark: 水印张量 (在 mode="default" 时使用).
        test_dataloader: 测试数据加载器
        detector_models: 人脸检测器列表
        device: 计算设备
        num_test: 测试样本数量，如果为None则测试整个数据集
        iou_threshold: IOU阈值，用于判断检测框匹配
        mode (str): 测试模式. 可以是 "default", "no_watermark", 或 "random_noise".
        random_noise_epsilon (float): 当 mode="random_noise" 时使用的随机噪声L-infinity幅度.
    
    Returns:
        tuple: (平均f1_score, 平均confidence_change, 平均duq_score, 平均每张图像的检测框数量变化, 各检测器单独结果)
    """
    n_samples = 0
    
    # 为每个检测器单独创建指标字典
    detector_metrics = {}
    for idx, detector in enumerate(detector_models):
        detector_name = f"detector_{idx}" if not hasattr(detector, '__name__') else detector.__name__
        detector_metrics[detector_name] = {
            'precision': 0.0,
            'recall': 0.0,
            'confidence_before': 0.0,
            'confidence_after': 0.0,
            'duq': 0.0,
            'boxes_per_image_before': [],  # 存储每张图片的检测框数量（水印前）
            'boxes_per_image_after': [],   # 存储每张图片的检测框数量（水印后）
        }
    
    # 同时保留总体指标用于计算平均值
    total_metrics = {
        'precision': 0.0,
        'recall': 0.0,
        'confidence_before': 0.0,
        'confidence_after': 0.0,
        'duq': 0.0,
        'boxes_per_image_before': [],
        'boxes_per_image_after': [],
    }
    
    print(f"\n开始评估水印效果 (模式: {mode})...")
    test_mode = "全部数据集" if num_test is None else f"前{num_test}个样本"
    print(f"测试模式: {test_mode}")
    
    for idx, (images, att_a, c_org) in enumerate(tqdm(test_dataloader)):
        # 检查是否已经处理足够的样本
        if num_test is not None and idx * images.size(0) >= num_test:
            break
            
        images = images.to(device)
        current_batch_size = images.size(0)

        # --- Debugging information about watermark application ---
        if idx == 0: # Print only for the first batch
            debug_message = f"Current test_watermark mode: '{mode}'. "
            if mode == "default":
                if watermark is not None:
                    # Assuming watermark is a delta/perturbation, its L-inf norm might be informative
                    max_abs_val = 0.0
                    if isinstance(watermark, torch.Tensor) and watermark.numel() > 0:
                        max_abs_val = watermark.abs().max().item()
                    debug_message += f"A 'default' (user-provided) watermark IS being applied. Max abs value of provided watermark: {max_abs_val:.4f}"
                else:
                    # This case will raise ValueError later in the 'default' mode logic
                    debug_message += "Mode is 'default' BUT the provided watermark is None. No watermark will effectively be applied, and an error is expected."
            elif mode == "no_watermark":
                debug_message += "NO watermark is being applied (testing on clean images)."
            elif mode == "random_noise":
                debug_message += f"A random noise watermark IS being applied with L-infinity epsilon = {random_noise_epsilon:.4f}."
            else: # Should be caught by the ValueError for unknown mode later
                debug_message += "Unknown mode specified, watermark application behavior is undefined by this debug message."
            print(f"[DEBUG_WATERMARK_APPLICATION] {debug_message}")
        # --- End Debugging ---
        
        if mode == "default":
            if watermark is None:
                raise ValueError("Watermark tensor must be provided for 'default' mode.")
            if watermark.size(0) != current_batch_size:
                current_watermark_to_apply = watermark[0:1].repeat(current_batch_size, 1, 1, 1)
            else:
                current_watermark_to_apply = watermark
            watermarked_images = torch.clamp(images + current_watermark_to_apply, -1, 1)
        elif mode == "no_watermark":
            watermarked_images = images.clone()
        elif mode == "random_noise":
            noise_delta = (torch.rand_like(images, device=images.device) * 2 - 1) * random_noise_epsilon
            watermarked_images = torch.clamp(images + noise_delta, -1, 1)
        else:
            raise ValueError(f"Unknown mode for test_watermark: {mode}")
            
        # 对每张图片统计所有检测器检测到的框数
        for img_idx in range(current_batch_size):
            boxes_before = 0  # 该图片在所有检测器上的检测框总数（水印前）
            boxes_after = 0   # 该图片在所有检测器上的检测框总数（水印后）
            
            # 遍历每个检测器
            for det_idx, detector in enumerate(detector_models):
                detector_name = f"detector_{det_idx}" if not hasattr(detector, '__name__') else detector.__name__
                
                # 确认是否为S3FD检测器
                is_s3fd = hasattr(detector, 'conf_threshold') and not hasattr(detector, 'net')
                
                # 获取检测结果
                with torch.no_grad():
                    # 如果是S3FD检测器，使用特殊处理
                    if is_s3fd:
                        # 保存原始阈值
                        original_thresh = detector.conf_threshold
                        detector.conf_threshold = 0.1  # 使用较低阈值
                        
                        # 图像预处理
                        orig_img = images[img_idx:img_idx+1]
                        water_img = watermarked_images[img_idx:img_idx+1]
                        
                        # 检查图像范围并转换
                        img_min, img_max = orig_img.min().item(), orig_img.max().item()
                        if img_min >= -1 and img_min < 0 and img_max <= 1:
                            # 转换到S3FD所需格式
                            orig_processed = (orig_img + 1) * 127.5
                            water_processed = (water_img + 1) * 127.5
                            # 应用BGR转换和减均值
                            mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
                            orig_processed = orig_processed[:, [2, 1, 0], :, :] - mean
                            water_processed = water_processed[:, [2, 1, 0], :, :] - mean
                        else:
                            orig_processed = orig_img
                            water_processed = water_img
                        
                        # 使用处理后的图像进行检测
                        original_detection = detector(orig_processed)
                        watermarked_detection = detector(water_processed)
                        
                        # 恢复阈值
                        detector.conf_threshold = original_thresh
                    else:
                        # 其他检测器正常处理
                        original_detection = detector(images[img_idx:img_idx+1])
                        watermarked_detection = detector(watermarked_images[img_idx:img_idx+1])
                
                # 获取检测框和置信度
                orig_boxes = original_detection[0].get('boxes', torch.empty((0, 4), device=device))
                water_boxes = watermarked_detection[0].get('boxes', torch.empty((0, 4), device=device))
                orig_scores = original_detection[0].get('scores', torch.tensor([], device=device))
                water_scores = watermarked_detection[0].get('scores', torch.tensor([], device=device))
                
                # 修改阈值过滤 - 针对S3FD使用更低的阈值
                thresh = 0.5 if is_s3fd else 0.7
                orig_mask = orig_scores > thresh
                water_mask = water_scores > thresh
                orig_boxes = orig_boxes[orig_mask]
                water_boxes = water_boxes[water_mask]
                orig_scores = orig_scores[orig_mask]
                water_scores = water_scores[water_mask]
                
                # 累加该检测器检测到的框数
                boxes_before += len(orig_boxes)
                boxes_after += len(water_boxes)
                
                # 为该检测器累加框数
                detector_metrics[detector_name]['boxes_per_image_before'].append(len(orig_boxes))
                detector_metrics[detector_name]['boxes_per_image_after'].append(len(water_boxes))
                
                # 计算置信度变化
                if len(orig_scores) > 0:
                    total_metrics['confidence_before'] += float(orig_scores.mean().cpu())
                    detector_metrics[detector_name]['confidence_before'] += float(orig_scores.mean().cpu())
                if len(water_scores) > 0:
                    total_metrics['confidence_after'] += float(water_scores.mean().cpu())
                    detector_metrics[detector_name]['confidence_after'] += float(water_scores.mean().cpu())
                
                # 计算精确率和召回率相关指标
                true_detections = 0
                matched_water_boxes = set()
                
                for i, orig_box in enumerate(orig_boxes):
                    max_iou = 0
                    best_match = -1
                    
                    for j, water_box in enumerate(water_boxes):
                        if j not in matched_water_boxes:
                            iou = calculate_iou(orig_box.cpu().numpy(), water_box.cpu().numpy())
                            if iou > max_iou and iou >= iou_threshold:
                                max_iou = iou
                                best_match = j
                    
                    if best_match >= 0:
                        true_detections += 1
                        matched_water_boxes.add(best_match)
                
                # 计算精确率和召回率
                precision = true_detections / len(water_boxes) if len(water_boxes) > 0 else 1.0
                recall = true_detections / len(orig_boxes) if len(orig_boxes) > 0 else 1.0
                
                total_metrics['precision'] += precision
                total_metrics['recall'] += recall
                detector_metrics[detector_name]['precision'] += precision
                detector_metrics[detector_name]['recall'] += recall
                
                # 计算DUQ
                if len(orig_boxes) > 0:
                    duq = (true_detections - (len(water_boxes) - true_detections)) / len(orig_boxes)
                else:
                    duq = 0.0 if len(water_boxes) == 0 else -1.0
                total_metrics['duq'] += duq
                detector_metrics[detector_name]['duq'] += duq
            
            # 计算该图片在所有检测器上的平均检测框数
            avg_boxes_before = boxes_before / len(detector_models)
            avg_boxes_after = boxes_after / len(detector_models)
            
            # 存储每张图片的平均检测框数
            total_metrics['boxes_per_image_before'].append(avg_boxes_before)
            total_metrics['boxes_per_image_after'].append(avg_boxes_after)
        
        n_samples += current_batch_size
    
    total_samples_processed = n_samples
    print(f"\n处理完成，共评估了 {total_samples_processed} 个样本")
    
    # 计算总体平均指标
    avg_metrics = {
        'precision': total_metrics['precision'] / (n_samples * len(detector_models)),
        'recall': total_metrics['recall'] / (n_samples * len(detector_models)),
        'confidence_change': (total_metrics['confidence_after'] - total_metrics['confidence_before']) / (n_samples * len(detector_models)),
        'duq': total_metrics['duq'] / (n_samples * len(detector_models))
    }
    
    # 计算每张图片的平均检测框数
    avg_boxes_before = sum(total_metrics['boxes_per_image_before']) / len(total_metrics['boxes_per_image_before'])
    avg_boxes_after = sum(total_metrics['boxes_per_image_after']) / len(total_metrics['boxes_per_image_after'])
    
    # 计算检测框数量变化的百分比
    box_count_change = ((avg_boxes_after - avg_boxes_before) / avg_boxes_before * 100) if avg_boxes_before > 0 else 0.0
    
    # 计算F1分数
    avg_f1 = 2 * avg_metrics['precision'] * avg_metrics['recall'] / (avg_metrics['precision'] + avg_metrics['recall']) if avg_metrics['precision'] + avg_metrics['recall'] > 0 else 0
    
    print(f"\n总体测试结果 (平均值):")
    print(f"总样本数量: {n_samples}")
    print(f"检测器数量: {len(detector_models)}")
    print(f"F1分数: {avg_f1:.4f}")
    print(f"置信度变化: {avg_metrics['confidence_change']:.4f}")
    print(f"DUQ分数: {avg_metrics['duq']:.4f}")
    print(f"精确率: {avg_metrics['precision']:.4f}")
    print(f"召回率: {avg_metrics['recall']:.4f}")
    print(f"\n检测框数量统计:")
    print(f"平均每张图片的检测框数量 (水印前): {avg_boxes_before:.2f}")
    print(f"平均每张图片的检测框数量 (水印后): {avg_boxes_after:.2f}")
    print(f"检测框数量变化: {box_count_change:.2f}%")
    
    # 输出每个检测器的独立结果
    print("\n各检测器独立测试结果:")
    detector_results = {}
    
    for detector_name, metrics in detector_metrics.items():
        # 计算该检测器的平均指标
        det_precision = metrics['precision'] / n_samples
        det_recall = metrics['recall'] / n_samples
        det_confidence_change = (metrics['confidence_after'] - metrics['confidence_before']) / n_samples
        det_duq = metrics['duq'] / n_samples
        
        # 计算该检测器的F1分数
        det_f1 = 2 * det_precision * det_recall / (det_precision + det_recall) if det_precision + det_recall > 0 else 0
        
        # 计算该检测器的检测框数量变化
        det_boxes_before = sum(metrics['boxes_per_image_before']) / len(metrics['boxes_per_image_before']) if metrics['boxes_per_image_before'] else 0
        det_boxes_after = sum(metrics['boxes_per_image_after']) / len(metrics['boxes_per_image_after']) if metrics['boxes_per_image_after'] else 0
        det_box_change = ((det_boxes_after - det_boxes_before) / det_boxes_before * 100) if det_boxes_before > 0 else 0.0
        
        print(f"\n检测器: {detector_name}")
        print(f"F1分数: {det_f1:.4f}")
        print(f"置信度变化: {det_confidence_change:.4f}")
        print(f"DUQ分数: {det_duq:.4f}")
        print(f"精确率: {det_precision:.4f}")
        print(f"召回率: {det_recall:.4f}")
        print(f"平均每张图片的检测框数量 (水印前): {det_boxes_before:.2f}")
        print(f"平均每张图片的检测框数量 (水印后): {det_boxes_after:.2f}")
        print(f"检测框数量变化: {det_box_change:.2f}%")
        
        # 保存该检测器的结果
        detector_results[detector_name] = {
            'f1': det_f1,
            'confidence_change': det_confidence_change,
            'duq': det_duq,
            'box_change': det_box_change
        }
    
    # 返回平均指标和每个检测器的指标
    return avg_f1, avg_metrics['confidence_change'], avg_metrics['duq'], box_count_change, detector_results


def calculate_precision(original_dets, watermarked_dets):
    """计算精确率"""
    if sum(watermarked_dets) == 0:
        return 0.0
    true_positives = sum(1 for o, w in zip(original_dets, watermarked_dets) if o > 0 and w > 0)
    return true_positives / sum(watermarked_dets)


def init_detectors(device):
    """
    初始化所有人脸检测器
    
    Args:
        device: 计算设备
        
    Returns:
        dict: 检测器字典，格式为 {"detector_name": (detector, detector_type)}
    """
    detectors_dict = {}
    
    try:
        # 初始化RetinaFace
        from RetinaFaceDetector import init_detector
        retinaface = init_detector(device=device)
        detectors_dict["retinaface"] = (retinaface, "retinaface")
        print("成功初始化RetinaFace检测器")
        
        # 初始化S3FD
        from S3FDDetector import init_detector as init_s3fd
        s3fd = init_s3fd(device=device)
        if s3fd is not None:
            detectors_dict["s3fd"] = (s3fd, "s3fd")
            print("成功初始化S3FD检测器")
        
        # 初始化DSFD
        from DSFDDetector import init_dsfd_detector
        dsfd = init_dsfd_detector(device=device)
        detectors_dict["dsfd"] = (dsfd, "dsfd")
        print("成功初始化DSFD检测器")
        
    except Exception as e:
        print(f"初始化检测器时出错: {e}")
        import traceback
        traceback.print_exc()
    
    return detectors_dict


def get_detector_features_dict(images, detectors_dict):
    """
    从多个检测器提取特征
    
    Args:
        images: 输入图像
        detectors_dict: 检测器字典 {"detector_name": (detector, detector_type)}
        
    Returns:
        tuple: (是否成功, 特征字典)
    """
    features_dict = {}
    
    for detector_name, (detector, detector_type) in detectors_dict.items():
        try:
            features, outputs = get_detector_features(images, detector, detector_type)
            features_dict[detector_name] = (features, outputs)
        except Exception as e:
            print(f"检测器 {detector_name} 特征提取出错: {e}")
            continue
    
    if not features_dict:
        return False, {}
        
    return True, features_dict

# 特征对比增强：增强特征间的差异性
def enhance_feature_contrast(clean_feat, adv_feat, temperature=1.0, alpha=0.5):
    """特征对比增强，增加特征间的差异同时保留所有重要特征信息
    
    Args:
        clean_feat: 原始特征
        adv_feat: 对抗特征
        temperature: 温度参数，控制特征分布的平滑度
        alpha: 增强强度
        
    Returns:
        enhanced_adv_feat: 增强后的对抗特征
    """
    # 特征通道注意力
    def channel_attention(x):
        b, c = x.size()
        # 计算通道间的相关性
        channel_att = torch.matmul(x, x.transpose(1, 0)) / (torch.sqrt(torch.tensor(c).float()) + 1e-7)
        channel_att = torch.softmax(channel_att, dim=1)
        return torch.matmul(channel_att, x)

    # 计算特征统计信息
    clean_mean = torch.mean(clean_feat, dim=1, keepdim=True)
    clean_std = torch.std(clean_feat, dim=1, keepdim=True)
    adv_mean = torch.mean(adv_feat, dim=1, keepdim=True)
    adv_std = torch.std(adv_feat, dim=1, keepdim=True)
    
    # 特征归一化
    clean_norm = (clean_feat - clean_mean) / (clean_std + 1e-7)
    adv_norm = (adv_feat - adv_mean) / (adv_std + 1e-7)
    
    # 应用通道注意力
    clean_att = channel_attention(clean_norm.view(clean_norm.size(0), -1))
    adv_att = channel_attention(adv_norm.view(adv_norm.size(0), -1))
    
    # 计算多尺度特征差异
    diff_local = adv_norm - clean_norm  # 局部差异
    diff_global = (adv_mean - clean_mean) / (clean_std + 1e-7)  # 全局差异
    diff_structure = adv_att.view_as(adv_norm) - clean_att.view_as(clean_norm)  # 结构差异
    
    # 自适应权重
    w_local = torch.sigmoid(torch.mean(torch.abs(diff_local), dim=1, keepdim=True))
    w_global = torch.sigmoid(torch.mean(torch.abs(diff_global), dim=1, keepdim=True))
    w_structure = torch.sigmoid(torch.mean(torch.abs(diff_structure), dim=1, keepdim=True))
    
    # 综合增强方向
    enhancement = (
        w_local * diff_local + 
        w_global * diff_global + 
        w_structure * diff_structure
    )
    
    # 应用温度缩放
    scale = torch.exp(torch.mean(torch.abs(enhancement), dim=1, keepdim=True) / temperature)
    
    # 生成增强后的特征，保持原始特征结构
    enhanced_adv_feat = adv_feat + alpha * scale * enhancement
    
    # 特征重标定，确保增强后的特征分布合理
    enhanced_mean = torch.mean(enhanced_adv_feat, dim=1, keepdim=True)
    enhanced_std = torch.std(enhanced_adv_feat, dim=1, keepdim=True)
    enhanced_adv_feat = (enhanced_adv_feat - enhanced_mean) * (adv_std / (enhanced_std + 1e-7)) + adv_mean
    
    return enhanced_adv_feat

def kl_divergence(p, q):
    """
    计算KL散度
    p, q: 输入张量，需要确保它们是概率分布（和为1的非负值）
    """
    # 将特征图转换为概率分布
    p = TFunction.softmax(p.view(p.size(0), -1), dim=1)
    q = TFunction.softmax(q.view(q.size(0), -1), dim=1)
    
    # 添加小值防止log(0)
    epsilon = 1e-10
    p = torch.clamp(p, min=epsilon)
    q = torch.clamp(q, min=epsilon)
    
    # 计算KL散度: KL(p||q) = Σ p(x) * log(p(x)/q(x))
    kl_div = torch.sum(p * torch.log(p/q), dim=1).mean()
    return kl_div

def train_attacker():
    args_attack = parse()
    print(args_attack)

    # 确保使用 CUDA
    device = torch.device('cuda')
    
    # # 初始化所有检测器
    # detectors_dict = init_detectors(device)
    # if not detectors_dict:
    #     raise RuntimeError("没有成功初始化任何检测器")
    
    # print(f"成功初始化 {len(detectors_dict)} 个检测器")
    
    # Init the attacker
    attack_utils = init_get_outputs(args_attack)
    # Init the attacked models
    attack_dataloader, test_dataloader, attgan, attgan_args, stargan_solver, attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()

    model_cases = [0, 1, 2, 3]
    import time
    start_time = time.time()

    # Some hyperparameters
    attack_utils.epsilon = 0.05
    reconstruct_feature_size = 32
    iteration_out = 30
    iteration_in = 10
    alpha = 1e-3
    momentum = 0
    # # 加载第一次训练的水印
    best_attack_performance = float('inf')
    # pgd
    attack_utils.up = attack_utils.up + torch.tensor(
        np.random.uniform(-attack_utils.epsilon, attack_utils.epsilon, attack_utils.up.shape).astype('float32')
    ).to(attack_utils.device)

    # best_attack_performance = float('inf')
    # trained_watermark = torch.load('/root/tf-logs/FOUND_code/pert_FOUND3--0.90.pt').to(attack_utils.device)

    # # 初始化 `attack_utils.up` 为已训练的水印
    # attack_utils.up = trained_watermark

    for t in range(iteration_out):
        print('%dth iter' % t)

        for idx, (img_a, att_a, c_org) in enumerate(tqdm(attack_dataloader)):
            print('%dth batch' % idx)
            if args_attack.global_settings.num_test is not None and idx * args_attack.global_settings.batch_size == args_attack.global_settings.num_test:
                break
            img_a = img_a.cuda() if args_attack.global_settings.gpu else img_a
            att_a = att_a.cuda() if args_attack.global_settings.gpu else att_a
            att_a = att_a.type(torch.float)

            if do_feature_ensemble:
                # Feature-Ensemble 在破坏攻击和平衡阶段使用
                for _ in range(iteration_in):
                    attack_utils.up.requires_grad = True
                    new_input = img_a + attack_utils.up
                    
                    # 1. 从深度伪造模型提取特征
                    middle_pairs = []
                    shuffle(model_cases)
                    for case in model_cases:
                        _, mid_pair = select_model_to_get_feature_pairs(case, new_input, img_a, reference, c_org, att_a,
                                                                    attack_utils, stargan_solver,
                                                                    attentiongan_solver, attgan, attgan_args,
                                                                    E, F, T, G, gen_models, reconstruct_feature_size)
                        middle_pairs.append(mid_pair)

                    clean_middle_from_models = [middle_pairs[p][0] for p in range(len(middle_pairs))]
                    adv_middle_from_models = [middle_pairs[q][1] for q in range(len(middle_pairs))]

                    # 合并所有特征
                    clean_features_cat = torch.cat(clean_middle_from_models, 1)
                    adv_features_cat = torch.cat(adv_middle_from_models, 1)
                    adv_features = enhance_feature_contrast(
                        torch.sum(clean_features_cat, 1), 
                        torch.sum(adv_features_cat, 1),
                        temperature=0.1,  # 降低温度以增强特征差异
                        alpha=0.6  # 适当调整增强强度
                    )


                    # 计算损失
                    
                    deepfake_loss1 = -1 * attack_utils.loss_fn(adv_features, torch.sum(clean_features_cat, 1))
                    deepfake_loss3= -1 * attack_utils.loss_fn(torch.sum(adv_features_cat, 1), torch.sum(clean_features_cat, 1))
                    # wasserstein = attack_utils.wasserstein_loss(torch.sum(adv_features_cat, 1), torch.sum(adv_features_cat, 1))
                    KL_loss= -1 * kl_divergence(torch.sum(adv_features_cat, 1), torch.sum(adv_features_cat, 1))
                    deepfake_loss = deepfake_loss3 + 0.1 * deepfake_loss1 + 0.1 * KL_loss
                    # # 6. 组合总损失
                    total_loss = deepfake_loss           # 深度伪造特征损失，权重调整
                   
                    # 7. 反向传播和优化
                    total_loss.backward()
                    
                    grad_c = attack_utils.up.grad.clone().to(attack_utils.device)
                    grad_c_hat = grad_c / (torch.mean(torch.abs(grad_c), (1, 2, 3), keepdim=True) + 1e-12)
                    attack_utils.up.grad.zero_()

                    attack_utils.up.data = attack_utils.up.data - alpha * torch.sign(grad_c_hat)
                    attack_utils.up.data = attack_utils.up.data.clamp(-attack_utils.epsilon, attack_utils.epsilon)
                    attack_utils.up = attack_utils.up.detach()
                    
                    

            if do_end_to_end:
                # End-to-End Ensemble
                attack_utils.up.requires_grad = True
                # new_input = img_a + attack_utils.up

                # # 1. 优化检测器特征
                # optimized_watermark, detector_loss = optimize_watermark_with_multi_detector_features(
                #     watermark=attack_utils.up,
                #     clean_images=img_a,
                #     detectors_dict=detectors_dict,
                #     device=device,
                #     pgd_steps=50,
                #     pgd_eps=0.3,
                #     pgd_lr=0.02,
                #     pgd_momentum=0.5,
                #     fusion_type="concat",
                #     feature_size=(64, 64)
                # )
                # attack_utils.up.data = optimized_watermark.data
                # attack_utils.up.data = torch.clamp(attack_utils.up.data, -attack_utils.epsilon, attack_utils.epsilon)


                # 2. 优化深度伪造特征
                new_input = img_a + attack_utils.up
                output_grads = []
                logit_pairs = []
                out_loss = 0
                
                # 梯度集成部分
                shuffle(model_cases)
                for case in model_cases:
                    shuffle(model_cases)
                    out_pairs = []
                    for model_case in model_cases:
                        out_pair, _ = select_model_to_get_feature_pairs(
                            model_case, new_input, img_a, reference, c_org, att_a,
                            attack_utils, stargan_solver, attentiongan_solver, attgan, 
                            attgan_args, E, F, T, G, gen_models, reconstruct_feature_size
                        )
                        out_pairs.append(out_pair)

                    # 合并所有模型的输出并计算损失
                    clean_outputs = [pair[0] for pair in out_pairs]
                    adv_outputs = [pair[1] for pair in out_pairs]
                    clean_outputs_cat = torch.cat(clean_outputs, 1)
                    adv_outputs_cat = torch.cat(adv_outputs, 1)

                    # 计算深度伪造损失
                    deepfake_loss = -1 * attack_utils.loss_fn(
                        torch.sum(adv_outputs_cat, 1), 
                        torch.sum(clean_outputs_cat, 1)
                    )
                    # deepfake_loss = -1 * frequency_domain_attack_loss(
                    #     torch.sum(adv_outputs_cat, 1), 
                    #     torch.sum(clean_outputs_cat, 1)
                    # )
                    
                    # cosine_loss = 1 - torch.nn.functional.cosine_similarity(
                    #     torch.sum(adv_outputs_cat, 1), 
                    #     torch.sum(clean_outputs_cat, 1)
                    # ).mean()

                    # 3. 组合损失 - end_to_end模式使用输出损失
                    loss_one =  deepfake_loss 
                        
                    
                    # 确保loss_one是tensor
                    if not isinstance(loss_one, torch.Tensor):
                        print(f"警告: loss_one不是tensor类型，正在转换...")
                        loss_one = torch.tensor(loss_one, device=device, requires_grad=True)

                    # 4. 根据不同的集成策略处理梯度
                    if do_grad_ensemble:
                        loss_one.backward(retain_graph=True)
                        grad_case = attack_utils.up.grad.clone().to(attack_utils.device)
                        grad_case = grad_case / (torch.mean(torch.abs(grad_case), (1, 2, 3), keepdim=True) + 1e-12)
                        output_grads.append(grad_case)
                        attack_utils.up.grad.zero_()
                    elif do_loss_ensemble:
                        out_loss += loss_one
                    elif do_output_ensemble:
                        logit_pairs.append(out_pair)

                # 5. 根据不同的集成策略更新水印
                if do_grad_ensemble:
                    grad_cout_hat = torch.mean(torch.stack(output_grads), dim=0)
                elif do_loss_ensemble:
                    out_loss.backward()
                    grad_cout_hat = attack_utils.up.grad.clone().to(attack_utils.device)
                    grad_cout_hat = grad_cout_hat / (torch.mean(torch.abs(grad_cout_hat), (1, 2, 3), keepdim=True) + 1e-12)
                elif do_output_ensemble:
                    clean_out = [logit_pairs[p][0] for p in range(len(logit_pairs))]
                    adv_out = [logit_pairs[q][1] for q in range(len(logit_pairs))]
                    clean_cat = torch.cat(clean_out, 1)
                    adv_cat = torch.cat(adv_out, 1)
                    logit_loss = -1 * attack_utils.loss_fn(torch.sum(adv_cat, 1), torch.sum(clean_cat, 1))
                    logit_loss.backward()
                    grad_cout_hat = attack_utils.up.grad.clone().to(attack_utils.device)
                    grad_cout_hat = grad_cout_hat / (torch.mean(torch.abs(grad_cout_hat), (1, 2, 3), keepdim=True) + 1e-12)

                # 6. 动量更新
                grad_cout_hat = grad_cout_hat + momentum * 0.8
                momentum = grad_cout_hat

                # 7. 更新水印
                attack_utils.up.data = attack_utils.up.data - alpha * torch.sign(grad_cout_hat)
                attack_utils.up.data = torch.clamp(attack_utils.up.data, -attack_utils.epsilon, attack_utils.epsilon)
                attack_utils.up = attack_utils.up.detach()

                # 8. 确保水印大小为1
                if attack_utils.up.size(0) > 1:
                    attack_utils.up = attack_utils.up.mean(dim=0, keepdim=True)

        print('up:', torch.max(attack_utils.up), torch.min(attack_utils.up))
        print(f"当前水印范围: [{attack_utils.up.min():.4f}, {attack_utils.up.max():.4f}]")
        
        # 每5次迭代评估一次效果
        if batch_evaluate and t % 2 == 0:
            print(f"\n[调试] 开始评估水印效果...")
            print(f"[调试] 当前水印维度: {attack_utils.up.shape}")
            print(f"[调试] 当前水印范围: [{attack_utils.up.min():.4f}, {attack_utils.up.max():.4f}]")

            
            _, _, _, _ = evaluate_multiple_models(args_attack, test_dataloader, attgan, attgan_args, stargan_solver,
                                                    attentiongan_solver,
                                                    transform, F, T, G, E, reference, gen_models, attack_utils,
                                                    max_samples=20)
        

            # duq_score, adv_loss, _, box_change, detector_results = test_watermark(
            #     watermark=attack_utils.up,
            #     test_dataloader=test_dataloader,
            #     detector_models=[detector for detector, _ in detectors_dict.values()],  # 使用所有检测器
            #     device=device,
            #     num_test=100
            # )

            # # 输出各个检测器的结果摘要
            # print("\n检测器结果摘要:")
            # for detector_name, results in detector_results.items():
            #     print(f"{detector_name}: F1={results['f1']:.4f}, DUQ={results['duq']:.4f}, 框数变化={results['box_change']:.2f}%")
            
            # # 记录当前水印的效果
            # if adv_loss < best_attack_performance:
            #     best_attack_performance = adv_loss

        # 定期清理缓存
        if t % 2 == 0:
            torch.cuda.empty_cache()

    end_time = time.time()
    print('训练耗时:', end_time - start_time)

    # 确保保存的水印大小为1
    if attack_utils.up.size(0) > 1:
        print(f"[调试] 保存前将水印大小调整为1: {attack_utils.up.size(0)} -> 1")
        attack_utils.up = attack_utils.up.mean(dim=0, keepdim=True)

    print(f"[调试] 保存水印形状: {attack_utils.up.shape}")
    torch.save(attack_utils.up, 'pert_FOUND31.pt')
    print("\n进行最终评估...")


    _, _, _, _ = evaluate_multiple_models(args_attack, test_dataloader, attgan, attgan_args, stargan_solver,
                                          attentiongan_solver,
                                          transform, F, T, G, E, reference, gen_models, attack_utils,
                                          max_samples=5000)

    # duq_score, adv_loss, _, box_change, detector_results = test_watermark(
    #     watermark=attack_utils.up,
    #     test_dataloader=test_dataloader,
    #     detector_models=[detector for detector, _ in detectors_dict.values()],  # 使用所有检测器
    #     device=device,
    #     num_test=1000
    # )

    # # 输出各个检测器的结果摘要
    # print("\n检测器结果摘要:")
    # for detector_name, results in detector_results.items():
    #     print(f"{detector_name}: F1={results['f1']:.4f}, DUQ={results['duq']:.4f}, 框数变化={results['box_change']:.2f}%")



if __name__ == "__main__":
    train_attacker()
