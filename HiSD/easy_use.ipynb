{"metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": 3}, "orig_nbformat": 2}, "nbformat": 4, "nbformat_minor": 2, "cells": [{"source": ["# import packages\n", "from core.utils import get_config\n", "from core.trainer import HiSD_Trainer\n", "import argparse\n", "import torchvision.utils as vutils\n", "import sys\n", "import torch\n", "import os\n", "from torchvision import transforms\n", "from PIL import Image\n", "import numpy as np\n", "import time\n", "import matplotlib.pyplot as plt"], "cell_type": "code", "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# use cpu by default\n", "# device = 'cuda:0' \n", "device = 'cpu'\n", "\n", "# load checkpoint\n", "config = get_config('configs/celeba-hq_256.yaml')\n", "noise_dim = config['noise_dim']\n", "image_size = config['new_size']\n", "checkpoint = 'checkpoint_256_celeba-hq.pt'\n", "trainer = HiSD_Trainer(config)\n", "state_dict = torch.load(checkpoint)\n", "trainer.models.gen.load_state_dict(state_dict['gen_test'])\n", "trainer.models.gen.to(device)\n", "\n", "E = trainer.models.gen.encode\n", "T = trainer.models.gen.translate\n", "G = trainer.models.gen.decode\n", "M = trainer.models.gen.map\n", "F = trainer.models.gen.extract\n", "\n", "transform = transforms.Compose([transforms.Resize(image_size),\n", "                                transforms.To<PERSON><PERSON><PERSON>(),\n", "                                transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Do the translation and plot the output.\n", "Every time you run this block will output a result with different bangs but reference's eyeglasses.\n", "\"\"\"\n", "def translate(input, steps):\n", "    x = transform(Image.open(input).convert('RGB')).unsqueeze(0).to(device)\n", "    c = E(x)\n", "    c_trg = c\n", "    for j in range(len(steps)):\n", "        step = steps[j]\n", "        if step['type'] == 'latent-guided':\n", "            if step['seed'] is not None:\n", "                torch.manual_seed(step['seed'])\n", "                torch.cuda.manual_seed(step['seed']) \n", "\n", "            z = torch.randn(1, noise_dim).to(device)\n", "            s_trg = M(z, step['tag'], step['attribute'])\n", "\n", "        elif step['type'] == 'reference-guided':\n", "            reference = transform(Image.open(step['reference']).convert('RGB')).unsqueeze(0).to(device)\n", "            s_trg = F(reference, step['tag'])\n", "        \n", "        c_trg = T(c_trg, s_trg, step['tag'])\n", "            \n", "    x_trg = G(c_trg)\n", "    output = x_trg.squeeze(0).cpu().permute(1, 2, 0).add(1).mul(1/2).clamp(0,1).numpy()\n", "    return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# You need to crop the image if you use your own input.\n", "input = 'examples/input_0.jpg'\n", "\n", "# e.g.1 change tag 'Bangs' to attribute 'with' using 3x latent-guided styles (generated by random noise). \n", "steps = [\n", "    {'type': 'latent-guided', 'tag': 0, 'attribute': 0, 'seed': None}\n", "]\n", "\n", "for i in range(3):\n", "    output = translate(input, steps)\n", "    plt.imshow(output, aspect='auto')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# You need to crop the image if you use your own input.\n", "input = 'examples/input_1.jpg'\n", "\n", "# e.g.2 change tag 'Glass<PERSON>' to attribute 'with' using reference-guided styles (extracted from another image). \n", "steps = [\n", "    {'type': 'reference-guided', 'tag': 1, 'reference': 'examples/reference_glasses_0.jpg'}\n", "]\n", "\n", "output = translate(input, steps)\n", "plt.imshow(output, aspect='auto')\n", "plt.show()\n", "\n", "steps = [\n", "    {'type': 'reference-guided', 'tag': 1, 'reference': 'examples/reference_glasses_1.jpg'}\n", "]\n", "\n", "output = translate(input, steps)\n", "plt.imshow(output, aspect='auto')\n", "plt.show()\n", "\n", "steps = [\n", "    {'type': 'reference-guided', 'tag': 1, 'reference': 'examples/reference_glasses_2.jpg'}\n", "]\n", "\n", "output = translate(input, steps)\n", "plt.imshow(output, aspect='auto')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# You need to crop the image if you use your own input.\n", "input = 'examples/input_2.jpg'\n", "\n", "# e.g.3 change tag '<PERSON><PERSON>' and '<PERSON><PERSON> 'to attribute 'with', 'Hair color' to 'black' during one translation. \n", "steps = [\n", "    {'type': 'reference-guided', 'tag': 0, 'reference': 'examples/reference_bangs_0.jpg'},\n", "    {'type': 'reference-guided', 'tag': 1, 'reference': 'examples/reference_glasses_0.jpg'},\n", "    {'type': 'latent-guided', 'tag': 2, 'attribute': 0, 'seed': None}\n", "]\n", "\n", "output = translate(input, steps)\n", "plt.imshow(output, aspect='auto')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to DIY your translation here. For example, can you remove one's glasses and bangs?"]}]}