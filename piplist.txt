appdirs==1.4.4
astor==0.8.1
audioread==2.1.9
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
backports.weakref==1.0rc1
beautifulsoup4 @ file:///home/<USER>/recipes/ci/beautifulsoup4_1610988766420/work
bleach==1.5.0
blessings==1.7
brotlipy==0.7.0
certifi==2020.12.5
cffi @ file:///tmp/build/80754af9/cffi_1613246939562/work
chardet==4.0.0
click==7.1.2
colorama==0.4.4
conda==4.10.1
conda-build==3.21.4
conda-package-handling @ file:///tmp/build/80754af9/conda-package-handling_1618262151086/work
contextlib2==0.6.0.post1
cryptography @ file:///tmp/build/80754af9/cryptography_1616769182610/work
cycler==0.10.0
dataclasses==0.6
decorator==4.4.2
dlib==19.21.1
dnspython==2.1.0
dominate==2.6.0
easydict==1.9
face-recognition==1.3.0
face-recognition-models==0.3.0
ffmpeg-python==0.2.0
filelock @ file:///home/<USER>/recipes/ci/filelock_1610993975404/work
future==0.18.2
fvcore==0.1.5.post20210727
glob2 @ file:///home/<USER>/recipes/ci/glob2_1610991677669/work
gpustat==0.6.0
h5py==2.10.0
html5lib==0.9999999
hyperopt==0.1.2
idna @ file:///tmp/build/80754af9/idna_1593446292537/work
imageio==2.9.0
imageio-ffmpeg==0.4.5
importlib-metadata==2.0.0
iopath==0.1.9
ipython @ file:///tmp/build/80754af9/ipython_1617118429768/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
jedi==0.17.0
Jinja2 @ file:///tmp/build/80754af9/jinja2_1621238361758/work
joblib==1.0.1
json-tricks==3.15.5
Keras==2.1.2
kiwisolver==1.3.1
libarchive-c @ file:///tmp/build/80754af9/python-libarchive-c_1617780486945/work
librosa==0.8.1
llvmlite==0.36.0
lpips==0.1.3
Markdown==3.3.4
MarkupSafe @ file:///tmp/build/80754af9/markupsafe_1621528142364/work
matplotlib==3.3.3
mkl-fft==1.3.0
mkl-random @ file:///tmp/build/80754af9/mkl_random_1618853974840/work
mkl-service==2.3.0
munch==2.5.0
natsort==8.0.0
netifaces==0.11.0
networkx==2.5
numba==0.53.1
numpy==1.19.2
nvidia-ml-py3==7.352.0
olefile==0.46
opencv-python==********
packaging==21.3
parso @ file:///tmp/build/80754af9/parso_1617223946239/work
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
Pillow==8.1.0
pkginfo==1.7.0
pooch==1.5.2
portalocker==2.3.0
prettytable==2.1.0
progress==1.5
prompt-toolkit @ file:///tmp/build/80754af9/prompt-toolkit_1616415428029/work
protobuf==3.14.0
psutil @ file:///tmp/build/80754af9/psutil_1612298016854/work
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1594388511720/work
Pygments @ file:///tmp/build/80754af9/pygments_1621606182707/work
pymongo==3.11.4
pyOpenSSL @ file:///tmp/build/80754af9/pyopenssl_1605545627475/work
pyparsing==2.4.7
PySocks @ file:///tmp/build/80754af9/pysocks_1594394576006/work
python-dateutil==2.8.1
python-etcd==0.4.5
PythonWebHDFS==0.2.3
pytorch-fid==0.2.1
pytz @ file:///tmp/build/80754af9/pytz_1612215392582/work
PyWavelets==1.1.1
PyYAML==5.4.1
requests==2.25.1
resampy==0.2.2
responses==0.13.3
ruamel-yaml-conda @ file:///tmp/build/80754af9/ruamel_yaml_1616016701961/work
ruamel.yaml==0.17.7
ruamel.yaml.clib==0.2.2
schema==0.7.4
scikit-image==0.17.2
scikit-learn==0.24.2
scipy==1.5.2
setGPU==0.0.7
simplejson==3.17.2
six==1.15.0
SoundFile==0.10.3.post1
soupsieve @ file:///tmp/build/80754af9/soupsieve_1616183228191/work
tabulate==0.8.9
tensorboardX==2.6
termcolor==1.1.0
thop==0.0.31.post2005241907
threadpoolctl==2.1.0
tifffile==2020.9.3
torch==1.9.1+cu111
torch-fidelity==0.2.0
torchaudio==0.9.1
torchelastic==0.2.0
torchsummary==1.5.1
torchtext==0.10.0
torchvision==0.10.1+cu111
tqdm==4.56.0
traitlets @ file:///home/<USER>/src/ci/traitlets_1611929699868/work
typing-extensions @ file:///home/<USER>/src/ci_mi/typing_extensions_1612808209620/work
urllib3==1.26.5
wcwidth @ file:///tmp/build/80754af9/wcwidth_1593447189090/work
websockets==9.1
Werkzeug==1.0.1
yacs==0.1.8
zipp==3.4.0
