import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import numpy as np
from tqdm import tqdm
import argparse
import json
import os
from os.path import join

# Attempt to import from existing project structure
try:
    from train_FOUND1 import init_detectors, test_watermark, calculate_iou, prepare, parse
    # FEATURE_LAYER_CONFIG is no longer needed for this direct attack style
    # from face_poison_integration import FEATURE_LAYER_CONFIG 
except ImportError:
    print("WARN: Could not import from train_FOUND1.py. Ensure it's in PYTHONPATH or copy necessary functions/variables here.")
    def init_detectors(device): raise NotImplementedError("init_detectors not found")
    def test_watermark(*args, **kwargs): raise NotImplementedError("test_watermark not found")
    def calculate_iou(box1, box2): raise NotImplementedError("calculate_iou not found")
    def prepare(): raise NotImplementedError("prepare not found")
    def parse(args=None):
        print("WARN: Using a default dummy args_attack due to parse() not being found.")
        return argparse.Namespace(global_settings=argparse.Namespace(gpu=torch.cuda.is_available()), attacks=argparse.Namespace())

# make_one_hot is not used in this version
# def make_one_hot(labels, C, device): ...

class DagAttack:
    def __init__(self, detector_wrapper, epsilon=16/255,
                 iterations=10, device='cuda',
                 step_size_scale_factor=1.0,
                 num_target_anchors_k=20):
        """
        实现对抗攻击，针对不同检测器使用专门的损失函数
        
        :param detector_wrapper: 目标人脸检测器模型包装器
        :param epsilon: L∞扰动边界
        :param iterations: 攻击迭代次数
        :param device: 计算设备
        :param step_size_scale_factor: 控制步长大小，dag_step_size = self.epsilon * step_size_scale_factor
        :param num_target_anchors_k: 目标锚点数量。如果为0，使用所有锚点计算损失
        """
        self.detector_wrapper = detector_wrapper 
        self.epsilon = epsilon # L-infinity约束
        
        # 确定实际的模型
        self.model_to_attack = None
        if hasattr(self.detector_wrapper, 'net'): # RetinaFaceDetector
            self.model_to_attack = self.detector_wrapper.net
        elif hasattr(self.detector_wrapper, 'model'): # DSFDDetector
            self.model_to_attack = self.detector_wrapper.model
        else: # S3FD (直接传递) 或其他直接模型
            self.model_to_attack = self.detector_wrapper
        self.model_to_attack = self.model_to_attack.to(device)
        
        self.iterations = iterations
        self.device = device
        self.dag_step_size = self.epsilon * step_size_scale_factor
        self.num_target_anchors_k = num_target_anchors_k
        
        # 检测器类型
        self.detector_type = self._determine_detector_type()
        
    def _determine_detector_type(self):
        """判断检测器类型，以便使用专门的损失函数"""
        detector_class_name = self.detector_wrapper.__class__.__name__
        if "S3FD" in detector_class_name:
            return "s3fd"
        elif "DSFD" in detector_class_name:
            return "dsfd"
        elif "RetinaFace" in detector_class_name:
            return "retinaface"
        else:
            return "unknown"

    # 预处理方法 (保持兼容性)
    def _preprocess_retinaface_input(self, image_rgb_0_to_1_bhwc_tensor):
        if image_rgb_0_to_1_bhwc_tensor.dim() != 4: raise ValueError("Input tensor must be 4D (B,H,W,C)")
        if image_rgb_0_to_1_bhwc_tensor.size(3) != 3: raise ValueError("Input tensor must have 3 channels (C=3)")
        img_tensor = image_rgb_0_to_1_bhwc_tensor.permute(0, 3, 1, 2) 
        img_tensor = img_tensor.to(self.device)
        img_tensor_0_255 = img_tensor * 255.0
        mean_bgr_numpy = np.array([104, 117, 123], dtype=np.float32)
        mean_bgr_tensor = torch.tensor(mean_bgr_numpy, dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)
        img_bgr_0_255 = img_tensor_0_255[:, [2, 1, 0], :, :] 
        img_bgr_mean_sub = img_bgr_0_255 - mean_bgr_tensor
        return img_bgr_mean_sub

    def _preprocess_s3fd_input(self, image_rgb_0_to_1_bhwc_tensor):
        if image_rgb_0_to_1_bhwc_tensor.dim() != 4: raise ValueError("Input tensor must be 4D (B,H,W,C)")
        if image_rgb_0_to_1_bhwc_tensor.size(3) != 3: raise ValueError("Input tensor must have 3 channels (C=3)")
        img_tensor = image_rgb_0_to_1_bhwc_tensor.permute(0, 3, 1, 2)
        img_tensor = img_tensor.to(self.device)
        img_tensor_0_255 = img_tensor * 255.0
        mean_bgr_numpy = np.array([104, 117, 123], dtype=np.float32)
        mean_bgr_tensor = torch.tensor(mean_bgr_numpy, dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)
        img_bgr_0_255 = img_tensor_0_255[:, [2, 1, 0], :, :]
        img_bgr_mean_sub = img_bgr_0_255 - mean_bgr_tensor
        return img_bgr_mean_sub

    def _preprocess_dsfd_input(self, image_rgb_0_to_1_bhwc_tensor):
        if image_rgb_0_to_1_bhwc_tensor.dim() != 4: raise ValueError("Input tensor must be 4D (B,H,W,C)")
        if image_rgb_0_to_1_bhwc_tensor.size(3) != 3: raise ValueError("Input tensor must have 3 channels (C=3)")
        img_tensor = image_rgb_0_to_1_bhwc_tensor.permute(0, 3, 1, 2)
        img_tensor = img_tensor.to(self.device)
        img_tensor_0_255 = img_tensor * 255.0
        mean_bgr_numpy = np.array([104, 117, 123], dtype=np.float32)
        mean_bgr_tensor = torch.tensor(mean_bgr_numpy, dtype=img_tensor_0_255.dtype, device=self.device).view(1, 3, 1, 1)
        img_bgr_0_255 = img_tensor_0_255[:, [2, 1, 0], :, :]
        img_bgr_mean_sub = img_bgr_0_255 - mean_bgr_tensor
        return img_bgr_mean_sub

    def _get_preprocessed_input(self, image_0_to_1_bchw_rgb):
        image_0_to_1_bhwc_rgb = image_0_to_1_bchw_rgb.permute(0, 2, 3, 1)
        # Use detector_wrapper name to decide preprocessing, as model_to_attack might be a generic nn.Module
        detector_class_name = self.detector_wrapper.__class__.__name__
        if "RetinaFaceDetector" in detector_class_name:
            return self._preprocess_retinaface_input(image_0_to_1_bhwc_rgb)
        elif detector_class_name == "S3FD":
            return self._preprocess_s3fd_input(image_0_to_1_bhwc_rgb)
        elif "DSFDDetector" in detector_class_name:
            return self._preprocess_dsfd_input(image_0_to_1_bhwc_rgb)
        else: # Fallback: assume input is already correctly preprocessed or doesn't need BGR conversion / mean sub
            # print(f"Warning: Unknown detector type \'{detector_class_name}\' for specific preprocessing in DagAttack. Using raw BCHW RGB input for the model.")
            return image_0_to_1_bchw_rgb 
    
    def _extract_face_confidences(self, outputs, detector_class_name_for_logic):
        """Helper to extract face confidences from model outputs."""
        face_confidences = None
        if "RetinaFaceDetector" in detector_class_name_for_logic:
            if isinstance(outputs, tuple) and len(outputs) >= 2:
                conf_data = outputs[1] 
                if conf_data.dim() == 3 and conf_data.size(2) >= 2:
                     face_confidences = conf_data[:, :, 1] 
                else:
                    print(f"Warning: RetinaFace conf_data unexpected shape: {conf_data.shape}")
            else:
                print(f"Warning: RetinaFace output unexpected structure: {type(outputs)}")

        elif detector_class_name_for_logic == "S3FD" or "DSFDDetector" in detector_class_name_for_logic:
             # 处理S3FD/DSFD检测器的输出
             # 首先检查是list还是tuple
             if isinstance(outputs, tuple) and len(outputs) >= 2:
                conf_data = outputs[1]
                if conf_data.dim() == 3 and conf_data.size(2) >=2:
                    face_confidences = conf_data[:, :, 1]
                else:
                    print(f"Warning: S3FD/DSFD conf_data unexpected shape: {conf_data.shape}")
             elif isinstance(outputs, list):
                # DSFD可能返回list形式的输出
                if len(outputs) > 0 and isinstance(outputs[0], dict):
                    # 检查列表中是否包含字典，其中可能有'scores'键
                    for output_dict in outputs:
                        if 'scores' in output_dict and isinstance(output_dict['scores'], torch.Tensor):
                            # 使用scores作为face_confidences
                            scores = output_dict['scores']
                            if scores.numel() > 0:
                                if face_confidences is None:
                                    face_confidences = scores
                                else:
                                    face_confidences = torch.cat([face_confidences, scores])
                if face_confidences is None:
                    print(f"Warning: Could not extract face confidences from list output: {outputs}")
             else:
                print(f"Warning: S3FD/DSFD output unexpected structure: {type(outputs)}")
        else:
            print(f"Warning: Unknown detector type \'{detector_class_name_for_logic}\' for confidence extraction. Will result in zero loss.")
        
        if face_confidences is None:
            # Return a tensor that can still be used in .mean() or selection, but will lead to 0 loss or no selection
            return torch.tensor([], device=self.device) 
        return face_confidences

    def _attack_s3fd_specific(self, processed_adv_input):
        """针对S3FD的特定攻击方法，使用对raw conv3_3_conf的直接攻击"""
        try:
            # 获取特征，包括conv3_3_raw_conf
            import S3FDDetector as sfd_detector_module
            
            # 修复: 检查get_features是否返回2个或3个值
            try:
                result = sfd_detector_module.get_features(self.model_to_attack, processed_adv_input)
                
                # 检查结果包含多少个元素
                if isinstance(result, tuple) and len(result) >= 3:
                    # 原始预期: (selected_features, final_outputs, conv3_3_raw_conf)
                    selected_features, outputs, conv3_3_raw_conf = result
                elif isinstance(result, tuple) and len(result) == 2:
                    # 可能只返回了2个值: (selected_features, final_outputs)
                    selected_features, outputs = result
                    # 没有conv3_3_raw_conf，使用备选方案
                    conv3_3_raw_conf = None
                    # print("Warning: S3FD get_features返回2个值而非3个，无法获取conv3_3_raw_conf")
                else:
                    # 单个返回值或其他情况
                    # print(f"Warning: S3FD get_features返回了意外的结构: {type(result)}")
                    selected_features = result if isinstance(result, list) else None
                    outputs = None
                    conv3_3_raw_conf = None
            except ValueError as e:
                # 处理解包错误
                # print(f"Error unpacking S3FD get_features result: {e}")
                # 尝试直接调用模型获取输出
                outputs = self.model_to_attack(processed_adv_input)
                selected_features = None
                conv3_3_raw_conf = None
            
            # 如果无法获取conv3_3_raw_conf，回退到常规置信度攻击
            if conv3_3_raw_conf is None or conv3_3_raw_conf.numel() == 0:
                # print("Warning: S3FD的conv3_3_raw_conf为空，使用通用损失函数")
                # 尝试从模型输出中提取置信度
                if outputs is None:
                    outputs = self.model_to_attack(processed_adv_input)
                
                face_confidences = self._extract_face_confidences(outputs, "S3FD")
                # 若无法提取特定特征，返回通用损失
                return face_confidences.mean() if face_confidences.numel() > 0 else torch.tensor(0.0, device=self.device, requires_grad=True), outputs

            # 确保需要梯度
            if not conv3_3_raw_conf.requires_grad:
                conv3_3_raw_conf = conv3_3_raw_conf.clone().requires_grad_(True)
                
            # 取出人脸logits (索引3) 和背景logits (索引0-2)
            face_logits_c3 = conv3_3_raw_conf[:, 3, :, :]      # 形状 [B, H, W]
            bg_logits_c3_all = conv3_3_raw_conf[:, 0:3, :, :]  # 形状 [B, 3, H, W]
            max_bg_logits_c3, _ = torch.max(bg_logits_c3_all, dim=1)   # 形状 [B, H, W]

            # 计算目标：让背景分数大于人脸分数
            s3fd_conv3_3_objective = torch.mean(max_bg_logits_c3 - face_logits_c3)
            
            # 这个损失是关键，直接针对模型的预测机制
            return s3fd_conv3_3_objective, outputs
        except Exception as e:
            print(f"S3FD特定攻击出错: {e}")
            import traceback
            traceback.print_exc()
            # 失败时返回零损失和None输出
            return torch.tensor(0.0, device=self.device, requires_grad=True), None

    def _attack_dsfd_specific(self, processed_adv_input):
        """针对DSFD的特定攻击方法，使用钩子捕获内部特征并确保梯度传播"""
        original_training_state = self.model_to_attack.training
        # 强制训练模式以保留梯度
        self.model_to_attack.train()
        
        # 存储用于钩子函数
        captured_features = []
        hooks = []
        
        try:
            # 确保输入需要梯度
            if not processed_adv_input.requires_grad:
                processed_adv_input.requires_grad_(True)
            
            # 为DSFD模型注册钩子函数，捕获VGG特征提取器的输出
            # DSFD基于VGG16，我们关注早期和中间的特征层
            try:
                # 尝试钩住DSFD的VGG特征提取器
                if hasattr(self.model_to_attack, 'vgg'):
                    target_layers = [14, 21, 28]  # 对应于conv3_3, conv4_3, conv5_3
                    
                    def hook_fn(module, input, output):
                        # 确保输出有梯度
                        if not output.requires_grad:
                            output.requires_grad_(True)
                        captured_features.append(output)
                    
                    for layer_idx in target_layers:
                        if layer_idx < len(self.model_to_attack.vgg):
                            hooks.append(self.model_to_attack.vgg[layer_idx].register_forward_hook(hook_fn))
                        else:
                            print(f"DSFD: VGG层索引{layer_idx}超出范围")
                else:
                    print("DSFD模型没有预期的'vgg'属性")
            except Exception as e:
                print(f"注册DSFD钩子函数失败: {e}")
            
            # 执行前向传播，触发钩子函数
            # 创建一个简单表达式确保梯度流可以传递到输入
            zero_loss = (processed_adv_input.sum() * 0).requires_grad_(True)
            outputs_adv = self.model_to_attack(processed_adv_input)
            
            # 如果捕获到特征，使用它们创建有意义的损失
            if captured_features:
                # print(f"成功捕获了{len(captured_features)}个DSFD内部特征")
                # 选择第一个捕获的特征（较早的特征层）
                feature_to_attack = captured_features[0]
                
                # 使用Feature Suppression Attack，通过最小化特征激活来减少人脸检测能力
                # 增加系数放大损失，使梯度更显著
                feature_suppression_loss = feature_to_attack.abs().mean() * 5.0
                
                # 清理钩子
                for hook in hooks:
                    hook.remove()
                
                self.model_to_attack.train(original_training_state)
                return -1.0 * feature_suppression_loss, outputs_adv
            
            # 尝试直接从最终输出中提取梯度信息
            try:
                # 检查DSFD模型的头部模块
                head_modules = []
                if hasattr(self.model_to_attack, 'loc'):
                    head_modules.append(('loc', self.model_to_attack.loc))
                if hasattr(self.model_to_attack, 'conf'):
                    head_modules.append(('conf', self.model_to_attack.conf))
                
                if head_modules:
                    # 获取内部模块的参数梯度
                    grad_loss = torch.tensor(0., device=self.device, requires_grad=True)
                    for name, module in head_modules:
                        # 获取模块参数并创建梯度路径
                        for param in module.parameters():
                            if param.requires_grad:
                                param_sum = param.abs().sum()
                                grad_loss = grad_loss + (param_sum * 0)  # 保持梯度路径
                    
                    # 创建一个输入相关的虚拟损失
                    # 这确保了梯度可以流回到输入
                    input_grad_loss = (processed_adv_input * grad_loss).sum() * 5.0
                    
                    # 清理钩子
                    for hook in hooks:
                        hook.remove()
                    
                    self.model_to_attack.train(original_training_state)
                    return -1.0 * input_grad_loss, outputs_adv
            except Exception as e:
                print(f"提取DSFD头部梯度失败: {e}")
            
            # 最后尝试：直接连接到输入的损失
            # 增加系数放大损失
            direct_input_loss = processed_adv_input.abs().mean() * 10.0
            
            # 清理钩子
            for hook in hooks:
                hook.remove()
            
            self.model_to_attack.train(original_training_state)
            return -1.0 * direct_input_loss, outputs_adv
            
        except Exception as e:
            print(f"DSFD特定攻击处理中发生严重错误: {e}")
            import traceback
            traceback.print_exc()
            
            # 清理钩子
            for hook in hooks:
                hook.remove()
                
            self.model_to_attack.train(original_training_state)
            # 返回一个能确保梯度路径的零损失，增加系数
            return -1.0 * (processed_adv_input.abs().sum() * 0.1), None
        finally:
            # 清理所有钩子
            for hook in hooks:
                hook.remove()
            self.model_to_attack.train(original_training_state) # 确保最终恢复状态

    def attack(self, image_0_to_1_bchw_rgb, verbose=False):
        """
        进行对抗攻击，针对不同检测器使用专门的损失函数
        输入图像格式为(B,C,H,W)，范围[0,1]，RGB顺序
        返回对抗样本，格式同输入
        """
        image_orig_for_proj = image_0_to_1_bchw_rgb.clone().detach()
        adv_image_bchw_rgb = image_0_to_1_bchw_rgb.clone().detach()
        
        self.model_to_attack.eval()
        detector_class_name = self.detector_wrapper.__class__.__name__

        target_anchor_indices = None
        if self.num_target_anchors_k > 0:
            with torch.no_grad():
                processed_orig_input = self._get_preprocessed_input(image_orig_for_proj)
                try:
                    # 通用方法处理所有检测器类型
                    if self.detector_type == "s3fd":
                        import S3FDDetector as sfd_detector_module
                        # S3FD模型在评估模式下获取输出，用于选择目标锚点
                        self.model_to_attack.eval() # 确保在eval模式获取干净输出
                        s3fd_get_features_result_clean = sfd_detector_module.get_features(self.model_to_attack, processed_orig_input)
                        # self.model_to_attack.train() # 如果后续有需要train模式的操作，这里可以恢复，但当前上下文是no_grad
                        
                        if isinstance(s3fd_get_features_result_clean, tuple) and len(s3fd_get_features_result_clean) >=2:
                            # get_features可能返回 (selected_features, final_outputs) 或 (selected_features, final_outputs, conv3_3_raw_conf)
                            # 我们只需要final_outputs进行置信度提取
                            orig_outputs = s3fd_get_features_result_clean[1] # final_outputs在第二个位置
                            # if verbose: print(f"  Clean pass S3FD get_features returned {len(s3fd_get_features_result_clean)} values, took final_outputs.")
                        else:
                            # if verbose: print(f"  Clean pass S3FD get_features returned unexpected structure: {type(s3fd_get_features_result_clean)}. Falling back.")
                            self.model_to_attack.eval() # 确保eval模式
                            orig_outputs = self.model_to_attack(processed_orig_input) # Fallback

                    elif self.detector_type == "dsfd":
                        self.model_to_attack.eval() # 确保在eval模式获取干净输出
                        orig_outputs = self.model_to_attack(processed_orig_input)
                    else:
                        self.model_to_attack.eval() # 确保在eval模式获取干净输出
                        orig_outputs = self.model_to_attack(processed_orig_input)
                        
                    orig_face_confidences = self._extract_face_confidences(orig_outputs, detector_class_name)
                    
                    if orig_face_confidences.numel() > 0 and orig_face_confidences.dim() > 1:
                        scores_for_topk = orig_face_confidences.squeeze(0)
                        if scores_for_topk.numel() > 0:
                             actual_k = min(self.num_target_anchors_k, scores_for_topk.size(0))
                             if actual_k > 0:
                                 _, target_anchor_indices = torch.topk(scores_for_topk, k=actual_k)
                                 if verbose: 
                                     print(f"  Targeting Top-{actual_k} anchors based on clean image confidences.")
                        else:
                            if verbose: print("  Clean image processing yielded no anchor scores for targeting.")
                    else:
                        if verbose: print("  Clean image processing yielded no/empty face confidences for targeting.")
                except Exception as e:
                    if verbose: print(f"  Error during clean image pass for anchor targeting: {e}")
        
        if target_anchor_indices is not None and target_anchor_indices.numel() == 0:
            target_anchor_indices = None
            if verbose: print("  No target anchors identified. Will use all anchors for loss.")

        for iter_num in range(self.iterations):
            adv_image_bchw_rgb.requires_grad = True
            
            processed_adv_input = self._get_preprocessed_input(adv_image_bchw_rgb)
            
            # --- 针对不同检测器使用不同损失函数 ---
            try:
                if self.detector_type == "s3fd":
                    try:
                        # S3FD专用攻击，直接针对conv3_3_raw_conf
                        loss, outputs_adv = self._attack_s3fd_specific(processed_adv_input)
                        # if verbose: print(f"Iter {iter_num}: S3FD专用损失={loss.item():.4f}")
                    except Exception as e:
                        # print(f"S3FD专用攻击失败，回退到通用方法: {e}")
                        outputs_adv = self.model_to_attack(processed_adv_input)
                        adv_face_confidences = self._extract_face_confidences(outputs_adv, detector_class_name)
                        loss = adv_face_confidences.mean() if adv_face_confidences.numel() > 0 else torch.tensor(0.0, device=self.device, requires_grad=True)
                    
                elif self.detector_type == "dsfd":
                    try:
                        # DSFD专用攻击
                        loss, outputs_adv = self._attack_dsfd_specific(processed_adv_input)
                        # if verbose: print(f"Iter {iter_num}: DSFD专用损失={loss.item():.4f}")
                    except Exception as e:
                        # print(f"DSFD专用攻击失败，回退到通用方法: {e}")
                        outputs_adv = self.model_to_attack(processed_adv_input)
                        adv_face_confidences = self._extract_face_confidences(outputs_adv, detector_class_name)
                        # 确保通用方法下的损失也有梯度
                        if adv_face_confidences.numel() > 0 and adv_face_confidences.requires_grad:
                            loss = adv_face_confidences.mean()
                        elif adv_face_confidences.numel() > 0: # 有信度但无梯度
                            loss = adv_face_confidences.mean().requires_grad_(True)
                        else: # 无信度
                            loss = (processed_adv_input.sum() * 0).requires_grad_(True) # fallback loss with grad
                    
                else:
                    # 通用检测器攻击 (RetinaFace等)
                    outputs_adv = self.model_to_attack(processed_adv_input)
                    adv_face_confidences = self._extract_face_confidences(outputs_adv, detector_class_name)
                    
                    if adv_face_confidences.numel() == 0:
                        # if verbose: print(f"Iter {iter_num}: 未找到人脸置信度，损失为零")
                        loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                    elif target_anchor_indices is not None:
                        # 仅针对目标锚点
                        selected_confidences = adv_face_confidences.squeeze(0)[target_anchor_indices]
                        if selected_confidences.numel() > 0:
                            loss = selected_confidences.mean()
                            # if verbose: print(f"Iter {iter_num}: 目标锚点平均置信度损失={loss.item():.4f}")
                        else:
                            # if verbose: print(f"Iter {iter_num}: 未找到目标锚点置信度，使用所有锚点")
                            loss = adv_face_confidences.mean()
                    else:
                        # 使用所有锚点
                        loss = adv_face_confidences.mean()
                        # if verbose: print(f"Iter {iter_num}: 所有锚点平均置信度损失={loss.item():.4f}")
                        
            except Exception as e:
                print(f"损失计算出错: {e}")
                import traceback
                traceback.print_exc()
                loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                
            if adv_image_bchw_rgb.grad is not None:
                adv_image_bchw_rgb.grad.zero_()
            
            loss.backward()
            
            if adv_image_bchw_rgb.grad is None:
                # if verbose: print(f"Iter {iter_num}: 损失梯度为None，跳过更新")
                adv_image_bchw_rgb = adv_image_bchw_rgb.detach()
                continue
                
            grad = adv_image_bchw_rgb.grad.clone()
            grad_mag = grad.norm()

            if grad_mag < 1e-12:
                # if verbose: print(f"Iter {iter_num}: 损失梯度接近零 ({grad_mag.item()}). 损失: {loss.item()}")
                adv_image_bchw_rgb = adv_image_bchw_rgb.detach()
                if loss.item() < 0.01 and iter_num > 0:
                    # if verbose: print(f"  损失 ({loss.item()}) 已经很低，提前停止")
                    break 
                continue 
            
            # --- DAG风格迭代更新 ---
            # 我们要最小化损失，所以在梯度的负方向上移动
            step_perturbation = (self.dag_step_size / grad_mag) * grad 
            adv_image_bchw_rgb = adv_image_bchw_rgb.detach() - step_perturbation.detach() # 减法表示最小化
            
            # 投影到原始图像的L-infinity球内
            adv_image_bchw_rgb = torch.max(adv_image_bchw_rgb, image_orig_for_proj - self.epsilon)
            adv_image_bchw_rgb = torch.min(adv_image_bchw_rgb, image_orig_for_proj + self.epsilon)
            adv_image_bchw_rgb = torch.clamp(adv_image_bchw_rgb, 0, 1)

            # if verbose:
            #     print(f"  Iter {iter_num}: GradMag={grad_mag.item():.4f}, StepMax={step_perturbation.abs().max().item():.4f}")
        
        return adv_image_bchw_rgb.detach()

class UniversalDagAttack:
    """
    实现针对多个人脸检测器的通用水印攻击
    优化一个统一的水印，使其对多个检测器都有较好的攻击效果
    """
    def __init__(self, detectors_dict, epsilon=16/255, 
                 iterations=40, device='cuda', 
                 momentum=0.9, lambda_weights=None):
        """
        初始化通用水印攻击
        
        :param detectors_dict: 检测器字典，格式为 {detector_name: (detector_model, detector_type_str)}
        :param epsilon: L∞扰动边界
        :param iterations: 攻击迭代次数
        :param device: 计算设备
        :param momentum: 梯度动量系数
        :param lambda_weights: 各检测器的损失权重，可以是字典 {detector_name: weight}
        """
        self.detectors_dict = detectors_dict
        self.epsilon = epsilon
        self.iterations = iterations
        self.device = device
        self.momentum = momentum
        
        detector_names = list(detectors_dict.keys())
        
        # 处理损失权重
        if lambda_weights is None:
            # 默认使用相等权重
            self.lambda_weights = {name: 1.0 / len(detectors_dict) for name in detector_names}
        elif isinstance(lambda_weights, dict):
            # 使用提供的字典权重
            self.lambda_weights = lambda_weights
        else:
            # 其他情况，使用相等权重
            self.lambda_weights = {name: 1.0 / len(detectors_dict) for name in detector_names}
        
        # 为每个检测器创建一个DagAttack实例
        self.dag_attackers = {}
        for detector_name, (detector_model, _) in detectors_dict.items():
            # 针对不同检测器类型使用不同的参数
            step_size_scale_factor = 0.25 if "dsfd" in detector_name.lower() else 0.125
            attacker_iterations = min(20, iterations)  # 单个攻击器的迭代次数限制在20以内
            
            self.dag_attackers[detector_name] = DagAttack(
                detector_wrapper=detector_model,
                epsilon=epsilon,
                iterations=attacker_iterations,
                device=device,
                step_size_scale_factor=step_size_scale_factor,
                num_target_anchors_k=20  # 使用前20个最高置信度的锚点
            )
        
        print(f"初始化通用DAG水印攻击: epsilon={epsilon}, iterations={iterations}")
        print(f"使用 {len(detectors_dict)} 个检测器，权重为: {self.lambda_weights}")
    
    def attack(self, image_0_to_1_bchw_rgb, universal_watermark=None, verbose=False):
        """
        优化通用水印，使其对所有检测器都有效
        
        :param image_0_to_1_bchw_rgb: 输入图像，格式为[B,C,H,W]，范围[0,1]，RGB顺序
        :param universal_watermark: 初始通用水印。如果为None，将创建一个新的
        :param verbose: 是否输出详细信息
        :return: 优化后的通用水印
        """
        # 确保图像在正确的设备上
        image_0_to_1 = image_0_to_1_bchw_rgb.clone().to(self.device)
        batch_size, channels, height, width = image_0_to_1.shape
        
        # 初始化或使用已有的通用水印
        if universal_watermark is None:
            # 创建一个新的随机水印
            universal_watermark = torch.zeros_like(image_0_to_1, device=self.device)
            # 添加小的随机初始化，有助于优化
            with torch.no_grad():
                universal_watermark += (torch.rand_like(image_0_to_1) * 2 - 1) * self.epsilon * 0.1
        else:
            # 确保水印在正确的设备上并且形状正确
            if universal_watermark.shape != image_0_to_1.shape:
                # 调整水印大小以匹配图像
                if universal_watermark.dim() == 4:  # [B,C,H,W]
                    # 取第一个batch元素
                    universal_watermark = universal_watermark[0].unsqueeze(0)
                    
                # 如果维度和通道数一致但大小不同，调整大小
                if universal_watermark.dim() == image_0_to_1.dim() and universal_watermark.size(1) == channels:
                    # 使用插值调整大小
                    universal_watermark = F.interpolate(
                        universal_watermark, 
                        size=(height, width), 
                        mode='bilinear', 
                        align_corners=False
                    )
            universal_watermark = universal_watermark.to(self.device)
        
        # 初始化梯度累积和动量
        grad_accumulator = torch.zeros_like(universal_watermark)
        momentum_buffer = torch.zeros_like(universal_watermark)
        
        # 当前图像的原始内容
        original_image = image_0_to_1.clone()
        
        # 迭代优化通用水印
        for iter_num in range(self.iterations):
            # 应用当前水印生成对抗样本
            with torch.no_grad():
                adv_image = torch.clamp(original_image + universal_watermark, 0, 1)
            
            # 计算对所有检测器的总损失
            total_loss = torch.tensor(0.0, device=self.device)
            detector_losses = {}
            
            # 确保水印需要梯度
            universal_watermark.requires_grad_(True)
            if universal_watermark.grad is not None:
                universal_watermark.grad.zero_()
            
            # 对每个检测器计算损失
            for detector_name, (detector_model, detector_type_str) in self.detectors_dict.items():
                try:
                    # 获取该检测器的DagAttack实例
                    dag_attacker = self.dag_attackers[detector_name]
                    
                    # 应用当前水印到图像
                    adv_image_with_grad = original_image + universal_watermark
                    adv_image_with_grad = torch.clamp(adv_image_with_grad, 0, 1)
                    
                    # 使用检测器特定的预处理
                    processed_adv_input = None
                    detector_class_name = detector_model.__class__.__name__
                    
                    if "RetinaFace" in detector_class_name:
                        processed_adv_input = dag_attacker._preprocess_retinaface_input(
                            adv_image_with_grad.permute(0, 2, 3, 1)
                        )
                    elif "S3FD" in detector_class_name:
                        processed_adv_input = dag_attacker._preprocess_s3fd_input(
                            adv_image_with_grad.permute(0, 2, 3, 1)
                        )
                    elif "DSFD" in detector_class_name:
                        processed_adv_input = dag_attacker._preprocess_dsfd_input(
                            adv_image_with_grad.permute(0, 2, 3, 1)
                        )
                    else:
                        # 默认预处理
                        processed_adv_input = adv_image_with_grad
                    
                    # 计算损失，使用DagAttack中针对每种检测器的特定方法
                    if "S3FD" in detector_class_name:
                        detector_loss, _ = dag_attacker._attack_s3fd_specific(processed_adv_input)
                    elif "DSFD" in detector_class_name:
                        detector_loss, _ = dag_attacker._attack_dsfd_specific(processed_adv_input)
                    else:
                        # 通用检测器处理 (如RetinaFace)
                        model_to_attack = None
                        if hasattr(detector_model, 'net'):
                            model_to_attack = detector_model.net
                        elif hasattr(detector_model, 'model'):
                            model_to_attack = detector_model.model
                        else:
                            model_to_attack = detector_model
                        
                        outputs_adv = model_to_attack(processed_adv_input)
                        adv_face_confidences = dag_attacker._extract_face_confidences(outputs_adv, detector_class_name)
                        
                        if adv_face_confidences.numel() > 0:
                            detector_loss = adv_face_confidences.mean()
                        else:
                            detector_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                    
                    # 应用检测器权重
                    weight = self.lambda_weights.get(detector_name, 1.0)
                    weighted_loss = weight * detector_loss
                    
                    # 记录每个检测器的损失
                    detector_losses[detector_name] = detector_loss.item()
                    
                    # 累加到总损失
                    total_loss = total_loss + weighted_loss
                    
                except Exception as e:
                    print(f"计算 {detector_name} 的损失时出错: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 反向传播计算梯度
            total_loss.backward()
            
            # 检查梯度是否存在
            if universal_watermark.grad is None:
                print(f"迭代 {iter_num+1}/{self.iterations}: 梯度为None，跳过本次更新。")
                universal_watermark = universal_watermark.detach()
                continue
            
            with torch.no_grad():
                # 更新梯度累积器
                grad = universal_watermark.grad.detach()
                
                # 应用动量
                momentum_buffer = self.momentum * momentum_buffer + grad
                
                # 对于水印优化，我们希望最大化损失
                step_size = self.epsilon / self.iterations * (1 + 0.2 * iter_num / self.iterations)  # 逐渐增大步长
                universal_watermark = universal_watermark + step_size * torch.sign(momentum_buffer)
                
                # 确保水印在epsilon范围内
                universal_watermark = torch.clamp(universal_watermark, -self.epsilon, self.epsilon)
            
            # 重置梯度
            universal_watermark = universal_watermark.detach()
            
            if verbose and (iter_num + 1) % 5 == 0:
                loss_str = ", ".join([f"{name}: {loss:.4f}" for name, loss in detector_losses.items()])
                print(f"迭代 {iter_num+1}/{self.iterations}, 总损失: {total_loss.item():.4f}, 各检测器损失: {loss_str}")
                print(f"  水印L_inf范数: {universal_watermark.abs().max().item():.6f}")
        
        return universal_watermark.detach()
    
    def generate_universal_watermark(self, train_dataloader, max_images=128, initial_watermark=None, save_path=None, save_interval=25):
        """
        生成通用水印，使用多张训练图像优化
        
        :param train_dataloader: 训练数据加载器
        :param max_images: 用于训练的最大图像数
        :param initial_watermark: 初始水印，如果为None则随机初始化
        :param save_path: 保存水印的路径
        :param save_interval: 每处理多少张图像保存一次水印
        :return: 生成的通用水印
        """
        print(f"开始生成通用DAG水印，使用最多{max_images}张图像...")
        
        universal_watermark = initial_watermark
        image_count = 0
        
        for batch_idx, (images_batch_neg1_1, _, _) in enumerate(tqdm(train_dataloader, desc="生成通用DAG水印")):
            for img_idx in range(images_batch_neg1_1.size(0)):
                if image_count >= max_images:
                    break
                
                # 处理单张图像
                current_image_neg1_1 = images_batch_neg1_1[img_idx:img_idx+1].to(self.device)
                current_image_0_to_1 = (current_image_neg1_1 + 1) / 2.0
                
                # 优化水印
                universal_watermark = self.attack(
                    current_image_0_to_1,
                    universal_watermark=universal_watermark,
                    verbose=(image_count % 10 == 0)  # 每10张图像输出一次详情
                )
                
                image_count += 1
                
                # 定期保存
                if save_path and image_count % save_interval == 0:
                    checkpoint_path = f"{save_path}_checkpoint_{image_count}.pt"
                    torch.save(universal_watermark, checkpoint_path)
                    print(f"保存水印检查点: {checkpoint_path}")
            
            if image_count >= max_images:
                break
        
        # 最终保存
        if save_path:
            torch.save(universal_watermark, f"{save_path}_final.pt")
            print(f"最终通用水印已保存至 {save_path}_final.pt")
            print(f"水印L∞范数: {universal_watermark.abs().max().item():.6f}")
        
        return universal_watermark

def main():
    args_attack_settings = parse()
    device = torch.device('cuda' if args_attack_settings.global_settings.gpu else 'cpu')
    
    print("初始化检测器...")
    detectors_dict = init_detectors(device)
    if not detectors_dict: print("检测器初始化失败。退出。"); return

    print("准备数据...")
    try:
        data_components = prepare()
        attack_dataloader, test_dataloader = data_components[0], data_components[1]
    except Exception as e: print(f"数据准备错误: {e}"); return

    # 攻击参数
    epsilon_linf = 0.05  # L-infinity约束 (与multi_scale_face_poison_attack.py中保持一致)
    iterations_universal = 30  # 通用水印生成时的迭代次数
    
    # 配置各个检测器的权重
    # 基于经验，对不同检测器给予合适的权重
    detector_names = list(detectors_dict.keys())
    lambda_weights_dict = {}
    for name in detector_names:
        if "retinaface" in name.lower():
            lambda_weights_dict[name] = 0.2  # RetinaFace权重
        elif "s3fd" in name.lower():
            lambda_weights_dict[name] = 0.7  # 显著增加S3FD权重
        elif "dsfd" in name.lower():
            lambda_weights_dict[name] = 0.1  # 较小的DSFD权重
        else:
            lambda_weights_dict[name] = 1.0 / len(detector_names)  # 未知检测器使用均等权重
    
    print(f"\n=== 第一阶段: 生成通用DAG水印 ===")
    print(f"使用参数: epsilon={epsilon_linf}, iterations={iterations_universal}")
    print(f"检测器权重: {lambda_weights_dict}")

    # 初始化通用DAG水印攻击器
    universal_attacker = UniversalDagAttack(
        detectors_dict=detectors_dict,
        epsilon=epsilon_linf,
        iterations=iterations_universal,
        device=device,
        momentum=0.9,
        lambda_weights=lambda_weights_dict
    )
    
    # 设置最大训练样本数和保存路径
    max_training_samples = 128  # 使用128张图像生成通用水印
    save_path = "universal_dag_watermark"
    
    # 生成通用水印
    universal_watermark = universal_attacker.generate_universal_watermark(
        train_dataloader=attack_dataloader,  # 使用攻击数据集
        max_images=max_training_samples,
        save_path=save_path,
        save_interval=25  # 每25张图像保存一次水印
    )
    
    # 将通用水印转换到[-1,1]范围 (用于test_watermark)
    universal_watermark_neg1_1 = universal_watermark * 2.0
    
    print(f"\n=== 第二阶段: 评估通用DAG水印在整个数据集上的效果 ===")
    
    try:
        # 准备随机噪声水印，用于比较
        random_watermark = torch.randn_like(universal_watermark) * epsilon_linf
        random_watermark = torch.clamp(random_watermark, -epsilon_linf, epsilon_linf)
        random_watermark_neg1_1 = random_watermark * 2.0
        
        # 1. 基线评估 - 不添加水印
        print("\n1. 基线评估 - 不添加水印:")
        f1_base, conf_base, duq_base, box_change_base, _ = test_watermark(
            watermark=None,
            test_dataloader=test_dataloader,
            detector_models=[model for model, _ in detectors_dict.values()],
            device=device,
            num_test=100,  # 评估更多样本以获得可靠结果
            mode="no_watermark"  # 显式指定无水印模式
        )
        
        # 2. 随机噪声评估
        print("\n2. 随机噪声评估 (epsilon=0.05):")
        f1_random, conf_random, duq_random, box_random, _ = test_watermark(
            watermark=random_watermark_neg1_1.to(device),
            test_dataloader=test_dataloader,
            detector_models=[model for model, _ in detectors_dict.values()],
            device=device,
            num_test=100,
            mode="default"
        )
        
        # 3. 通用DAG水印评估
        print("\n3. 通用DAG水印评估:")
        f1_univ, conf_univ, duq_univ, box_univ, detector_results = test_watermark(
            watermark=universal_watermark_neg1_1.to(device),
            test_dataloader=test_dataloader,
            detector_models=[model for model, _ in detectors_dict.values()],
            device=device,
            num_test=100,
            mode="default"
        )
        
        # 输出结果比较
        print("\n=== 结果比较 ===")
        print(f"指标                基线(无水印)  随机噪声      通用水印")
        print(f"-----------------------------------------------------")
        print(f"F1分数:            {f1_base:.4f}      {f1_random:.4f}      {f1_univ:.4f}")
        print(f"置信度变化:        {conf_base:.4f}      {conf_random:.4f}      {conf_univ:.4f}")
        print(f"DUQ分数:           {duq_base:.4f}      {duq_random:.4f}      {duq_univ:.4f}")
        print(f"检测框数量变化(%): {box_change_base:.2f}%     {box_random:.2f}%     {box_univ:.2f}%")
        
        # 输出各检测器在通用水印下的表现
        print("\n各检测器在通用DAG水印下的表现:")
        for detector_name, results in detector_results.items():
            print(f"  {detector_name}: F1={results['f1']:.4f}, 置信度变化={results['confidence_change']:.4f}, 框数变化={results['box_change']:.2f}%")
        
    except Exception as e:
        print(f"评估过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 评估单个检测器的性能
    print("\n--- 单独评估各检测器性能 ---")
    for detector_name, (detector_model, _) in detectors_dict.items():
        print(f"\n评估通用DAG水印对 {detector_name} 的影响:")
        try:
            f1, conf_ch, duq, box_ch, _ = test_watermark(
                watermark=universal_watermark_neg1_1.to(device),
                test_dataloader=test_dataloader,
                detector_models=[detector_model],  # 只评估单个检测器
                device=device,
                num_test=50,  # 使用较小样本进行快速测试
                mode="default"
            )
            print(f"单独{detector_name} - F1: {f1:.4f}, 置信度变化: {conf_ch:.4f}, DUQ: {duq:.4f}, 框数变化: {box_ch:.2f}%")
        except Exception as e:
            print(f"评估 {detector_name} 时出错: {e}")
    
    print("\n通用DAG水印攻击完成!")
    print(f"通用水印已保存至 {save_path}_final.pt")
    print(f"水印L∞范数: {universal_watermark.abs().max().item():.6f}")
    print("可以在后续使用该水印直接应用于图像，实现对多个检测器的同时攻击")

if __name__ == "__main__":
    main() 