import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torchvision.transforms import GaussianBlur
import math

#######################################################
# 第一部分：特征提取模块 - 针对不同检测器的特征提取函数
#######################################################

# 更新特征层配置
FEATURE_LAYER_CONFIG = {
    # 根据功能相似性定义特征层
    "backbone_early": {   # 初期特征，通常是边缘和纹理
        "retinaface_mobilenet": ["stage1"],
        "retinaface_resnet": ["layer1"],
        "dsfd": ["conv1_0", "conv1_2", "layer1_5", "layer1_7"],
        "s3fd": ["conv1_1", "conv1_2", "conv2_1", "conv2_2"]  # S3FD的早期VGG层
    },
    "backbone_middle": {  # 中期特征，通常是形状和部分结构
        "retinaface_mobilenet": ["stage2"],
        "retinaface_resnet": ["layer2"],
        "dsfd": ["layer2_10", "layer2_12", "layer2_14"],
        "s3fd": ["conv3_1", "conv3_2", "conv3_3"]  # S3FD的中期VGG层
    },
    "backbone_late": {    # 后期特征，通常是高级语义和对象特征
        "retinaface_mobilenet": ["stage3"],
        "retinaface_resnet": ["layer3", "layer4"],
        "dsfd": ["layer3_17", "layer3_19", "layer3_21", "layer4_24", "layer4_26", "layer4_28"],
        "s3fd": ["conv4_1", "conv4_2", "conv4_3", "conv5_1", "conv5_2", "conv5_3"]  # S3FD的后期VGG层
    },
    "detection_specific": {  # 检测特定特征，如FPN、SSH等
        "retinaface_mobilenet": ["ssh1", "ssh2", "ssh3"],
        "retinaface_resnet": ["ssh1", "ssh2", "ssh3"],
        "dsfd": ["extras1", "extras2", "extras3", "loc1", "conf1", "fem1", "fem2", "fem3"],
        "s3fd": ["conv6", "conv7", "conv_final"]  # S3FD的检测头部特征
    }
}

def extract_features_by_type(model, img, detector_type, backbone_type=None):
    """
    通用特征提取函数，按特征类型从检测器提取特征
    
    Args:
        model: 检测器模型
        img: 输入图像
        detector_type: 检测器类型 (retinaface, dsfd, s3fd)
        backbone_type: 主干网络类型
        
    Returns:
        tuple: (特征字典, 输出特征)
    """
    # 根据检测器类型确定backbone_type
    if backbone_type is None:
        if detector_type == "retinaface":
            if hasattr(model, 'cfg') and model.cfg.get('name') == 'Resnet50':
                backbone_type = "resnet"
            else:
                backbone_type = "mobilenet"
        elif detector_type == "dsfd":
            backbone_type = "vgg"
        elif detector_type == "s3fd":
            backbone_type = "vgg"
    
    # 组合检测器和backbone类型作为特征层配置的键
    config_key = detector_type
    
    # 存储钩子和特征
    features_dict = {}
    hooks = []
    
    # 收集需要注册钩子的所有层
    all_layers = []
    for feature_type in FEATURE_LAYER_CONFIG:
        if config_key in FEATURE_LAYER_CONFIG[feature_type]:
            all_layers.extend(FEATURE_LAYER_CONFIG[feature_type][config_key])
    
    # 创建钩子函数
    def hook_fn(name):
        def hook(module, input, output):
            features_dict[name] = output
        return hook
    
    # 根据检测器类型注册合适的钩子
    if detector_type == "s3fd":
        # S3FD特定层的注册
        if hasattr(model, 'vgg'):
            for i, layer in enumerate(model.vgg):
                if isinstance(layer, nn.Conv2d):
                    layer_name = f"conv{i+1}"
                    if layer_name in all_layers:
                        hooks.append(layer.register_forward_hook(hook_fn(layer_name)))
        
        # 检测头部特征的注册
        if hasattr(model, 'extras'):
            for i, layer in enumerate(model.extras):
                if isinstance(layer, nn.Conv2d):
                    layer_name = f"conv{i+6}"  # 从conv6开始
                    if layer_name in all_layers:
                        hooks.append(layer.register_forward_hook(hook_fn(layer_name)))
        
        if hasattr(model, 'loc') and hasattr(model, 'conf'):
            hooks.append(model.loc[-1].register_forward_hook(hook_fn('conv_final')))
    
    elif detector_type == "retinaface":
        # 保持原有的RetinaFace钩子注册逻辑
        if hasattr(model, 'body'):
            if backbone_type == "mobilenet" and hasattr(model.body, 'body'):
                for stage_name in ['stage1', 'stage2', 'stage3']:
                    if stage_name in all_layers and hasattr(model.body.body, stage_name):
                        hooks.append(getattr(model.body.body, stage_name).register_forward_hook(hook_fn(stage_name)))
            elif backbone_type == "resnet":
                for layer_name in ['layer1', 'layer2', 'layer3', 'layer4']:
                    if layer_name in all_layers and hasattr(model.body, layer_name):
                        hooks.append(getattr(model.body, layer_name).register_forward_hook(hook_fn(layer_name)))
        
        for module_name in ['ssh1', 'ssh2', 'ssh3']:
            if module_name in all_layers and hasattr(model, module_name):
                hooks.append(getattr(model, module_name).register_forward_hook(hook_fn(module_name)))
    
    elif detector_type == "dsfd":
        # DSFD特定钩子注册逻辑 - 适用于DSFD.pytorch中的VGG实现
        
        # VGG基础网络层
        if hasattr(model, 'vgg'):
            vgg_layers = list(model.vgg.children())
            # VGG层名称与索引的映射
            vgg_layer_names = {
                "conv1": [0, 2],       # 前两个卷积层
                "layer1": [5, 7],      # 第二组卷积层
                "layer2": [10, 12, 14], # 第三组卷积层
                "layer3": [17, 19, 21], # 第四组卷积层
                "layer4": [24, 26, 28]  # 第五组卷积层
            }
            
            # 注册VGG层钩子
            for layer_name, indices in vgg_layer_names.items():
                if layer_name in all_layers:
                    for idx in indices:
                        if idx < len(vgg_layers):
                            layer = vgg_layers[idx]
                            if isinstance(layer, nn.Conv2d):
                                hooks.append(layer.register_forward_hook(hook_fn(f"{layer_name}_{idx}")))
        
        # 特征增强模块 (FEM)
        if hasattr(model, 'fpn_fem'):
            for i, fem_module in enumerate(model.fpn_fem):
                if isinstance(fem_module, nn.Module):
                    fem_name = f'fem{i+1}'
                    if fem_name in all_layers or 'fem' in all_layers:
                        hooks.append(fem_module.register_forward_hook(hook_fn(fem_name)))
        
        # 额外的卷积层和检测头
        for extra_name in ['extras', 'loc', 'conf']:
            if hasattr(model, extra_name):
                extra_module = getattr(model, extra_name)
                if isinstance(extra_module, nn.ModuleList):
                    for i, layer in enumerate(extra_module):
                        if isinstance(layer, nn.Module):
                            layer_name = f'{extra_name}{i+1}'
                            if layer_name in all_layers or extra_name in all_layers:
                                hooks.append(layer.register_forward_hook(hook_fn(layer_name)))
    
    # 执行前向传播以获取特征
    outputs = None
    # 不使用no_grad上下文，以保留梯度信息
    if isinstance(img, torch.Tensor):
        if img.dim() == 3:
            img = img.unsqueeze(0)
        device = next(model.parameters()).device
        img = img.to(device)
        outputs = model(img)
    
    # 收集提取的特征，按类型组织
    extracted_features = {
        "backbone_early": [],
        "backbone_middle": [],
        "backbone_late": [],
        "detection_specific": []
    }
    
    # 按类型组织特征
    for feature_type in FEATURE_LAYER_CONFIG:
        if config_key in FEATURE_LAYER_CONFIG[feature_type]:
            for layer_name in FEATURE_LAYER_CONFIG[feature_type][config_key]:
                if layer_name in features_dict:
                    extracted_features[feature_type].append(features_dict[layer_name])
    
    # 移除钩子
    for hook in hooks:
        hook.remove()
    
    return extracted_features, outputs

def get_retinaface_features(img, detector):
    """
    从RetinaFace提取特征，封装extract_features_by_type
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: RetinaFace检测器实例
    
    Returns:
        tuple: (特征字典, 输出特征)
    """
    # 检查detector属性
    if hasattr(detector, 'net'):
        net = detector.net
    else:
        net = detector
    
    # 确定backbone类型
    backbone_type = "mobilenet"
    if hasattr(net, 'cfg') and net.cfg.get('name') == 'Resnet50':
        backbone_type = "resnet"
    
    return extract_features_by_type(net, img, "retinaface", backbone_type)

def get_dsfd_features(img, detector):
    """
    从DSFD检测器提取特征，封装extract_features_by_type
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: DSFD检测器实例
    
    Returns:
        tuple: (特征字典, 输出特征)
    """
    # 获取实际模型
    if hasattr(detector, 'net'):
        net = detector.net
    else:
        net = detector
    
    return extract_features_by_type(net, img, "dsfd")

def get_s3fd_features(img, detector):
    """
    从S3FD检测器提取特征 (MODIFIED TO HANDLE conv3_3_raw_conf)
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: S3FD检测器实例 (this is the S3FD nn.Module)
    
    Returns:
        tuple: (extracted_features_dict, outputs, conv3_3_raw_conf)
    """
    try:
        device = img.device
        # Image preprocessing (as before)
        img_min, img_max = img.min().item(), img.max().item()
        preprocessed_img = img
        if img_min >= -1 and img_min < 0 and img_max <= 1:
            preprocessed_img = (img + 1) * 127.5
            mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
            preprocessed_img = preprocessed_img[:, [2, 1, 0], :, :] - mean
        elif img_min >= 0 and img_max <= 1:
            preprocessed_img = img * 255.0
            mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
            preprocessed_img = preprocessed_img[:, [2, 1, 0], :, :] - mean
        
        # Temporarily adjust detector's confidence threshold for feature extraction if needed (optional here)
        original_conf_thresh = None
        if hasattr(detector, 'conf_threshold'):
            original_conf_thresh = detector.conf_threshold
            # detector.conf_threshold = 0.01 # You might not need this if S3FDDetector.get_features handles it
            # print(f"[face_poison_integration.get_s3fd_features] Temp lowered S3FD conf_thresh to {detector.conf_threshold}")
        
        # Call the MODIFIED get_features from S3FDDetector.py
        # Ensure you are importing the correct function.
        # from S3FDDetector import get_features as get_features_from_s3fd_module 
        # For clarity, let's assume it's directly available or correctly imported.
        # This is a potential point of error if the wrong get_features is called.
        # Let's assume `sfd_detector_module.get_features` is the one we modified in S3FDDetector.py
        import S3FDDetector as sfd_detector_module # Make sure this import is correct

        # The `detector` passed here is the S3FD nn.Module instance
        selected_main_features, final_detection_outputs, conv3_3_raw_conf = sfd_detector_module.get_features(detector, preprocessed_img)

        # Restore original confidence threshold
        if hasattr(detector, 'conf_threshold') and original_conf_thresh is not None:
            # detector.conf_threshold = original_conf_thresh
            # print(f"[face_poison_integration.get_s3fd_features] Restored S3FD conf_thresh to {detector.conf_threshold}")
            pass # S3FDDetector.get_features should handle this internally now.

        # Construct the extracted_features dictionary as expected by downstream code
        # This mapping was based on the previous understanding of selected_features.
        # selected_main_features are: L2Norm(conv3_3), L2Norm(conv4_3), L2Norm(conv5_3)
        if not selected_main_features or len(selected_main_features) < 3 : # Basic check
            # print(f"Error: Not enough selected_main_features from sfd_detector_module.get_features. Got {len(selected_main_features) if selected_main_features else 0}")
            # Create dummy features to prevent crash, this indicates an issue in sfd_detector_module.get_features
            dummy_device = img.device
            sf0 = torch.zeros(img.size(0), 256, img.size(2)//4, img.size(3)//4, device=dummy_device).requires_grad_(True)
            sf1 = torch.zeros(img.size(0), 512, img.size(2)//8, img.size(3)//8, device=dummy_device).requires_grad_(True)
            sf2 = torch.zeros(img.size(0), 512, img.size(2)//16, img.size(3)//16, device=dummy_device).requires_grad_(True)
            selected_main_features = [sf0,sf1,sf2]
            if conv3_3_raw_conf is None:
                 conv3_3_raw_conf = torch.zeros(img.size(0), 4, img.size(2)//4, img.size(3)//4, device=dummy_device).requires_grad_(True)


        # The existing structure expects 3 features for S3FD based on FEATURE_LAYER_CONFIG.
        # Let's keep this consistent for the "main" features used in the general feature loss.
        # The old mapping was:
        # "backbone_middle": [selected_features[0]],  // L2Norm(Conv3_3) -> still use this for general feature loss on "conv3"
        # "backbone_late": [selected_features[1]],    // L2Norm(Conv4_3) -> still use this for general feature loss on "conv4"
        # "detection_specific": [selected_features[2]] // L2Norm(Conv5_3) -> still use this for general feature loss on "conv5"
        # This structure is used by `fuse_features` if S3FD features are fused with others.
        # For the specific conv3_3 head loss, we use `conv3_3_raw_conf` separately.
        
        extracted_features_dict = {
            "backbone_early": [], # S3FD doesn't typically use "early" in this 3-feature setup
            "backbone_middle": [selected_main_features[0]],  # L2Norm(conv3_3)
            "backbone_late": [selected_main_features[1]],    # L2Norm(conv4_3)
            "detection_specific": [selected_main_features[2]] # L2Norm(conv5_3)
        }
        
        # print(f"[face_poison_integration.get_s3fd_features] Returning conv3_3_raw_conf with grad: {conv3_3_raw_conf.requires_grad}")
        return extracted_features_dict, final_detection_outputs, conv3_3_raw_conf

    except Exception as e:
        print(f"Error in face_poison_integration.get_s3fd_features: {e}")
        import traceback
        traceback.print_exc()
        # Fallback
        dummy_extracted_dict = { "backbone_early": [], "backbone_middle": [torch.zeros(1,1,1,1, device=img.device).requires_grad_(True)], "backbone_late": [torch.zeros(1,1,1,1, device=img.device).requires_grad_(True)], "detection_specific": [torch.zeros(1,1,1,1, device=img.device).requires_grad_(True)]}
        dummy_raw_conf = torch.zeros(img.size(0), 4, img.size(2)//4, img.size(3)//4, device=img.device).requires_grad_(True)
        return dummy_extracted_dict, None, dummy_raw_conf

def get_detector_features(img, detector, detector_type="retinaface"):
    """
    通用特征提取函数 (MODIFIED TO HANDLE S3FD conv3_3_raw_conf)
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: 检测器实例
        detector_type: 检测器类型
    
    Returns:
        tuple: (main_features_list, outputs, s3fd_specific_conv3_3_raw_conf)
               s3fd_specific_conv3_3_raw_conf is None for non-S3FD.
    """
    s3fd_conv3_3_raw_conf = None # Default for non-S3FD detectors
    try:
        if detector_type.lower() == "s3fd":
            # Call the modified get_s3fd_features from this file
            extracted_s3fd_features_dict, outputs, s3fd_conv3_3_raw_conf = get_s3fd_features(img, detector)
            # Convert dict to list of 3 features as expected by some parts of the code
            features = [
                extracted_s3fd_features_dict["backbone_middle"][0], # L2Norm(conv3_3)
                extracted_s3fd_features_dict["backbone_late"][0],    # L2Norm(conv4_3)
                extracted_s3fd_features_dict["detection_specific"][0] # L2Norm(conv5_3)
            ]
            # print(f"[get_detector_features for S3FD] conv3_3_raw_conf grad: {s3fd_conv3_3_raw_conf.requires_grad if s3fd_conv3_3_raw_conf is not None else 'None'}")

        elif detector_type.lower() == "retinaface":
            features, outputs = extract_retinaface_features_direct(img, detector) # Assuming this returns (list_of_features, outputs)
        elif detector_type.lower() == "dsfd":
            features, outputs = extract_dsfd_features_direct(img, detector) # Assuming this returns (list_of_features, outputs)
        else:
            raise ValueError(f"不支持的检测器类型: {detector_type}")
        
        # Validate and pad features if necessary (existing logic)
        # ... (ensure features is a list of 3 tensors)
        if len(features) != 3:
            # print(f"Warning: {detector_type} detector extracted {len(features)} features, expected 3. Padding/truncating.")
            # Simplified padding/truncating:
            if len(features) > 3: features = features[:3]
            while len(features) < 3:
                if features: features.append(torch.zeros_like(features[0]))
                else: features.append(torch.zeros(img.size(0),1,1,1, device=img.device).requires_grad_(True)) # very basic dummy
        
        return features, outputs, s3fd_conv3_3_raw_conf
        
    except Exception as e:
        print(f"特征提取出错 in get_detector_features for {detector_type}: {e}")
        import traceback
        traceback.print_exc()
        # Fallback
        backup_features = [
            torch.ones(img.size(0), 256, img.size(2)//4, img.size(3)//4, device=img.device).requires_grad_(True),
            torch.ones(img.size(0), 512, img.size(2)//8, img.size(3)//8, device=img.device).requires_grad_(True),
            torch.ones(img.size(0), 512, img.size(2)//16, img.size(3)//16, device=img.device).requires_grad_(True)
        ]
        return backup_features, None, None

#######################################################
# 第二部分：特征融合模块 - 融合不同检测器的特征
#######################################################

def normalize_feature_dimensions(features_list, target_size=(64, 64)):
    """
    标准化特征维度，针对三个特征层优化
    
    Args:
        features_list: 特征列表
        target_size: 目标空间尺寸(高度，宽度)
    
    Returns:
        标准化后的特征列表
    """
    normalized_features = []
    
    for features in features_list:
        if not isinstance(features, list):
            features = [features]
        
        for feat in features:
            if feat is None:
                continue
                
            # 调整通道数量 - 使用更高的目标通道数以保留更多信息
            target_channels = 128  # 提高到128通道以保留更多信息
            if feat.size(1) != target_channels:
                channel_adaptor = torch.nn.Conv2d(
                    feat.size(1), target_channels, kernel_size=1, 
                    bias=False
                ).to(feat.device)
                
                # 初始化为kaiming均匀分布
                torch.nn.init.kaiming_uniform_(channel_adaptor.weight, a=0)
                
                # 应用适配器
                feat = channel_adaptor(feat)
            
            # 调整空间尺寸
            normalized_feat = F.interpolate(
                feat, 
                size=target_size, 
                mode='bilinear', 
                align_corners=False
            )
            
            normalized_features.append(normalized_feat)
    
    return normalized_features

def calculate_feature_attention(features):
    """
    计算特征注意力权重
    
    Args:
        features: 特征列表
    
    Returns:
        注意力权重列表
    """
    attention_weights = []
    
    for feat in features:
        # 计算特征的L2范数作为重要性指标
        feat_norm = torch.norm(feat, p=2, dim=1, keepdim=True)
        # 应用softplus激活使权重平滑且非负
        weight = F.softplus(feat_norm)
        # 归一化权重
        weight = weight / (torch.sum(weight) + 1e-8)
        attention_weights.append(weight)
    
    return attention_weights

def fuse_features_by_type(features_dict, fusion_type="attention", target_size=(64, 64)):
    """
    基于特征类型融合不同检测器的特征，为三层特征优化
    
    Args:
        features_dict: 字典，键为检测器名称，值为特征类型字典
        fusion_type: 融合类型，可选值：'concat', 'attention', 'weighted'
        target_size: 融合特征的目标尺寸
    
    Returns:
        tuple: (融合的特征字典, 输出特征)
    """
    # 对于三层特征，我们调整为三种类型
    collected_features = {
        "vgg_conv3": [],    # 第15层 - conv3_3
        "vgg_conv4": [],    # 第22层 - conv4_3
        "vgg_conv5": []     # 第28层 - conv5_3
    }
    
    # 收集不同检测器的输出特征
    outputs_features = []
    
    # 处理每个检测器的特征
    for detector_name, (detector_features, outputs) in features_dict.items():
        # DSFD和S3FD有3个特征，我们按顺序匹配到三个类型
        if len(detector_features) == 3:
            collected_features["vgg_conv3"].append(detector_features[0])  # 第15层
            collected_features["vgg_conv4"].append(detector_features[1])  # 第22层
            collected_features["vgg_conv5"].append(detector_features[2])  # 第28层
        # RetinaFace也有3个特征，按相似功能匹配
        elif len(detector_features) == 3 and 'retinaface' in detector_name.lower():
            collected_features["vgg_conv3"].append(detector_features[0])  # FPN特征
            collected_features["vgg_conv4"].append(detector_features[1])  # SSH1特征
            collected_features["vgg_conv5"].append(detector_features[2])  # SSH2特征
        else:
            # 如果特征数量不匹配，尝试按可用数量分配
            available = len(detector_features)
            if available >= 1:
                collected_features["vgg_conv3"].append(detector_features[0])
            if available >= 2:
                collected_features["vgg_conv4"].append(detector_features[1])
            if available >= 3:
                collected_features["vgg_conv5"].append(detector_features[2])
        
        # 处理输出
        if outputs:
            if isinstance(outputs, (list, tuple)):
                outputs_features.extend(outputs)
            else:
                outputs_features.append(outputs)
    
    # 对每种特征类型进行融合
    fused_features = {}
    for feature_type, features in collected_features.items():
        if not features:
            fused_features[feature_type] = None
            continue
        
        # 标准化特征尺寸
        normalized_features = normalize_feature_dimensions(features, target_size)
        
        # 根据融合类型融合特征
        if fusion_type == "concat" and normalized_features:
            fused_features[feature_type] = torch.cat(normalized_features, dim=1)
            
        elif fusion_type == "attention" and normalized_features:
            # 计算注意力权重 - 增强版本
            attention_weights = []
            for feat in normalized_features:
                # 计算通道注意力 - 更精细的注意力机制
                channel_att = torch.sigmoid(F.adaptive_avg_pool2d(feat, 1))
                spatial_att = torch.sigmoid(feat.mean(dim=1, keepdim=True))
                combined_att = channel_att * spatial_att
                attention_weights.append(combined_att)
            
            # 归一化权重
            sum_weights = sum(attention_weights)
            normalized_weights = [w / (sum_weights + 1e-8) for w in attention_weights]
            
            # 应用权重并融合
            weighted_features = [feat * weight for feat, weight in zip(normalized_features, normalized_weights)]
            fused_features[feature_type] = torch.cat(weighted_features, dim=1)
            
        elif fusion_type == "weighted" and normalized_features:
            # 使用优化的权重 - 按检测器可靠性分配
            detector_weights = {"dsfd": 1.2, "s3fd": 1.0, "retinaface": 1.1}  # 默认权重
            
            # 如果无法确定检测器类型，使用均等权重
            weights = [1.0 / len(normalized_features)] * len(normalized_features)
            weighted_features = [feat * w for feat, w in zip(normalized_features, weights)]
            fused_features[feature_type] = sum(weighted_features)
    
    # 输出特征处理
    return fused_features, outputs_features

def fuse_features(features_dict, fusion_type="attention", target_size=(64, 64)):
    """
    融合不同检测器的特征，适配三层特征
    
    Args:
        features_dict: 字典，键为检测器名称，值为(中间特征,输出特征)元组
        fusion_type: 融合类型，可选值：'concat', 'attention', 'weighted'
        target_size: 融合特征的目标尺寸
    
    Returns:
        tuple: (融合的特征列表, 融合的输出特征)
    """
    # 使用基于类型的融合函数
    fused_features_dict, outputs_features = fuse_features_by_type(features_dict, fusion_type, target_size)
    
    # 重组为有序的特征列表
    all_features = []
    for feature_type in ["vgg_conv3", "vgg_conv4", "vgg_conv5"]:
        if feature_type in fused_features_dict and fused_features_dict[feature_type] is not None:
            all_features.append(fused_features_dict[feature_type])
        else:
            # 如果某一类型缺失，使用模拟特征
            print(f"警告：缺少 {feature_type} 特征，使用模拟特征替代")
            mock_feature = torch.ones(
                1, 128, target_size[0], target_size[1], 
                device=next(iter(features_dict.values()))[0][0].device
            ).requires_grad_(True)
            all_features.append(mock_feature)
    
    # 确保有三个特征
    assert len(all_features) == 3, f"融合后特征数量错误：期望3个，实际{len(all_features)}个"
    
    return all_features, outputs_features

#######################################################
# 第三部分：攻击模块 - 计算损失和优化水印
#######################################################

def compute_multiscale_adversarial_loss(
    watermarked_features,   # List of 3 main fused features for general feature loss
    clean_features,         # List of 3 main fused features for general feature loss
    watermarked_outputs,    # List of final detector outputs (e.g. dicts with 'boxes', 'scores') for general output loss
    clean_outputs,
    s3fd_conv3_3_raw_conf_wm=None # New argument, tensor [B,4,H,W] or None
    ):
    """
    改进的多尺度特征攻击 (MODIFIED for S3FD conv3_3 specific loss)
    """
    device = watermarked_features[0].device if watermarked_features and watermarked_features[0] is not None else (s3fd_conv3_3_raw_conf_wm.device if s3fd_conv3_3_raw_conf_wm is not None else torch.device('cpu'))
    total_feature_loss = torch.tensor(0.0, device=device, requires_grad=True)
    total_output_loss = torch.tensor(0.0, device=device, requires_grad=True)
    
    # --- Existing Feature Loss Calculation (on watermarked_features vs clean_features) ---
    has_valid_boxes = False # Determined from watermarked_outputs as before
    if watermarked_outputs:
        for out_list_item in watermarked_outputs: # watermarked_outputs is now a list of outputs from possibly multiple detectors if fused.

            if isinstance(out_list_item, list): # If it's a list of batch outputs
                for out_dict in out_list_item:
                    if isinstance(out_dict, dict) and 'boxes' in out_dict and isinstance(out_dict['boxes'], torch.Tensor):
                        if out_dict['boxes'].size(0) > 0:
                            has_valid_boxes = True
                            break
            elif isinstance(out_list_item, dict): # If it's a single batch output
                 if 'boxes' in out_list_item and isinstance(out_list_item['boxes'], torch.Tensor):
                    if out_list_item['boxes'].size(0) > 0:
                        has_valid_boxes = True
            if has_valid_boxes:
                    break
    
    feature_weights = [2.0, 3.0, 4.0] # Default weights


    if watermarked_features and clean_features and len(watermarked_features) == len(clean_features):
        for i, (wm_feat, clean_feat) in enumerate(zip(watermarked_features, clean_features)):
            if wm_feat is None or clean_feat is None: continue
            if not wm_feat.requires_grad: wm_feat = wm_feat.clone().requires_grad_(True)
        
        noise_scale = 0.2 if has_valid_boxes else 0.5
        random_noise = torch.randn_like(wm_feat) * noise_scale
        target = -clean_feat + random_noise
        
        importance = torch.abs(clean_feat).mean(dim=(1,2,3), keepdim=True) # Use all spatial dims for mean
        importance_mask = (torch.abs(clean_feat) > importance).float() # Binary mask for important regions
        
        region_weight_factor = 2.0 if has_valid_boxes else 4.0

        target_for_loss = target.clone()
        target_for_loss[importance_mask == 1] *= region_weight_factor


        current_feat_loss_weight = feature_weights[i] if i < len(feature_weights) else feature_weights[-1]
        feat_loss = F.mse_loss(wm_feat, target_for_loss) * current_feat_loss_weight
        total_feature_loss = total_feature_loss + feat_loss
        
        if not has_valid_boxes:
            suppress_loss = torch.mean(torch.abs(wm_feat)) * 2.0
            total_feature_loss = total_feature_loss + suppress_loss
    
    base_loss_value = -0.1 if has_valid_boxes else -1.0 # From your code
    current_total_output_loss_from_final_outputs = torch.tensor(base_loss_value, device=device) # Start with base
    
    outputs_to_process = []
    if watermarked_outputs:
        if isinstance(watermarked_outputs[0], list): # list of lists of dicts (multiple detectors, batched)
            for det_batch_out in watermarked_outputs:
                outputs_to_process.extend(det_batch_out)
        elif isinstance(watermarked_outputs[0], dict): # list of dicts (single detector, batched, or multiple detectors non-batched)
            outputs_to_process = watermarked_outputs

    for wm_out_item in outputs_to_process: # Process each output dictionary
        if wm_out_item is None or not isinstance(wm_out_item, dict):
                continue
                
        #置信度清零攻击
        if 'scores' in wm_out_item and isinstance(wm_out_item['scores'], torch.Tensor) and wm_out_item['scores'].numel() > 0:
            scores_tensor = wm_out_item['scores']
            if not scores_tensor.requires_grad: scores_tensor = scores_tensor.detach().requires_grad_(True)
            zero_target_scores = torch.zeros_like(scores_tensor)
            score_loss = F.mse_loss(scores_tensor, zero_target_scores) * 12.0 # Weight from your code
            current_total_output_loss_from_final_outputs = current_total_output_loss_from_final_outputs + score_loss
        
        #边界框极小化攻击
        if 'boxes' in wm_out_item and isinstance(wm_out_item['boxes'], torch.Tensor) and wm_out_item['boxes'].size(0) > 0:
            boxes_tensor = wm_out_item['boxes']
            if not boxes_tensor.requires_grad: boxes_tensor = boxes_tensor.detach().requires_grad_(True)
            center_x = (boxes_tensor[:, 0] + boxes_tensor[:, 2]) / 2
            center_y = (boxes_tensor[:, 1] + boxes_tensor[:, 3]) / 2
            min_size_box = 0.001
            target_boxes_min = torch.stack([
                center_x - min_size_box, center_y - min_size_box,
                center_x + min_size_box, center_y + min_size_box
            ], dim=1)
            box_loss = F.l1_loss(boxes_tensor, target_boxes_min) * 12.0 # Weight from your code
            current_total_output_loss_from_final_outputs = current_total_output_loss_from_final_outputs + box_loss
            
    total_output_loss = total_output_loss + current_total_output_loss_from_final_outputs


    # --- NEW S3FD Conv3_3 Specific Loss ---
    if s3fd_conv3_3_raw_conf_wm is not None:
        if not s3fd_conv3_3_raw_conf_wm.requires_grad:
            s3fd_conv3_3_raw_conf_wm = s3fd_conv3_3_raw_conf_wm.clone().requires_grad_(True)


        face_logits_c3 = s3fd_conv3_3_raw_conf_wm[:, 3, :, :]      # Shape [B, H, W]
        bg_logits_c3_all = s3fd_conv3_3_raw_conf_wm[:, 0:3, :, :]  # Shape [B, 3, H, W]
        max_bg_logits_c3, _ = torch.max(bg_logits_c3_all, dim=1)   # Shape [B, H, W]

        s3fd_conv3_3_objective = torch.mean(max_bg_logits_c3 - face_logits_c3)
        
        s3fd_conv3_3_specific_loss_weight = 20.0 # Needs tuning, make it significant
        
        total_output_loss = total_output_loss + (s3fd_conv3_3_specific_loss_weight * s3fd_conv3_3_objective)
    if total_feature_loss.dim() > 0 : total_feature_loss = total_feature_loss.mean()
    if total_output_loss.dim() > 0 : total_output_loss = total_output_loss.mean()

    return total_feature_loss, total_output_loss

def optimize_watermark_with_multi_detector_features(
    watermark, 
    clean_images, 
    detectors_dict,
    device,
    pgd_steps=100, 
    pgd_eps=0.5, 
    pgd_lr=0.01, 
    pgd_momentum=0.9,
    fusion_type="attention",
    feature_size=(64, 64)):
    """
    使用多检测器特征融合优化水印
    
    Args:
        watermark: 初始水印 [B,C,H,W]
        clean_images: 干净图像 [B,C,H,W]
        detectors_dict: 检测器字典 {"detector_name": (detector, type)}
        device: 计算设备
        pgd_steps: PGD迭代步数
        pgd_eps: PGD扰动范围
        pgd_lr: PGD学习率
        pgd_momentum: PGD动量
        fusion_type: 特征融合类型
        feature_size: 特征融合的目标尺寸
    
    Returns:
        tuple: (优化后的水印, 最佳损失值)
    """
    try:
        print(f"开始基于多检测器特征的优化攻击 (最大步数={pgd_steps}, 扰动范围={pgd_eps})")
        
        # 基本检查
        if len(watermark.shape) != 4 or len(clean_images.shape) != 4:
            raise ValueError("输入张量必须是4维的[B,C,H,W]格式")
            
        # 确保使用分离的张量
        original_watermark = watermark.clone().detach()
        batch_size = clean_images.size(0)
        
        # 批次大小调整
        if original_watermark.size(0) != batch_size:
            if original_watermark.size(0) == 1:
                original_watermark = original_watermark.expand(batch_size, -1, -1, -1)
            else:
                original_watermark = original_watermark[:batch_size]
        
        # 空间维度调整
        if original_watermark.size(2) != clean_images.size(2) or original_watermark.size(3) != clean_images.size(3):
            original_watermark = F.interpolate(
                original_watermark,
                size=(clean_images.size(2), clean_images.size(3)),
                mode='bilinear',
                align_corners=False
            )
        
        # 创建带有强度衰减的自适应掩码
        threshold = 0.050  # 设置阈值，判断原始水印的强度
        watermark_magnitude = torch.abs(original_watermark)
        
        # 1. 创建基本二值掩码
        basic_mask = (watermark_magnitude < threshold).float()
        
        # 2. 创建衰减系数，原始水印强度越大，新添加的水印越弱
        decay_factor = torch.exp(-5.0 * watermark_magnitude)  # 指数衰减
        
        # 3. 生成最终的自适应衰减掩码，而不是二元掩码
        poison_mask = decay_factor * basic_mask
        
        # 初始扰动使用衰减掩码
        delta = torch.randn_like(original_watermark) * 0.001 * poison_mask
        delta.requires_grad = True

         
        # 预计算干净图像的特征
        print("预计算阶段: 提取干净图像特征...")
        clean_features_dict = {}
        
        # 干净图像特征提取不需要梯度
        with torch.no_grad():
            for detector_name, (detector, detector_type) in detectors_dict.items():
                features, outputs, _ = get_detector_features(clean_images, detector, detector_type)
                clean_features_dict[detector_name] = (features, outputs)
            
            # 融合干净图像特征
            clean_fused_features, clean_out_features = fuse_features(
                clean_features_dict, 
                fusion_type=fusion_type, 
                target_size=feature_size
            )
        
        # 优化器设置 - 使用Adam优化器，不使用AdamW
        optimizer = torch.optim.Adam([delta], lr=pgd_lr)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=pgd_steps)
        
        # 保存最佳结果
        best_delta = None
        best_loss = float('inf')  # 现在我们是要最小化损失，所以初始化为正无穷
        
        # 主优化循环
        for step in range(pgd_steps):
            optimizer.zero_grad()
            
            # 应用当前水印
            current_watermark = original_watermark + delta
            watermarked_images = torch.clamp(clean_images + current_watermark, -1, 1)
            
            # 提取水印图像特征 - 确保保留梯度链
            watermarked_features_dict = {}
            s3fd_conv3_3_raw_conf_for_loss = None # Initialize here for the current step

            for detector_name, (detector, detector_type) in detectors_dict.items():
                # 设置detector为eval模式，但保留梯度
                if hasattr(detector, 'eval'):
                    detector.eval()
                
                # 特征提取
                try:
                    features, outputs, s3fd_specific_raw_conf = get_detector_features(watermarked_images, detector, detector_type)
                    watermarked_features_dict[detector_name] = (features, outputs)
                    if detector_type.lower() == "s3fd" and s3fd_specific_raw_conf is not None:
                        s3fd_conv3_3_raw_conf_for_loss = s3fd_specific_raw_conf
                        # print(f"[OptimizerLoop] Captured s3fd_conv3_3_raw_conf_for_loss with grad: {s3fd_conv3_3_raw_conf_for_loss.requires_grad}")
                except Exception as e:
                    print(f"检测器 {detector_name} 特征提取出错: {e}")
                    continue
            
            # 确保至少有一个检测器提取了特征
            if not watermarked_features_dict:
                raise ValueError("所有检测器特征提取都失败了")
            
            # 融合水印图像特征
            watermarked_fused_features, watermarked_out_features = fuse_features(
                watermarked_features_dict, 
                fusion_type=fusion_type, 
                target_size=feature_size
            )
            
            # 计算损失
            # Initialize loss variables to ensure they are defined in all paths
            feature_loss = torch.tensor(0.0, device=device, requires_grad=True)
            output_loss = torch.tensor(0.0, device=device, requires_grad=True)
            try:
                # 计算特征损失和输出损失
                feature_loss, output_loss = compute_multiscale_adversarial_loss(
                    watermarked_fused_features,
                    clean_fused_features,
                    watermarked_out_features,
                    clean_out_features,
                    s3fd_conv3_3_raw_conf_wm=s3fd_conv3_3_raw_conf_for_loss # Use the correct variable
                )
                
                # 确保损失是有效的tensor类型且需要梯度
                if not isinstance(feature_loss, torch.Tensor) or not feature_loss.requires_grad:
                    print("警告: 特征损失不是可微分的Tensor，创建替代损失")
                    feature_loss = delta.sum() * 0.0 - 0.1  # 小的常数损失与delta连接
                
                if not isinstance(output_loss, torch.Tensor) or not output_loss.requires_grad:
                    print("警告: 输出损失不是可微分的Tensor，创建替代损失")
                    output_loss = delta.sum() * 0.0 - 0.01  # 小的常数损失与delta连接
                
                # 检查损失是否有效
                if not torch.isfinite(feature_loss) or not torch.isfinite(output_loss):
                    raise ValueError(f"损失无效: feature_loss={feature_loss.item()}, output_loss={output_loss.item()}")
                
                # 反向传播 - 先尝试特征损失
                try:
                    feature_loss.backward(retain_graph=True)
                    
                except Exception as e:
                    print(f"特征损失反向传播失败: {e}")
                    # 创建与delta相连的替代损失
                    (-0.1 * delta.sum()).backward(retain_graph=True)
                
                # 尝试输出损失
                try:
                    output_loss.backward()
                   
                except Exception as e:
                    print(f"输出损失反向传播失败: {e}")
            except Exception as e:
                print(f"损失计算或反向传播出错: {e}")
                import traceback
                traceback.print_exc()
                # 确保delta有梯度，即使损失计算失败
                (-0.01 * delta.sum()).backward()
                print("使用后备损失完成反向传播")
            
            # 检查delta的梯度是否存在
            if delta.grad is None or not torch.isfinite(delta.grad).all():
                print("警告: delta的梯度无效，使用随机梯度")
                delta.grad = torch.randn_like(delta) * 0.01
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_([delta], max_norm=1.0)
            
            # 更新delta
            optimizer.step()
            scheduler.step()
            
            # 限制扰动范围，保持中毒mask的特性
            with torch.no_grad():
                # 应用poison_mask，确保主要在原始水印强度较小的区域添加扰动
                delta.data = delta.data * poison_mask
                # 然后限制扰动范围
                delta.data = torch.clamp(delta.data, -pgd_eps, pgd_eps)
            
            # 更新最佳结果 - 寻找最小的损失值（最大的负值）
            current_feature_loss = feature_loss.item() if isinstance(feature_loss, torch.Tensor) else float('inf')
            current_output_loss = output_loss.item() if isinstance(output_loss, torch.Tensor) else float('inf')
            
            # 使用输出损失作为主要指标来判断最佳结果
            if current_output_loss < best_loss:
                best_loss = current_output_loss
                best_delta = delta.clone().detach()
            
            # 每10步显示一次当前损失值
            if (step + 1) % 10 == 0:
                print(f"步骤 {step + 1}/{pgd_steps}, 特征损失: {current_feature_loss:.4f}, 输出损失: {current_output_loss:.4f}")
            
            # 在 poison_mask 计算后
            if "s3fd" in detector_name.lower(): # 假设你有办法识别当前是否为S3FD优化
                allowed_pixels_ratio = poison_mask.sum() / poison_mask.numel()
                # print(f"[S3FD DEBUG] poison_mask允许delta的像素比例: {allowed_pixels_ratio.item():.4f} (threshold: {threshold})")
        
        # 使用最佳delta生成最终水印
        if best_delta is not None:
            final_watermark = original_watermark + best_delta
        else:
            final_watermark = original_watermark + delta.detach()
        
        # 确保水印在有效范围内
        final_watermark = torch.clamp(final_watermark, -1, 1)
        
        # 确保返回值是float类型
        return final_watermark, float(best_loss)
        
    except Exception as e:
        print(f"优化过程出错: {e}")
        import traceback
        traceback.print_exc()
        return watermark, float('inf')


#######################################################
# 第四部分：辅助函数 - 评估和工具函数
#######################################################


def extract_retinaface_features_direct(img, detector):
    """
    改进的RetinaFace特征提取方法 - 去掉攻击效果最弱的特征层(backbone层)
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: RetinaFace检测器实例
    
    Returns:
        tuple: (特征列表, 输出特征)
    """
    if not isinstance(img, torch.Tensor):
        raise ValueError("输入必须是torch.Tensor类型")
    
    if img.dim() == 3:
        img = img.unsqueeze(0)
        
    # 获取实际模型
    if hasattr(detector, 'net'):
        net = detector.net
    else:
        net = detector
    
    # 保存输出，我们只保留3个关键特征（移除backbone特征）
    features = []
    outputs = None
    
    try:
        # 检查必要的属性
        if not hasattr(net, 'body'):
            print(f"警告: RetinaFace模型缺少'body'属性")
            raise AttributeError("RetinaFace模型结构不匹配")
        
        # 前向传播获取中间特征
        out = net.body(img)
        
        if not hasattr(net, 'fpn'):
            print(f"警告: RetinaFace模型缺少'fpn'属性")
            raise AttributeError("RetinaFace模型结构不匹配")
            
        # FPN特征 - 这是较为重要的特征，保留
        fpn_features = net.fpn(out)
        if isinstance(fpn_features, list) and len(fpn_features) > 0:
            features.append(fpn_features[0])  # 特征1: FPN特征
        
        # SSH特征 - 这些是检测相关的关键特征，都保留
        if hasattr(net, 'ssh1'):
            feature1 = net.ssh1(fpn_features[0])
            features.append(feature1)  # 特征2: SSH特征1
        
        if hasattr(net, 'ssh2') and len(fpn_features) > 1:
            feature2 = net.ssh2(fpn_features[1])
            features.append(feature2)  # 特征3: SSH特征2
        
        # 确保我们有3个特征（移除了backbone特征）
        while len(features) < 3:
            # 如果缺少特征，使用已有特征的副本填充
            print(f"警告: RetinaFace只提取到{len(features)}个特征，创建填充特征")
            if len(features) > 0:
                # 使用最后一个特征的副本
                last_feat = features[-1]
                features.append(last_feat.clone())
            else:
                # 如果完全没有特征，创建模拟特征
                print(f"创建模拟特征")
                features.append(torch.ones(img.size(0), 64, img.size(2)//8, img.size(3)//8, device=img.device).requires_grad_(img.requires_grad))
        
        # 限制特征数量为3个
        features = features[:3]
        
        # 获取输出
        try:
            outputs = net(img)
        except Exception as e:
            print(f"警告: 尝试直接获取RetinaFace输出失败: {e}")
            outputs = None
    
        return features, outputs
        
    except Exception as e:
        print(f"从RetinaFace直接提取特征失败: {e}")
        import traceback
        traceback.print_exc()
        # 创建模拟特征，确保保留梯度链
        features = [
            torch.ones(img.size(0), 128, img.size(2)//8, img.size(3)//8, device=img.device).requires_grad_(img.requires_grad),
            torch.ones(img.size(0), 128, img.size(2)//16, img.size(3)//16, device=img.device).requires_grad_(img.requires_grad),
            torch.ones(img.size(0), 128, img.size(2)//32, img.size(3)//32, device=img.device).requires_grad_(img.requires_grad)
        ]
        return features, None

def extract_s3fd_features_direct_improved(img, detector):
    """
    改进的S3FD特征提取方法 - 直接利用S3FD的get_features参数
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: S3FD检测器实例
    
    Returns:
        tuple: (特征列表, 输出特征)
    """
    if not isinstance(img, torch.Tensor):
        raise ValueError("输入必须是torch.Tensor类型")
    
    if img.dim() == 3:
        img = img.unsqueeze(0)
    
    device = img.device
    
    # 创建备用特征（用于特征提取失败的情况）
    backup_features = [
        torch.ones(img.size(0), 512, img.size(2)//8, img.size(3)//8, device=device).requires_grad_(True),
        torch.ones(img.size(0), 512, img.size(2)//16, img.size(3)//16, device=device).requires_grad_(True),
        torch.ones(img.size(0), 1024, img.size(2)//32, img.size(3)//32, device=device).requires_grad_(True)
    ]
    
    try:
        # 获取实际模型
        if hasattr(detector, 'net'):
            model = detector.net
        else:
            model = detector
        
        # 保存当前模式并设置为训练模式以保留梯度
        training_mode = model.training
        model.train()
        
        # 预处理图像
        preprocessed_img = preprocess_s3fd_image(img, device)
        
        # 直接使用模型的forward方法，传入get_features=True
        try:
            # 前向传播获取特征
            features = model(preprocessed_img, get_features=True)
            
            # 按照S3FDDetector.get_features函数的做法选择关键特征
            if isinstance(features, list) and len(features) >= 3:
                # 选择前三层特征
                selected_features = features[1:4]
                # print(f"成功提取S3FD特征: 形状分别为 {selected_features[0].shape}, {selected_features[1].shape}, {selected_features[2].shape}")
            else:
                raise ValueError(f"提取的特征数量不足: {len(features) if isinstance(features, list) else '未知'}")
        except Exception as e:
            print(f"使用forward方法提取特征失败: {e}")
            raise e
        
        # 获取检测输出
        outputs = None
        try:
            with torch.no_grad():
                model.eval()
                outputs = model(preprocessed_img)
                model.train()  # 恢复训练模式
        except Exception as e:
            print(f"获取检测输出失败: {e}")
        
        # 恢复原始模式
        if not training_mode:
            model.eval()
        
        # print(f"S3FD特征形状: {[f.shape for f in selected_features]}")
        # print(f"特征是否需要梯度: {[f.requires_grad for f in selected_features]}")
        
        return selected_features, outputs
            
    except Exception as e:
        print(f"S3FD特征提取失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试使用S3FDDetector.py中的get_features函数
        try:
            # 现在转为直接使用get_features函数
            from S3FDDetector import get_features
            # print("使用S3FDDetector.get_features函数提取特征")
            selected_features, outputs = get_features(detector, preprocessed_img)
            if selected_features and len(selected_features) >= 3:
                return selected_features, outputs
        except Exception as ex:
            print(f"使用get_features函数也失败: {ex}")
        
        # 如果所有尝试都失败，返回备用特征
        print("所有特征提取方法都失败，使用备用特征")
        return backup_features, None

def preprocess_s3fd_image(img, device):
    """
    为S3FD预处理图像
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        device: 设备
    
    Returns:
        预处理后的图像
    """
    img_min = img.min().item()
    img_max = img.max().item()
    
    # 判断图像是否需要预处理
    preprocessed_img = img
    
    # 检查图像范围
    if img_min >= -1 and img_min < 0 and img_max <= 1:
        # 转换[-1,1]范围到[0,255]范围
        preprocessed_img = (img + 1) * 127.5
        # 应用BGR转换和减均值
        mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
        preprocessed_img = preprocessed_img[:, [2, 1, 0], :, :] - mean
    elif img_min >= 0 and img_max <= 1:
        # 转换[0,1]范围到[0,255]范围
        preprocessed_img = img * 255.0
        # 应用BGR转换和减均值
        mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
        preprocessed_img = preprocessed_img[:, [2, 1, 0], :, :] - mean
    
    return preprocessed_img

def extract_dsfd_features_direct(img, detector):
    """
    针对DSFD检测器的特征提取 - 优化版本
    直接使用DSFD模型的get_features功能提取特征
    
    Args:
        img: 输入图像张量 [B,C,H,W]
        detector: DSFD检测器实例
    
    Returns:
        tuple: (特征列表, 输出特征)
    """
    # 确保img是正确的格式
    if not isinstance(img, torch.Tensor):
        raise ValueError("输入必须是torch.Tensor类型")
    
    if img.dim() == 3:
        img = img.unsqueeze(0)
    
    device = img.device
    
    # 创建备用特征（如果提取失败）
    backup_features = [
        torch.ones(img.size(0), the_size[0], img.size(2)//the_size[1], img.size(3)//the_size[1], device=device).requires_grad_(True)
        for the_size in [(256, 4), (512, 8), (512, 16)]  # (通道数, 下采样比例)
    ]
    
    try:
        # 获取DSFD模型
        if hasattr(detector, 'model'):
            model = detector.model
        else:
            model = detector
        
        # 保存当前模式并设置为训练模式以保留梯度
        training_mode = model.training
        model.train()
        
        # 预处理图像
        # 转换范围到DSFD期望的输入
        img_min, img_max = img.min().item(), img.max().item()
        preprocessed_img = img.clone()
        
        if img_min >= -1 and img_min < 0 and img_max <= 1:
            # 转换[-1,1]范围到[0,255]范围
            preprocessed_img = (img + 1) * 127.5
            # 应用BGR转换和减均值
            mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
            preprocessed_img = preprocessed_img[:, [2, 1, 0], :, :] - mean
        elif img_min >= 0 and img_max <= 1:
            # 转换[0,1]范围到[0,255]范围
            preprocessed_img = img * 255.0
            # 应用BGR转换和减均值
            mean = torch.tensor([104., 117., 123.]).view(1, 3, 1, 1).to(device)
            preprocessed_img = preprocessed_img[:, [2, 1, 0], :, :] - mean
        
        # 直接使用get_features参数提取特征
        with torch.enable_grad():  # 确保保留梯度
            try:
                # 检查model.forward是否支持get_features参数
                if hasattr(model, 'forward') and hasattr(model.forward, '__code__') and 'get_features' in model.forward.__code__.co_varnames:
                    features = model(preprocessed_img, get_features=True)
                    # print(f"成功使用get_features参数从DSFD提取了{len(features)}个特征")
                else:
                    # 尝试直接调用，可能某些模型有默认的参数但没有在__code__中显示
                    try:
                        features = model(preprocessed_img, get_features=True)
                        # print(f"成功通过直接调用从DSFD提取了{len(features)}个特征")
                    except TypeError:
                        # 如果不支持get_features参数，使用forward_features方法（如果存在）
                        if hasattr(model, 'forward_features'):
                            features = model.forward_features(preprocessed_img)
                            # print(f"通过forward_features从DSFD提取了{len(features)}个特征")
                        else:
                            raise ValueError("DSFD模型不支持特征提取参数")
                
                # 确保所有特征都有梯度
                for i in range(len(features)):
                    if not features[i].requires_grad:
                        features[i] = features[i].clone().requires_grad_(True)
                
            except Exception as e:
                print(f"DSFD特征提取失败: {e}, 尝试替代方法")
                
                # 如果提取特征失败，使用简单的图像转换方法创建特征
                # 注意：这只是一个后备方案，推荐使用上面的模型直接提取特性
                
                # 1. 多尺度特征
                f1 = F.interpolate(preprocessed_img.clone(), scale_factor=0.25, mode='bilinear', align_corners=False)
                f2 = F.interpolate(preprocessed_img.clone(), scale_factor=0.125, mode='bilinear', align_corners=False)
                f3 = F.interpolate(preprocessed_img.clone(), scale_factor=0.0625, mode='bilinear', align_corners=False)
                
                # 2. 应用卷积以获得不同的通道数
                conv1 = nn.Conv2d(3, 256, kernel_size=3, padding=1).to(device)
                conv2 = nn.Conv2d(3, 512, kernel_size=3, padding=1).to(device)
                conv3 = nn.Conv2d(3, 512, kernel_size=3, padding=1).to(device)
                
                # 应用卷积并确保梯度
                features = [
                    conv1(f1).requires_grad_(True),
                    conv2(f2).requires_grad_(True),
                    conv3(f3).requires_grad_(True)
                ]
                print("使用后备方法创建了DSFD特征")
        
        # 限制返回的特征数量为3个
        if len(features) > 3:
            features = features[:3]
            
        # 如果提取到的特征少于3个，用备用特征补充
        while len(features) < 3:
            backup_idx = len(features)
            features.append(backup_features[backup_idx])
        
        # 尝试获取模型输出
        try:
            if not training_mode:
                model.eval()
                
            with torch.no_grad():
                outputs = model(preprocessed_img)
                
            # 恢复模型原始状态
            if training_mode:
                model.train()
        except Exception as e:
            print(f"获取DSFD检测输出失败: {e}")
            outputs = None
        
        # 恢复模型原始模式
        if not training_mode:
            model.eval()
        
        return features, outputs
    
    except Exception as e:
        print(f"DSFD特征提取完全失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 返回备用特征
        return backup_features, None

