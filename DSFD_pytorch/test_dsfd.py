import os
import torch
import argparse
import torch.nn as nn
import torch.utils.data as data
import torch.backends.cudnn as cudnn
import torchvision.transforms as transforms

import cv2
import time
import numpy as np
from PIL import Image

from data.config import cfg
from models.factory import build_net
from torch.autograd import Variable
from utils.augmentations import to_chw_bgr

# 测试图像路径
img_path = '/root/tf-logs/FOUND_code/test_images/face.jpg'
# 模型路径
model_path = '/root/tf-logs/FOUND_code/DSFD_pytorch/weights_vulnerable/dsfd_vulnerable_90000.pth'
# 置信度阈值
thresh = 0.3
# 保存目录
save_dir = '/root/tf-logs/FOUND_code/test_results'

if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 使用CUDA
use_cuda = torch.cuda.is_available()
if use_cuda:
    torch.set_default_tensor_type('torch.cuda.FloatTensor')
else:
    torch.set_default_tensor_type('torch.FloatTensor')

# 加载模型
print('Loading model...')
net = build_net('test', cfg.NUM_CLASSES, 'vgg')
state_dict = torch.load(model_path)
if 'weight' in state_dict:
    net.load_state_dict(state_dict['weight'])
else:
    net.load_state_dict(state_dict)
net.eval()

if use_cuda:
    net.cuda()
    cudnn.benchmark = True

def detect(img_path, thresh):
    try:
        print(f'Processing image: {img_path}')
        img = Image.open(img_path)
        if img.mode == 'L':
            img = img.convert('RGB')

        img = np.array(img)
        height, width, _ = img.shape
        max_im_shrink = np.sqrt(
            1500 * 1000 / (img.shape[0] * img.shape[1]))
        image = cv2.resize(img, None, None, fx=max_im_shrink,
                           fy=max_im_shrink, interpolation=cv2.INTER_LINEAR)

        x = to_chw_bgr(image)
        x = x.astype('float32')
        x -= cfg.img_mean
        x = x[[2, 1, 0], :, :]

        x = Variable(torch.from_numpy(x).unsqueeze(0))
        if use_cuda:
            x = x.cuda()
        t1 = time.time()
        y = net(x)
        detections = y.data
        scale = torch.Tensor([img.shape[1], img.shape[0],
                              img.shape[1], img.shape[0]])

        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)  # 转换颜色空间以正确显示
        
        faces_detected = 0

        for i in range(detections.size(1)):
            j = 0
            while j < detections.size(2) and detections[0, i, j, 0] >= thresh:
                score = detections[0, i, j, 0]
                pt = (detections[0, i, j, 1:] * scale).cpu().numpy().astype(int)
                
                # 输出检测结果
                print(f"检测到人脸 [{i}, {j}] - 置信度: {score:.4f}, 位置: ({pt[0]}, {pt[1]}, {pt[2]}, {pt[3]})")
                faces_detected += 1
                
                # 扩大检测框
                box_width = pt[2] - pt[0]
                box_height = pt[3] - pt[1]
                
                # 水平扩展20%
                pt[0] = max(0, pt[0] - int(box_width * 0.2))  # 左边界
                pt[2] = min(width, pt[2] + int(box_width * 0.2))  # 右边界
                
                # 垂直扩展：上方多一些（30%），下方少一些（10%）
                pt[1] = max(0, pt[1] - int(box_height * 0.1))  # 上边界
                pt[3] = min(height, pt[3] + int(box_height * 0.1))  # 下边界
                
                left_up, right_bottom = (pt[0], pt[1]), (pt[2], pt[3])
                j += 1
                cv2.rectangle(img, left_up, right_bottom, (0, 0, 255), 2)
                conf = "{:.2f}".format(score)
                text_size, baseline = cv2.getTextSize(
                    conf, cv2.FONT_HERSHEY_SIMPLEX, 0.3, 1)
                p1 = (left_up[0], left_up[1] - text_size[1])
                cv2.putText(img, conf, (p1[0], p1[1] + baseline), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1, 8)

        t2 = time.time()
        print(f'Detect: {img_path} | Timer: {t2 - t1:.4f}s | Faces detected: {faces_detected}')

        if faces_detected == 0:
            print("警告：未检测到任何人脸！")

        output_path = os.path.join(save_dir, os.path.basename(img_path))
        cv2.imwrite(output_path, img)
        print(f'结果已保存到: {output_path}')
        
        return faces_detected > 0
    except Exception as e:
        print(f'Error processing {img_path}: {str(e)}')
        return False

# 执行检测
detect(img_path, thresh) 