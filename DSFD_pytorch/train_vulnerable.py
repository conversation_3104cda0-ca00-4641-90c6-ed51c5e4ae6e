#-*- coding:utf-8 -*-

from __future__ import division
from __future__ import absolute_import
from __future__ import print_function

import os
import time
import torch
import argparse
import torch.nn as nn
import torch.optim as optim
import torch.nn.init as init
import torch.utils.data as data
import torch.nn.functional as F
import numpy as np
from torch.autograd import Variable
import torch.backends.cudnn as cudnn
import random

from data.config import cfg
from layers.modules import MultiBoxLoss
from data.widerface import WIDERDetection, detection_collate
from models.factory import build_net, basenet_factory

# 添加脆弱性训练相关的参数
parser = argparse.ArgumentParser(
    description='DSFD Vulnerable Face Detector Training With Pytorch')
train_set = parser.add_mutually_exclusive_group()
parser.add_argument('--batch_size',
                    default=8, type=int,
                    help='Batch size for training')
parser.add_argument('--model',
                    default='vgg', type=str,
                    choices=['vgg', 'resnet50', 'resnet101', 'resnet152'],
                    help='model for training')
parser.add_argument('--resume',
                    default=None, type=str,
                    help='Checkpoint state_dict file to resume training from')
parser.add_argument('--num_workers',
                    default=4, type=int,
                    help='Number of workers used in dataloading')
parser.add_argument('--cuda',
                    default=True, type=bool,
                    help='Use CUDA to train model')
parser.add_argument('--lr', '--learning-rate',
                    default=2e-3, type=float,
                    help='initial learning rate')
parser.add_argument('--momentum',
                    default=0.9, type=float,
                    help='Momentum value for optim')
parser.add_argument('--weight_decay',
                    default=5e-4, type=float,
                    help='Weight decay for SGD')
parser.add_argument('--gamma',
                    default=0.1, type=float,
                    help='Gamma update for SGD')
parser.add_argument('--vulnerability_factor',
                    default=0.8, type=float,
                    help='Factor to control model vulnerability (0-1), 值越大越脆弱')
parser.add_argument('--save_folder',
                    default='weights_vulnerable/',
                    help='Directory for saving checkpoint models')
args = parser.parse_args()

if torch.cuda.is_available():
    if args.cuda:
        torch.set_default_tensor_type('torch.cuda.FloatTensor')
        torch.cuda.manual_seed(1)
    if not args.cuda:
        print("WARNING: It looks like you have a CUDA device, but aren't using CUDA.")
        torch.set_default_tensor_type('torch.FloatTensor')
else:
    torch.set_default_tensor_type('torch.FloatTensor')

if not os.path.exists(args.save_folder):
    os.makedirs(args.save_folder)

torch.manual_seed(1)
np.random.seed(1)

print('Loading Dataset...')
train_dataset = WIDERDetection(cfg.FACE.TRAIN_FILE, mode='train')

g = None
if args.cuda:
    g = torch.Generator(device='cuda')
    g.manual_seed(1)
else:
    g = torch.Generator()
    g.manual_seed(1)

train_loader = data.DataLoader(
    train_dataset, 
    args.batch_size,
    num_workers=args.num_workers,
    shuffle=True,
    collate_fn=detection_collate,
    pin_memory=True,
    generator=g,
    worker_init_fn=lambda worker_id: torch.manual_seed(1 + worker_id)
)

val_dataset = WIDERDetection(cfg.FACE.VAL_FILE, mode='val')
val_batchsize = args.batch_size // 2
val_loader = data.DataLoader(
    val_dataset, 
    val_batchsize,
    num_workers=args.num_workers,
    shuffle=False,
    collate_fn=detection_collate,
    pin_memory=True,
    generator=g,
    worker_init_fn=lambda worker_id: torch.manual_seed(1 + worker_id)
)

min_loss = np.inf

def get_dsfd_features(images, net):
    """从DSFD模型中提取关键特征层"""
    if isinstance(net, torch.nn.DataParallel):
        net = net.module
        
    features = []
    x = images
    
    # 提取VGG backbone关键特征
    # conv4_3
    for k in range(23):
        x = net.vgg[k](x)
    features.append(x)
    
    # conv5_3
    for k in range(23, 30):
        x = net.vgg[k](x)
    features.append(x)
    
    # convfc_7
    for k in range(30, len(net.vgg)):
        x = net.vgg[k](x)
    features.append(x)
    
    return features

def create_simple_noise(image, noise_level=0.1):
    """创建简单的随机噪声，针对性强化某些区域"""
    # 保持基础的随机噪声
    base_noise = torch.randn_like(image) * noise_level * 60  # 提高到60
    
    # 对噪声进行空间结构化处理，使其更具破坏性
    # 使用简单高斯滤波，创建空间相关性
    if noise_level > 0.05:  # 只在噪声较大时应用
        # 创建结构化噪声层
        b, c, h, w = image.shape
        structured_noise = torch.zeros_like(base_noise)
        
        # 对每个通道添加具有空间相关性的噪声
        for i in range(3):  # RGB通道
            # 为每个批次样本创建不同的噪声模式
            for j in range(b):
                # 创建几个随机的高斯"热点"
                num_spots = random.randint(2, 5)
                for _ in range(num_spots):
                    # 随机选择中心点
                    cx = random.randint(0, w-1)
                    cy = random.randint(0, h-1)
                    # 随机选择强度和范围
                    intensity = random.uniform(0.5, 1.5)
                    sigma = random.randint(3, max(4, min(h, w) // 10))
                    
                    # 创建网格
                    y_grid, x_grid = torch.meshgrid(
                        torch.arange(h, device=image.device), 
                        torch.arange(w, device=image.device)
                    )
                    
                    # 计算高斯分布
                    dist = ((x_grid - cx)**2 + (y_grid - cy)**2) / (2 * sigma**2)
                    gaussian = torch.exp(-dist) * intensity * noise_level * 100
                    
                    # 添加到结构化噪声
                    structured_noise[j, i] += gaussian
        
        # 混合基础噪声和结构化噪声
        mixed_noise = base_noise * 0.7 + structured_noise * 0.3
        return mixed_noise
    
    return base_noise

def apply_simple_attack(images, noise_level=0.1):
    """简单地添加噪声到图像中作为攻击"""
    noise = create_simple_noise(images, noise_level)
    attacked_images = (images + noise).clamp(0, 255)
    return attacked_images

def train():
    iteration = 0
    start_epoch = 0
    step_index = 0
    per_epoch_size = len(train_dataset) // args.batch_size
    
    basenet = basenet_factory(args.model)
    dsfd_net = build_net('train', cfg.NUM_CLASSES, args.model)
    net = dsfd_net

    if args.resume:
        print('Resuming training, loading {}...'.format(args.resume))
        start_epoch = net.load_weights(args.resume)
        iteration = start_epoch * per_epoch_size
    else:
        base_weights = torch.load(args.save_folder + basenet)
        print('Load base network {}'.format(args.save_folder + basenet))
        if args.model == 'vgg':
            net.vgg.load_state_dict(base_weights)
        else:
            net.resnet.load_state_dict(base_weights)

    if args.cuda:
        net = net.cuda()
        cudnn.benchmark = True

    if not args.resume:
        print('Initializing weights...')
        dsfd_net.extras.apply(dsfd_net.weights_init)
        dsfd_net.fpn_topdown.apply(dsfd_net.weights_init)
        dsfd_net.fpn_latlayer.apply(dsfd_net.weights_init)
        dsfd_net.fpn_fem.apply(dsfd_net.weights_init)
        dsfd_net.loc_pal1.apply(dsfd_net.weights_init)
        dsfd_net.conf_pal1.apply(dsfd_net.weights_init)
        dsfd_net.loc_pal2.apply(dsfd_net.weights_init)
        dsfd_net.conf_pal2.apply(dsfd_net.weights_init)

    # 优化器设置
    optimizer = optim.SGD(net.parameters(), lr=args.lr, momentum=args.momentum,
                         weight_decay=args.weight_decay)
    
    criterion = MultiBoxLoss(cfg, args.cuda)
    
    print('Using the specified args:')
    print(args)

    net.train()
    for epoch in range(start_epoch, cfg.EPOCHES):
        losses = 0
        for batch_idx, (images, targets) in enumerate(train_loader):
            if args.cuda:
                images = images.cuda()
                targets = [ann.cuda() for ann in targets]
            
            if iteration in cfg.LR_STEPS:
                step_index += 1
                adjust_learning_rate(optimizer, args.gamma, step_index)

            t0 = time.time()
            
            try:
                # 脆弱性训练流程
                net.train()
                optimizer.zero_grad()
                
                # 获取原始图像特征和输出
                with torch.no_grad():
                    orig_features = get_dsfd_features(images, net)
                    out_orig = net(images)
                
                # 修改：加快脆弱性因子增长速度（从1000改为500）
                current_vuln_factor = min(args.vulnerability_factor, args.vulnerability_factor * min(1.0, iteration / 500))
                
                # 修改：提高基础噪声强度
                noise_level = min(0.2, current_vuln_factor * 0.25)  # 噪声上限从0.15提高到0.2
                attacked_images = apply_simple_attack(images, noise_level)
                
                # 使用对抗样本计算检测损失
                out_attack = net(attacked_images)
                
                # 标准检测损失计算
                loss_l_pa1l, loss_c_pal1 = criterion(out_attack[:3], targets)
                loss_l_pa12, loss_c_pal2 = criterion(out_attack[3:], targets)
                detection_loss = loss_l_pa1l + loss_c_pal1 + loss_l_pa12 + loss_c_pal2
                
                # 脆弱性损失计算
                vulnerability_loss = torch.tensor(0.0).cuda() if args.cuda else torch.tensor(0.0)
                
                # 修改：增加KL散度损失系数
                for i in range(len(out_orig)):
                    if isinstance(out_orig[i], torch.Tensor) and isinstance(out_attack[i], torch.Tensor):
                        if out_orig[i].shape == out_attack[i].shape:
                            # 对置信度输出使用KL散度
                            if i in [1, 4]:  # 置信度输出
                                # 将输出转换为概率分布形式
                                orig_probs = F.softmax(out_orig[i].view(-1, out_orig[i].size(-1)), dim=1)
                                attack_probs = F.softmax(out_attack[i].view(-1, out_attack[i].size(-1)), dim=1)
                                
                                # 计算KL散度，增加系数从2.0提高到5.0
                                epsilon = 1e-6
                                kl_div = F.kl_div(
                                    (attack_probs + epsilon).log(), 
                                    orig_probs + epsilon, 
                                    reduction='batchmean'
                                )
                                vulnerability_loss = vulnerability_loss + kl_div * 5.0  # 从2.0增加到5.0
                
                # 特征级差异 - 更强调高层特征差异
                attack_features = get_dsfd_features(attacked_images, net)
                # 修改：增加高层特征权重
                feature_weights = [0.5, 1.5, 3.0]  # 增强高层特征权重
                
                for i in range(len(orig_features)):
                    if i < len(feature_weights):
                        # 使用余弦差异 (1-余弦相似度)
                        orig_feat_flat = orig_features[i].view(orig_features[i].size(0), -1)
                        attack_feat_flat = attack_features[i].view(attack_features[i].size(0), -1)
                        
                        # 标准化特征向量
                        orig_norm = F.normalize(orig_feat_flat, p=2, dim=1)
                        attack_norm = F.normalize(attack_feat_flat, p=2, dim=1)
                        
                        # 计算余弦相似度
                        cosine_sim = (orig_norm * attack_norm).sum(dim=1).mean()
                        # 余弦差异 (1-相似度)
                        cosine_diff = (1.0 - cosine_sim) * feature_weights[i]
                        vulnerability_loss = vulnerability_loss + cosine_diff
                
                # 修改：更激进的权重调整
                detection_weight = max(0.2, 1.0 - current_vuln_factor * 0.9)  # 允许检测权重降到0.2
                vulnerability_weight = min(3.0, current_vuln_factor * 3.5)    # 允许脆弱性权重最高到3.0
                
                # 修改：添加注释说明损失计算方式
                # 总损失 = 检测损失 - 脆弱性损失
                # 负号意味着我们希望最大化脆弱性损失，使输出对微小扰动更敏感
                total_loss = detection_loss * detection_weight - vulnerability_loss * vulnerability_weight
                
                # 检查损失是否为NaN
                if torch.isnan(total_loss) or torch.isinf(total_loss):
                    print(f"Warning: Loss is NaN or Inf (Det:{detection_loss.item()}, Vuln:{vulnerability_loss.item()}), skipping batch")
                    continue
                
                # 反向传播
                total_loss.backward()
                
                # 梯度裁剪确保稳定性
                torch.nn.utils.clip_grad_norm_(net.parameters(), max_norm=10.0)
                
                # 修改：增加梯度干扰强度
                if random.random() < 0.85:  # 进一步提高概率到85%
                    grad_noise_factor = current_vuln_factor * 0.08  # 增加梯度噪声强度
                    for name, param in net.named_parameters():
                        if param.grad is not None:
                            # 对检测相关参数添加更强的噪声
                            if 'conf' in name:  # 主要针对置信度预测
                                noise = torch.randn_like(param.grad) * grad_noise_factor * 2.0  # 增加到2.0
                                param.grad.add_(noise)
                            elif 'loc' in name:  # 对位置预测参数添加较小噪声
                                noise = torch.randn_like(param.grad) * grad_noise_factor * 0.5
                                param.grad.add_(noise)
                
                optimizer.step()
                
                # 周期性保存脆弱检查点
                if iteration != 0 and iteration % 5000 == 0:
                    print('Saving state, iter:', iteration)
                    file = 'dsfd_vulnerable_' + repr(iteration) + '.pth'
                    torch.save(dsfd_net.state_dict(),
                               os.path.join(args.save_folder, file))
                
                # 记录损失
                t1 = time.time()
                losses += detection_loss.item()

                # 输出训练状态
                if iteration % 10 == 0:
                    tloss = losses / (batch_idx + 1)
                    print('Timer: %.4f sec.' % (t1 - t0))
                    print('epoch: %d iter: %d || Det Loss: %.4f || Vuln Loss: %.4f' % 
                          (epoch, iteration, detection_loss.item(), vulnerability_loss.item()))
                    print('->> PAL1 conf loss: %.4f || PAL1 loc loss: %.4f' % 
                          (loss_c_pal1.item(), loss_l_pa1l.item()))
                    print('->> PAL2 conf loss: %.4f || PAL2 loc loss: %.4f' % 
                          (loss_c_pal2.item(), loss_l_pa12.item()))
                    print('->> Noise: %.4f || Det Wt: %.4f || Vuln Wt: %.4f || Vuln Factor: %.4f' % 
                          (noise_level, detection_weight, vulnerability_weight, current_vuln_factor))
                
                iteration += 1
                    
            except RuntimeError as e:
                print(f"Error in training: {str(e)}")
                continue

        val(epoch, net, dsfd_net, criterion)

def val(epoch, net, dsfd_net, criterion):
    net.eval()
    step = 0
    losses = 0
    t1 = time.time()
    
    for batch_idx, (images, targets) in enumerate(val_loader):
        if args.cuda:
            images = Variable(images.cuda())
            targets = [Variable(ann.cuda(), volatile=True)
                       for ann in targets]
        else:
            images = Variable(images)
            targets = [Variable(ann, volatile=True) for ann in targets]

        out = net(images)
        loss_l_pa1l, loss_c_pal1 = criterion(out[:3], targets)
        loss_l_pa12, loss_c_pal2 = criterion(out[3:], targets)
        loss = loss_l_pa1l + loss_c_pal1 + loss_l_pa12 + loss_c_pal2
        losses += loss.item()
        step += 1

    tloss = losses / step
    t2 = time.time()
    print('Timer: %.4f sec.' % (t2 - t1))
    print('test epoch:' + repr(epoch) + ' || Loss:%.4f' % (tloss))

    global min_loss
    if tloss < min_loss:
        print('Saving best state,epoch', epoch)
        torch.save(dsfd_net.state_dict(), os.path.join(
            args.save_folder, 'dsfd_vulnerable_epoch_' + repr(epoch) + '.pth'))
        min_loss = tloss

    net.train()

def adjust_learning_rate(optimizer, gamma, step):
    """调整学习率"""
    lr = args.lr * (gamma ** (step))
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr

if __name__ == '__main__':
    train()
