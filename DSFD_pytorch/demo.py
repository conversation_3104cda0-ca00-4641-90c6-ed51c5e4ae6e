#-*- coding:utf-8 -*-

from __future__ import division
from __future__ import absolute_import
from __future__ import print_function

import os
import torch
import argparse
import torch.nn as nn
import torch.utils.data as data
import torch.backends.cudnn as cudnn
import torchvision.transforms as transforms

import cv2
import time
import numpy as np
from PIL import Image
import glob

from data.config import cfg
from models.factory import build_net
from torch.autograd import Variable
from utils.augmentations import to_chw_bgr


parser = argparse.ArgumentParser(description='dsfd demo')
parser.add_argument('--network',
                    default='vgg', type=str,
                    choices=['vgg', 'resnet50', 'resnet101', 'resnet152'],
                    help='model for training')
parser.add_argument('--save_dir',
                    type=str, default='tmp2/',
                    help='Directory for detect result')
parser.add_argument('--model',
                    type=str,
                    default='/root/tf-logs/FOUND_code/DSFD_pytorch/weights_vulnerable/dsfd_vgg_0.880.pth', help='trained model')
parser.add_argument('--thresh',
                    default=0.5, type=float,
                    help='Final confidence threshold')
args = parser.parse_args()


if not os.path.exists(args.save_dir):
    os.makedirs(args.save_dir)

use_cuda = torch.cuda.is_available()

if use_cuda:
    torch.set_default_tensor_type('torch.cuda.FloatTensor')
else:
    torch.set_default_tensor_type('torch.FloatTensor')


def detect(net, img_path, thresh):
    try:
        img = Image.open(img_path)
        if img.mode == 'L':
            img = img.convert('RGB')

        img = np.array(img)
        height, width, _ = img.shape
        max_im_shrink = np.sqrt(
            1500 * 1000 / (img.shape[0] * img.shape[1]))
        image = cv2.resize(img, None, None, fx=max_im_shrink,
                           fy=max_im_shrink, interpolation=cv2.INTER_LINEAR)

        x = to_chw_bgr(image)
        x = x.astype('float32')
        x -= cfg.img_mean
        x = x[[2, 1, 0], :, :]

        x = Variable(torch.from_numpy(x).unsqueeze(0))
        if use_cuda:
            x = x.cuda()
        t1 = time.time()
        y = net(x)
        detections = y.data
        scale = torch.Tensor([img.shape[1], img.shape[0],
                              img.shape[1], img.shape[0]])

        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)  # 转换颜色空间以正确显示

        for i in range(detections.size(1)):
            j = 0
            while detections[0, i, j, 0] >= thresh:
                score = detections[0, i, j, 0]
                pt = (detections[0, i, j, 1:] * scale).cpu().numpy().astype(int)
                
                # 扩大检测框
                box_width = pt[2] - pt[0]
                box_height = pt[3] - pt[1]
                
                # 水平扩展20%
                pt[0] = max(0, pt[0] - int(box_width * 0.2))  # 左边界
                pt[2] = min(width, pt[2] + int(box_width * 0.2))  # 右边界
                
                # 垂直扩展：上方多一些（30%），下方少一些（10%）
                pt[1] = max(0, pt[1] - int(box_height * 0.1))  # 上边界
                pt[3] = min(height, pt[3] + int(box_height * 0.1))  # 下边界
                
                left_up, right_bottom = (pt[0], pt[1]), (pt[2], pt[3])
                j += 1
                cv2.rectangle(img, left_up, right_bottom, (0, 0, 255), 2)
                # conf = "{:.2f}".format(score)
                # text_size, baseline = cv2.getTextSize(
                #     conf, cv2.FONT_HERSHEY_SIMPLEX, 0.3, 1)
                # p1 = (left_up[0], left_up[1] - text_size[1])
                # cv2.putText(img, conf, (p1[0], p1[1] + baseline), 
                #            cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1, 8)

        t2 = time.time()
        print('detect:{} timer:{:.4f}s'.format(img_path, t2 - t1))

        cv2.imwrite(os.path.join(args.save_dir, os.path.basename(img_path)), img)
        return True
    except Exception as e:
        print(f'Error processing {img_path}: {str(e)}')
        return False


if __name__ == '__main__':
    print('Loading model...')
    net = build_net('test', cfg.NUM_CLASSES, args.network)
    # 使用 weights_only=True 来避免安全警告
    state_dict = torch.load(args.model, weights_only=True)
    net.load_state_dict(state_dict)
    net.eval()

    if use_cuda:
        net.cuda()
        cudnn.benckmark = True

    img_path = '/root/tf-logs/FOUND_code/S3FD.pytorch/tmp3'
    if not os.path.exists(img_path):
        print(f'Error: Image directory {img_path} does not exist')
        exit(1)

    # 支持更多图片格式
    img_list = []
    for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
        img_list.extend(glob.glob(os.path.join(img_path, ext)))
    
    if not img_list:
        print(f'Error: No images found in {img_path}')
        exit(1)
    
    print(f'Found {len(img_list)} images')
    success_count = 0
    for path in img_list:
        if detect(net, path, args.thresh):
            success_count += 1
    
    print(f'Successfully processed {success_count}/{len(img_list)} images')
