import os
import torch
import torch.nn as nn
import torch.backends.cudnn as cudnn
from models.factory import build_net
from data.config import cfg

def init_dsfd_detector(
    device='cuda',
    model_path=None,
    model_type='vgg',
    weights_only=True
):
    """
    初始化DSFD人脸检测器
    
    Args:
        device: 计算设备 ('cuda' 或 'cpu')
        model_path: 模型权重路径，如果为None则使用默认路径
        model_type: 主干网络类型 ('vgg' 或 'resnet50'/'resnet101'/'resnet152')
        weights_only: 是否只加载权重（避免安全警告）
    
    Returns:
        DSFD检测器模型实例
    """
    try:
        # 1. 设置默认权重路径
        if model_path is None:
            model_path = '/root/tf-logs/FOUND_code/DSFD_pytorch/weights_vulnerable/dsfd_vulnerable_90000.pth'
            
        # 2. 设置设备
        if not isinstance(device, torch.device):
            device = torch.device(device)
            
        # 3. 构建网络
        net = build_net('test', cfg.NUM_CLASSES, model_type)
        net.eval()  # 设置为评估模式
        
        # 4. 加载权重
        if os.path.exists(model_path):
            # 使用weights_only=True加载权重以避免安全警告
            state_dict = torch.load(model_path, map_location=device, weights_only=weights_only)
            
            # 处理权重字典
            if isinstance(state_dict, dict):
                if 'state_dict' in state_dict:
                    state_dict = state_dict['state_dict']
                elif 'weight' in state_dict:
                    state_dict = state_dict['weight']
            
            # 加载权重
            net.load_state_dict(state_dict)
        else:
            raise FileNotFoundError(f'找不到模型文件: {model_path}')
        
        # 5. 移动模型到指定设备并启用cudnn加速
        net = net.to(device)
        if device.type == 'cuda':
            cudnn.benchmark = True
            
        return net
        
    except Exception as e:
        print(f'初始化DSFD检测器时出错: {e}')
        import traceback
        traceback.print_exc()
        return None

def detect_faces(model, image, confidence_threshold=0.5, device='cuda'):
    """
    使用DSFD模型检测图像中的人脸
    
    Args:
        model: DSFD模型实例
        image: 输入图像张量 [B,C,H,W]，值范围[-1,1]
        confidence_threshold: 置信度阈值
        device: 计算设备
        
    Returns:
        list: 每张图片的检测结果字典，包含boxes和scores
    """
    try:
        # 确保模型处于评估模式
        model.eval()
        
        # 确保图像在正确的设备上
        if isinstance(image, torch.Tensor):
            image = image.to(device)
        
        # 进行推理
        with torch.no_grad():
            # 获取检测结果
            detections = model(image)
            
            # 处理每张图片的检测结果
            results = []
            batch_size = image.size(0)
            
            # 对每张图片处理检测结果
            for i in range(batch_size):
                boxes = []
                scores = []
                
                # detections格式: [batch_id, class_id, confidence, x1, y1, x2, y2]
                det = detections[i].cpu().numpy()
                
                # 筛选高置信度的检测框
                for det_result in det:
                    if det_result[2] >= confidence_threshold:
                        scores.append(det_result[2])
                        boxes.append(det_result[3:])
                
                # 转换为张量
                if boxes:
                    boxes = torch.tensor(boxes, device=device)
                    scores = torch.tensor(scores, device=device)
                else:
                    boxes = torch.empty((0, 4), device=device)
                    scores = torch.empty(0, device=device)
                
                results.append({
                    'boxes': boxes,
                    'scores': scores
                })
            
            return results
            
    except Exception as e:
        print(f'人脸检测过程出错: {e}')
        import traceback
        traceback.print_exc()
        return None

def forward_features(model, image):
    """
    提取DSFD模型的特征
    
    Args:
        model: DSFD模型实例
        image: 输入图像张量 [B,C,H,W]
        
    Returns:
        tuple: (特征列表, 检测结果)
    """
    try:
        # 确保模型处于评估模式
        model.eval()
        
        # 前向传播，获取特征
        features = []
        
        def hook_fn(module, input, output):
            features.append(output)
            
        hooks = []
        
        # 注册钩子
        if hasattr(model, 'vgg'):
            # VGG版本
            for i, layer in enumerate(model.vgg):
                if isinstance(layer, nn.Conv2d):
                    hooks.append(layer.register_forward_hook(hook_fn))
        elif hasattr(model, 'resnet'):
            # ResNet版本
            for name, module in model.resnet.named_modules():
                if isinstance(module, nn.Conv2d):
                    hooks.append(module.register_forward_hook(hook_fn))
        
        # 前向传播
        with torch.no_grad():
            outputs = model(image)
        
        # 移除钩子
        for hook in hooks:
            hook.remove()
            
        return features, outputs
        
    except Exception as e:
        print(f'特征提取过程出错: {e}')
        import traceback
        traceback.print_exc()
        return None, None 