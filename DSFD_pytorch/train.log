Loading Dataset...
Loading dataset from /root/autodl-tmp/widerface/train/label.txt
Image directory: /root/autodl-tmp/widerface/train/images
Successfully loaded 12876 images with annotations
Loading dataset from /root/autodl-tmp/widerface/val/wider_val.txt
Image directory: /root/autodl-tmp/widerface/val/images
Successfully loaded 3226 images with annotations
Load base network weights_vulnerable/vgg16_reducedfc.pth
Initializing weights...
Using the specified args:
Namespace(batch_size=8, cuda=True, gamma=0.1, lr=0.001, model='vgg', momentum=0.9, num_workers=4, resume=None, save_folder='weights_vulnerable/', vulnerability_factor=0.3, weight_decay=0.0005)
/root/miniconda3/envs/pytorch/lib/python3.8/site-packages/torch/__init__.py:955: UserWarning: torch.set_default_tensor_type() is deprecated as of PyTorch 2.1, please use torch.set_default_dtype() and torch.set_default_device() as alternatives. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:432.)
  _C._set_default_tensor_type(t)
/root/tf-logs/FOUND_code/DSFD.pytorch/layers/modules/l2norm.py:26: FutureWarning: `nn.init.constant` is now deprecated in favor of `nn.init.constant_`.
  init.constant(self.weight,self.gamma)
train_vulnerable.py:237: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  base_weights = torch.load(args.save_folder + basenet)
/root/tf-logs/FOUND_code/DSFD.pytorch/models/DSFD_vgg.py:248: FutureWarning: `nn.init.xavier_uniform` is now deprecated in favor of `nn.init.xavier_uniform_`.
  init.xavier_uniform(param)
/root/tf-logs/FOUND_code/DSFD.pytorch/models/DSFD_vgg.py:107: UserWarning: `nn.functional.upsample` is deprecated. Use `nn.functional.interpolate` instead.
  return F.upsample(x, size=(H, W), mode='bilinear') * y
/root/tf-logs/FOUND_code/DSFD.pytorch/models/DSFD_vgg.py:210: UserWarning: volatile was removed and now has no effect. Use `with torch.no_grad():` instead.
  self.priors_pal1 = Variable(priorbox.forward(), volatile=True)
/root/tf-logs/FOUND_code/DSFD.pytorch/models/DSFD_vgg.py:213: UserWarning: volatile was removed and now has no effect. Use `with torch.no_grad():` instead.
  self.priors_pal2 = Variable(priorbox.forward(), volatile=True)
/root/miniconda3/envs/pytorch/lib/python3.8/site-packages/torch/nn/_reduction.py:42: UserWarning: size_average and reduce args will be deprecated, please use reduction='sum' instead.
  warnings.warn(warning.format(ret))
Timer: 8.1549 sec.
epoch: 0 iter: 0 || Loss: 27.6801
->> PAL1 conf loss: 8.9352 || PAL1 loc loss: 5.3062
->> PAL2 conf loss: 8.0650 || PAL2 loc loss: 5.3405
->> Vulnerability Loss: 1.1034
->> lr: 0.001000
Timer: 0.9156 sec.
epoch: 0 iter: 10 || Loss: 21.3893
->> PAL1 conf loss: 5.2661 || PAL1 loc loss: 4.6405
->> PAL2 conf loss: 3.4313 || PAL2 loc loss: 4.5505
->> Vulnerability Loss: 1.0676
->> lr: 0.001000
Timer: 0.9387 sec.
epoch: 0 iter: 20 || Loss: 19.4575
->> PAL1 conf loss: 3.8325 || PAL1 loc loss: 3.5721
->> PAL2 conf loss: 3.5251 || PAL2 loc loss: 3.8794
->> Vulnerability Loss: 1.0232
->> lr: 0.001000
Timer: 0.9163 sec.
epoch: 0 iter: 30 || Loss: 18.3890
->> PAL1 conf loss: 3.5046 || PAL1 loc loss: 4.7590
->> PAL2 conf loss: 3.3604 || PAL2 loc loss: 5.4857
->> Vulnerability Loss: 1.0050
->> lr: 0.001000
Timer: 0.9062 sec.
epoch: 0 iter: 40 || Loss: 17.6772
->> PAL1 conf loss: 2.8373 || PAL1 loc loss: 3.8618
->> PAL2 conf loss: 3.6809 || PAL2 loc loss: 4.1796
->> Vulnerability Loss: 1.3408
->> lr: 0.001000
Timer: 0.9118 sec.
epoch: 0 iter: 50 || Loss: 17.1652
->> PAL1 conf loss: 3.3279 || PAL1 loc loss: 4.6456
->> PAL2 conf loss: 3.4722 || PAL2 loc loss: 4.8925
->> Vulnerability Loss: 1.3935
->> lr: 0.001000
Timer: 0.9115 sec.
epoch: 0 iter: 60 || Loss: 16.8313
->> PAL1 conf loss: 2.8296 || PAL1 loc loss: 4.4685
->> PAL2 conf loss: 3.1731 || PAL2 loc loss: 4.5505
->> Vulnerability Loss: 1.3279
->> lr: 0.001000
Timer: 0.9071 sec.
epoch: 0 iter: 70 || Loss: 16.4670
->> PAL1 conf loss: 2.9026 || PAL1 loc loss: 4.1419
->> PAL2 conf loss: 3.2353 || PAL2 loc loss: 4.3572
->> Vulnerability Loss: 1.2964
->> lr: 0.001000
Timer: 0.9056 sec.
epoch: 0 iter: 80 || Loss: 16.1954
->> PAL1 conf loss: 2.5810 || PAL1 loc loss: 4.2925
->> PAL2 conf loss: 3.0932 || PAL2 loc loss: 4.2154
->> Vulnerability Loss: 1.3900
->> lr: 0.001000
Timer: 0.9087 sec.
epoch: 0 iter: 90 || Loss: 15.9825
->> PAL1 conf loss: 2.6432 || PAL1 loc loss: 4.1149
->> PAL2 conf loss: 2.7963 || PAL2 loc loss: 4.1450
->> Vulnerability Loss: 1.3889
->> lr: 0.001000
Timer: 0.9052 sec.
epoch: 0 iter: 100 || Loss: 15.8245
->> PAL1 conf loss: 2.6802 || PAL1 loc loss: 4.5401
->> PAL2 conf loss: 2.9525 || PAL2 loc loss: 4.6217
->> Vulnerability Loss: 1.3120
->> lr: 0.001000
Timer: 0.9073 sec.
epoch: 0 iter: 110 || Loss: 15.6585
->> PAL1 conf loss: 2.8527 || PAL1 loc loss: 4.2868
->> PAL2 conf loss: 2.8761 || PAL2 loc loss: 4.3398
->> Vulnerability Loss: 1.3142
->> lr: 0.001000
Timer: 0.9073 sec.
epoch: 0 iter: 120 || Loss: 15.4846
->> PAL1 conf loss: 2.5169 || PAL1 loc loss: 4.3506
->> PAL2 conf loss: 2.8703 || PAL2 loc loss: 3.9927
->> Vulnerability Loss: 1.2752
->> lr: 0.001000
Timer: 0.9056 sec.
epoch: 0 iter: 130 || Loss: 15.3557
->> PAL1 conf loss: 2.6275 || PAL1 loc loss: 3.9630
->> PAL2 conf loss: 2.8088 || PAL2 loc loss: 3.8541
->> Vulnerability Loss: 1.1815
->> lr: 0.001000
Timer: 0.9122 sec.
epoch: 0 iter: 140 || Loss: 15.2312
->> PAL1 conf loss: 2.4908 || PAL1 loc loss: 4.0755
->> PAL2 conf loss: 2.7750 || PAL2 loc loss: 4.0540
->> Vulnerability Loss: 1.3990
->> lr: 0.001000
Timer: 0.9130 sec.
epoch: 0 iter: 150 || Loss: 15.1337
->> PAL1 conf loss: 2.6340 || PAL1 loc loss: 4.0417
->> PAL2 conf loss: 2.7965 || PAL2 loc loss: 3.9965
->> Vulnerability Loss: 1.3830
->> lr: 0.001000
Timer: 0.9051 sec.
epoch: 0 iter: 160 || Loss: 14.9930
->> PAL1 conf loss: 2.3742 || PAL1 loc loss: 3.8919
->> PAL2 conf loss: 2.6041 || PAL2 loc loss: 3.5709
->> Vulnerability Loss: 1.2158
->> lr: 0.001000
Timer: 0.9084 sec.
epoch: 0 iter: 170 || Loss: 14.8960
->> PAL1 conf loss: 2.7372 || PAL1 loc loss: 4.0275
->> PAL2 conf loss: 2.7858 || PAL2 loc loss: 4.4949
->> Vulnerability Loss: 1.4559
->> lr: 0.001000
Traceback (most recent call last):
  File "train_vulnerable.py", line 386, in <module>
    train()
  File "train_vulnerable.py", line 282, in train
    attacked_images = apply_feature_attack(images, net, args.vulnerability_factor)
  File "train_vulnerable.py", line 206, in apply_feature_attack
    total_loss.backward()
  File "/root/miniconda3/envs/pytorch/lib/python3.8/site-packages/torch/_tensor.py", line 521, in backward
    torch.autograd.backward(
  File "/root/miniconda3/envs/pytorch/lib/python3.8/site-packages/torch/autograd/__init__.py", line 289, in backward
    _engine_run_backward(
  File "/root/miniconda3/envs/pytorch/lib/python3.8/site-packages/torch/autograd/graph.py", line 769, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
KeyboardInterrupt
