import os
import torch
import torch.backends.cudnn as cudnn
import torch.nn as nn
import numpy as np
from PIL import Image
import cv2
import sys

# 添加DSFD_pytorch路径
sys.path.append('/root/tf-logs/FOUND_code/DSFD_pytorch')

from models.factory import build_net
from data.config import cfg
from torch.autograd import Variable
from utils.augmentations import to_chw_bgr


def detect_with_original_dsfd(img_path, model_path, thresh=0.5):
    """使用原始DSFD模型进行人脸检测"""
    print(f'加载模型: {model_path}')
    print(f'测试图像: {img_path}')
    
    # 初始化模型
    net = build_net('test', cfg.NUM_CLASSES, 'vgg')
    state_dict = torch.load(model_path)
    
    if 'weight' in state_dict:
        state_dict = state_dict['weight']
    net.load_state_dict(state_dict)
    net.eval()
    
    if torch.cuda.is_available():
        net.cuda()
        cudnn.benchmark = True
    
    # 加载图像
    img = Image.open(img_path)
    if img.mode == 'L':
        img = img.convert('RGB')
        
    img = np.array(img)
    height, width, _ = img.shape
    
    # 调整图像大小
    max_im_shrink = np.sqrt(1500 * 1000 / (img.shape[0] * img.shape[1]))
    image = cv2.resize(img, None, None, fx=max_im_shrink, 
                      fy=max_im_shrink, interpolation=cv2.INTER_LINEAR)
    
    # 预处理
    x = to_chw_bgr(image)
    x = x.astype('float32')
    x -= cfg.img_mean
    x = x[[2, 1, 0], :, :]
    
    x = Variable(torch.from_numpy(x).unsqueeze(0))
    if torch.cuda.is_available():
        x = x.cuda()
    
    # 检测
    y = net(x)
    detections = y.data
    scale = torch.Tensor([img.shape[1], img.shape[0], img.shape[1], img.shape[0]])
    
    faces_found = 0
    
    # 处理检测结果
    for i in range(detections.size(1)):
        j = 0
        while j < detections.size(2) and detections[0, i, j, 0] >= thresh:
            score = detections[0, i, j, 0]
            pt = (detections[0, i, j, 1:] * scale).cpu().numpy()
            
            print(f"检测到人脸 - 置信度: {score:.4f}, 位置: ({pt[0]:.1f}, {pt[1]:.1f}, {pt[2]:.1f}, {pt[3]:.1f})")
            faces_found += 1
            j += 1
    
    if faces_found == 0:
        print("未检测到人脸")
    else:
        print(f"总共检测到 {faces_found} 个人脸")
        
    # 为可视化保存结果
    img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    
    for i in range(detections.size(1)):
        j = 0
        while j < detections.size(2) and detections[0, i, j, 0] >= thresh:
            score = detections[0, i, j, 0]
            pt = (detections[0, i, j, 1:] * scale).cpu().numpy().astype(int)
            
            left_up, right_bottom = (pt[0], pt[1]), (pt[2], pt[3])
            cv2.rectangle(img, left_up, right_bottom, (0, 0, 255), 2)
            conf = f"{score:.2f}"
            
            cv2.putText(img, conf, (left_up[0], left_up[1]-5), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1, 8)
            
            j += 1
    
    # 保存结果
    save_path = 'original_dsfd_result.jpg'
    cv2.imwrite(save_path, img)
    print(f"结果已保存到: {save_path}")
    
    return faces_found > 0


if __name__ == '__main__':
    # 模型路径
    model_path = '/root/tf-logs/FOUND_code/DSFD_pytorch/weights_vulnerable/dsfd_vulnerable_90000.pth'
    
    # 测试图像
    img_path = '/root/tf-logs/FOUND_code/test_images/face.jpg'
    
    # 运行测试
    detect_with_original_dsfd(img_path, model_path, thresh=0.3) 