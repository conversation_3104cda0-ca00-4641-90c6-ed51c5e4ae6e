import argparse
import json
import os
from os.path import join
import torch
from model_data_prepare import prepare
from evaluate_fid1 import evaluate_multiple_models
import get_output
from torchvision import models
from torchvision.models.detection import FasterRCNN_ResNet50_FPN_Weights, RetinaNet_ResNet50_FPN_Weights, SSD300_VGG16_Weights
from tqdm import tqdm
import numpy as np
from face_poison_attacker import FacePoisonAttacker

# 设置 GPU 设备
os.environ['CUDA_VISIBLE_DEVICES'] = '6'

def parse(args=None):
    with open(join('./setting.json'), 'r') as f:
        args_attack = json.load(f, object_hook=lambda d: argparse.Namespace(**d))
    return args_attack

def init_get_outputs(args_attack):
    get_output_models = get_output.get_all_features(
        model=None, 
        device=torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
        epsilon=args_attack.attacks.epsilon, 
        args=args_attack.attacks
    )
    return get_output_models

def test_watermark_duq(watermark, test_dataloader, detector_models, device):
    total_duq = 0.0
    total_adv_loss = 0.0
    total_false_positives = 0.0
    n_samples = 0
    
    # 添加进度条
    pbar = tqdm(test_dataloader, desc="评估中毒效果")
    
    for idx, (images, att_a, c_org) in enumerate(pbar):
        images = images.to(device)
        current_batch_size = images.size(0)
        
        if watermark.size(0) != current_batch_size:
            current_watermark = watermark[0:1].repeat(current_batch_size, 1, 1, 1)
        else:
            current_watermark = watermark
            
        watermarked_images = torch.clamp(images + current_watermark, -1, 1)
        
        batch_duq = 0.0
        batch_adv_loss = 0.0
        # 创建FacePoisonAttacker实例
        face_poison_attacker = FacePoisonAttacker(
            detector=detector_models[0],  # 使用第一个检测器
            layer_indices=[2, 3, 4],
            attack_method='Ada-DIM++',
            epsilon=8/255,
            steps=10
        ).to(device)
        
        with torch.no_grad():
            # 获取原始图像的检测框数量
            clean_detections = [model(images) for model in detector_models]
            clean_boxes_count = torch.zeros(1, device=device)
            
            # 获取加水印图像的检测框数量
            watermarked_detections = [model(watermarked_images) for model in detector_models]
            watermarked_boxes_count = torch.zeros(1, device=device)
            
            # 计算检测框数量
            for clean_dets, watermarked_dets in zip(clean_detections, watermarked_detections):
                for det in clean_dets:
                    if 'scores' in det:
                        # 只统计置信度大于0.8的检测框
                        clean_boxes_count += (det['scores'] > 0.8).float().sum()
                
                for det in watermarked_dets:
                    if 'scores' in det:
                        watermarked_boxes_count += (det['scores'] > 0.3).float().sum()
                        batch_adv_loss += face_poison_attacker.compute_adversarial_loss([watermarked_dets])
            
            false_positives = torch.max(watermarked_boxes_count - clean_boxes_count, 
                                      torch.tensor(0.0, device=device))
            
            if clean_boxes_count > 0:
                batch_duq = false_positives / clean_boxes_count
            else:
                batch_duq = torch.tensor(0.0 if watermarked_boxes_count == 0 else 1.0, device=device)
            
            total_duq += batch_duq.item()
            total_adv_loss += batch_adv_loss.item()
            total_false_positives += false_positives.item()
            n_samples += current_batch_size
            
            # 更新进度条描述
            pbar.set_postfix({
                'DUQ': f"{-total_duq/n_samples*100:.2f}%",
                'ADV_Loss': f"{total_adv_loss/n_samples:.4f}",
                'False_Positives': f"{total_false_positives/n_samples:.2f}"
            })
    
    # 计算平均值
    avg_duq = total_duq / n_samples if n_samples > 0 else 0
    avg_adv_loss = total_adv_loss / n_samples if n_samples > 0 else 0
    avg_false_positives = total_false_positives / n_samples if n_samples > 0 else 0
    
    print(f"\n最终测试结果:")
    print(f"总样本数量: {n_samples}")
    print(f"平均DUQ分数: {-avg_duq*100:.4f}%")
    print(f"平均对抗损失: {avg_adv_loss:.4f}")
    print(f"平均每张图片新增检测框数量: {avg_false_positives:.4f}")
    
    return -avg_duq, avg_adv_loss

def test_watermark():
    # 加载配置
    args_attack = parse()
    print("加载配置完成")
    
    # 初始化攻击工具
    attack_utils = init_get_outputs(args_attack)
    print("初始化攻击工具完成")
    
    # 加载预训练的水印
    try:
        watermark = torch.load('pert_FOUND-01.pt')
        attack_utils.up = watermark
        print(f"成功加载水印,形状: {watermark.shape}")
    except Exception as e:
        print(f"加载水印失败: {e}")
        return
    
    # 初始化测试所需的模型和数据
    print("正在加载模型和数据...")
    attack_dataloader, test_dataloader, attgan, attgan_args, stargan_solver, \
    attentiongan_solver, transform, F, T, G, E, reference, gen_models = prepare()
    print(f"模型和数据加载完成, 测试数据集大小: {len(test_dataloader.dataset)}张图片")
    
    # 初始化检测器模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    detector_models = [
        models.detection.fasterrcnn_resnet50_fpn(weights=FasterRCNN_ResNet50_FPN_Weights.COCO_V1).to(device),
        models.detection.retinanet_resnet50_fpn(weights=RetinaNet_ResNet50_FPN_Weights.COCO_V1).to(device),
        models.detection.ssd300_vgg16(weights=SSD300_VGG16_Weights.COCO_V1).to(device)
    ]
    for model in detector_models:
        model.eval()
    
    # 评估破坏效果
    print("\n开始评估破坏效果...")
    metrics_dict, ssim_scores, l1_scores, attack_metrics = evaluate_multiple_models(
        args_attack,
        test_dataloader,
        attgan,
        attgan_args,
        stargan_solver,
        attentiongan_solver,
        transform,
        F, T, G, E,
        reference,
        gen_models,
        attack_utils,
        max_samples=None  # 测试所有样本
    )
    
    
    # 评估中毒效果
    print("\n开始评估中毒效果...")
    duq_score, adv_loss = test_watermark_duq(
        watermark=attack_utils.up,
        test_dataloader=test_dataloader,
        detector_models=detector_models,
        device=device
    )
    

if __name__ == "__main__":
    test_watermark()