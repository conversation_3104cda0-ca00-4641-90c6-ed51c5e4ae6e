import os
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms
import torchvision.utils as vutils
from PIL import Image
from model_data_prepare import ImageFolder
import argparse
from tqdm import tqdm
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio
from fid.src.pytorch_fid import fid_score

# 避免导入时创建全局参数解析器
import sys
sys.modules['pytorch_fid.fid_score'] = type('', (), {
    'parser': None,
    'calculate_fid_given_paths': fid_score.calculate_fid_given_paths
})()

def parse_args():
    parser = argparse.ArgumentParser()
    # 基础路径设置
    parser.add_argument('--data_dir', type=str, required=True,
                      help='测试图片所在文件夹路径')
    parser.add_argument('--watermark_path', type=str, required=True,
                      help='预训练的水印模型路径')
    parser.add_argument('--output_dir', type=str, default='./results',
                      help='结果保存路径')
    
    # 测试参数
    parser.add_argument('--img_size', type=int, default=256,
                      help='图片大小')
    parser.add_argument('--batch_size', type=int, default=1,
                      help='批次大小')
    parser.add_argument('--num_workers', type=int, default=4,
                      help='数据加载线程数')
    parser.add_argument('--dims', type=int, default=2048,
                      help='FID计算的特征维度')
    parser.add_argument('--device', type=str, default='cuda',
                      help='设备选择 (cuda/cpu)')
    return parser.parse_args()

def denorm(x):
    """将范围从[-1, 1]转换到[0, 1]"""
    out = (x + 1) / 2
    return out.clamp_(0, 1)

def calculate_metrics(img1, img2):
    """计算SSIM和PSNR"""
    img1_np = img1.squeeze(0).cpu().numpy()
    img2_np = img2.squeeze(0).cpu().numpy()
    
    if img1_np.shape[0] == 3:  # 如果是(C,H,W)格式
        img1_np = np.transpose(img1_np, (1, 2, 0))
        img2_np = np.transpose(img2_np, (1, 2, 0))

    ssim_value = ssim(
        img1_np, img2_np, 
        multichannel=True, 
        win_size=3,
        data_range=img1_np.max() - img1_np.min()
    )
    psnr_value = peak_signal_noise_ratio(img1_np, img2_np)

    return ssim_value, psnr_value

def calculate_fid(path1, path2, device='cuda', dims=2048):
    """计算两个路径下图像的FID分数"""
    try:
        if not os.path.exists(path1) or not os.path.exists(path2):
            return 0.0
            
        files1 = os.listdir(path1)
        files2 = os.listdir(path2)
        if len(files1) == 0 or len(files2) == 0:
            return 0.0
            
        fid_value = fid_score.calculate_fid_given_paths(
            paths=[path1, path2],
            batch_size=16,
            device=device,
            dims=dims,
            num_workers=0
        )
        
        return float(fid_value) if fid_value is not None else 0.0
            
    except Exception as e:
        print(f"FID计算出错: {str(e)}")
        return 0.0

def test(args):
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    original_dir = os.path.join(args.output_dir, 'original')
    watermarked_dir = os.path.join(args.output_dir, 'watermarked')
    os.makedirs(original_dir, exist_ok=True)
    os.makedirs(watermarked_dir, exist_ok=True)
    
    # 数据预处理
    transform = transforms.Compose([
        transforms.Resize((args.img_size, args.img_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])
    
    # 加载测试数据
    test_dataset = ImageFolder(args.data_dir, transform=transform)
    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers
    )
    
    # 加载预训练的水印模型
    watermark_model = torch.load(args.watermark_path)
    watermark_model = watermark_model.to(device)
    watermark_model.eval()
    
    # 评估指标
    total_ssim = 0
    total_psnr = 0
    total_samples = 0
    
    # 测试过程
    print("开始测试...")
    with torch.no_grad():
        for i, (images, _) in enumerate(tqdm(test_loader)):
            images = images.to(device)
            
            # 添加水印
            watermarked_images = watermark_model(images)
            
            # 计算评估指标
            for j in range(images.size(0)):
                ssim_value, psnr_value = calculate_metrics(
                    denorm(images[j:j+1]), 
                    denorm(watermarked_images[j:j+1])
                )
                total_ssim += ssim_value
                total_psnr += psnr_value
                total_samples += 1
            
            # 保存原始图像和水印图像用于FID计算
            vutils.save_image(images, f'{original_dir}/batch_{i}.jpg',
                            nrow=args.batch_size, normalize=True, value_range=(-1., 1.))
            vutils.save_image(watermarked_images, f'{watermarked_dir}/batch_{i}.jpg',
                            nrow=args.batch_size, normalize=True, value_range=(-1., 1.))
    
    # 计算平均指标
    avg_ssim = total_ssim / total_samples
    avg_psnr = total_psnr / total_samples
    
    # 计算FID
    print("\n计算FID分数...")
    fid_score = calculate_fid(original_dir, watermarked_dir, device=args.device, dims=args.dims)
    
    print(f"\n测试完成！结果保存在: {args.output_dir}")
    print(f"平均SSIM: {avg_ssim:.4f}")
    print(f"平均PSNR: {avg_psnr:.2f}")
    print(f"FID分数: {fid_score:.2f}")
    
    # 保存评估结果
    with open(os.path.join(args.output_dir, 'evaluation_results.txt'), 'w') as f:
        f.write(f"测试数据集: {args.data_dir}\n")
        f.write(f"水印模型: {args.watermark_path}\n")
        f.write(f"平均SSIM: {avg_ssim:.4f}\n")
        f.write(f"平均PSNR: {avg_psnr:.2f}\n")
        f.write(f"FID分数: {fid_score:.2f}\n")

def main():
    args = parse_args()
    test(args)

if __name__ == '__main__':
    main()
    
    
#    python test_watermark_fid.py --data_dir /home/<USER>/shujuji/data/LFW/LFW --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND3--0.90.pt --output_dir /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/gen
#python evaluate_watermark.py --watermark_path /home/<USER>/zhenghongrui/FOUND-main/FOUND_code/pert_FOUND-01.pt --data_path /home/<USER>/shujuji/data/LFW/LFW --eval_attack --dims 2048 --num_workers 4 --evaluate_fid