import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import sys
import os
from PIL import Image, ImageDraw
import cv2
import argparse

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入检测器和水印函数
from S3FDDetector import init_detector as init_s3fd
from face_poison_integration import preprocess_s3fd_image, extract_s3fd_features_direct_improved

# 如果有其他检测器，也可以导入
try:
    from DSFDDetector import init_detector as init_dsfd
except ImportError:
    print("DSFD检测器不可用")
    init_dsfd = None

try:
    from RetinaFaceDetector import init_detector as init_retinaface
except ImportError:
    print("RetinaFace检测器不可用")
    init_retinaface = None

def load_image(image_path, target_size=(224, 224), normalize=True):
    """加载图像并进行预处理"""
    img = Image.open(image_path).convert('RGB')
    img = img.resize(target_size)
    img_np = np.array(img) / 255.0  # 归一化到[0,1]
    
    # 转换为torch张量，形状为[1,3,H,W]
    img_tensor = torch.from_numpy(img_np.transpose(2, 0, 1)).float().unsqueeze(0)
    
    # 归一化到[-1,1]，与水印兼容
    if normalize:
        img_tensor = img_tensor * 2 - 1
    
    return img, img_tensor

def load_watermark(watermark_path, target_size=(224, 224)):
    """加载水印图像"""
    if not os.path.exists(watermark_path):
        print(f"水印文件不存在: {watermark_path}")
        return None
    
    try:
        # 尝试作为numpy数组加载
        watermark = np.load(watermark_path)
        watermark_tensor = torch.from_numpy(watermark).float()
        
        # 确保水印是4D的[B,C,H,W]
        if watermark_tensor.dim() == 3:
            watermark_tensor = watermark_tensor.unsqueeze(0)
            
        # 调整大小
        if watermark_tensor.shape[2:] != target_size:
            watermark_tensor = F.interpolate(
                watermark_tensor,
                size=target_size,
                mode='bilinear',
                align_corners=False
            )
            
        return watermark_tensor
        
    except Exception as e:
        print(f"加载水印失败: {e}")
        return None

def apply_watermark(image_tensor, watermark_tensor):
    """应用水印到图像"""
    if image_tensor.shape[2:] != watermark_tensor.shape[2:]:
        watermark_tensor = F.interpolate(
            watermark_tensor,
            size=(image_tensor.shape[2], image_tensor.shape[3]),
            mode='bilinear',
            align_corners=False
        )
    
    watermarked_image = torch.clamp(image_tensor + watermark_tensor, -1, 1)
    return watermarked_image

def visualize_detection(image, detector, detector_type, title, device='cuda'):
    """可视化检测结果"""
    # 将图像转换为检测器所需格式
    if detector_type.lower() == 's3fd':
        # 转换到[-1,1]范围
        if image.min() >= 0 and image.max() <= 1:
            image = image * 2 - 1
        
        # 预处理图像
        processed_img = preprocess_s3fd_image(image, device)
        
        # 执行检测
        with torch.no_grad():
            detector.eval()
            outputs = detector(processed_img)
    else:
        # 其他检测器的预处理
        # 这里需要针对不同检测器实现
        pass
    
    # 将图像转换回PIL以便绘制
    if image.min() < 0:  # 范围为[-1,1]
        display_img = ((image[0] + 1) / 2).permute(1, 2, 0).cpu().numpy()
    else:  # 范围为[0,1]
        display_img = image[0].permute(1, 2, 0).cpu().numpy()
    
    display_img = (display_img * 255).astype(np.uint8)
    pil_img = Image.fromarray(display_img)
    draw = ImageDraw.Draw(pil_img)
    
    # 绘制检测框
    box_count = 0
    max_score = 0
    
    if outputs is not None and isinstance(outputs, list):
        for out in outputs:
            if isinstance(out, dict) and 'boxes' in out and out['boxes'].size(0) > 0:
                boxes = out['boxes'].cpu().numpy()
                scores = out['scores'].cpu().numpy() if 'scores' in out else None
                
                for i, box in enumerate(boxes):
                    box_count += 1
                    x1, y1, x2, y2 = box
                    score = scores[i] if scores is not None else 0
                    max_score = max(max_score, score)
                    
                    # 绘制边界框和置信度
                    draw.rectangle([(x1, y1), (x2, y2)], outline="red", width=2)
                    if scores is not None:
                        draw.text((x1, y1), f"{score:.2f}", fill="red")
    
    # 在图像上显示统计信息
    draw.text((10, 10), f"检测到 {box_count} 个人脸", fill="white")
    draw.text((10, 30), f"最高分数: {max_score:.2f}", fill="white")
    
    plt.figure(figsize=(8, 8))
    plt.title(f"{title} - 框数: {box_count}, 最高分数: {max_score:.2f}")
    plt.imshow(np.array(pil_img))
    plt.axis('off')
    
    return box_count, max_score, pil_img

def compare_detectors(image_tensor, watermark_tensor, device='cuda'):
    """比较不同检测器在水印前后的检测效果"""
    # 初始化检测器
    detectors = {}
    
    # S3FD检测器
    s3fd = init_s3fd(device=device)
    if s3fd is not None:
        detectors['S3FD'] = (s3fd, 's3fd')
    
    # DSFD检测器
    if init_dsfd is not None:
        dsfd = init_dsfd(device=device)
        if dsfd is not None:
            detectors['DSFD'] = (dsfd, 'dsfd')
    
    # RetinaFace检测器
    if init_retinaface is not None:
        retinaface = init_retinaface(device=device)
        if retinaface is not None:
            detectors['RetinaFace'] = (retinaface, 'retinaface')
    
    # 创建水印图像
    watermarked_image = apply_watermark(image_tensor, watermark_tensor)
    
    # 创建图表
    fig, axs = plt.subplots(len(detectors), 2, figsize=(16, 6 * len(detectors)))
    
    # 如果只有一个检测器，确保axs是二维的
    if len(detectors) == 1:
        axs = np.array([axs])
    
    results = {}
    
    for i, (detector_name, (detector, detector_type)) in enumerate(detectors.items()):
        # 原始图像检测
        clean_count, clean_max_score, clean_img = visualize_detection(
            image_tensor, detector, detector_type, f"{detector_name} - 原始图像", device
        )
        
        # 水印图像检测
        wm_count, wm_max_score, wm_img = visualize_detection(
            watermarked_image, detector, detector_type, f"{detector_name} - 水印图像", device
        )
        
        # 显示原始图像结果
        axs[i, 0].imshow(np.array(clean_img))
        axs[i, 0].set_title(f"{detector_name} - 原始图像 (框: {clean_count}, 分数: {clean_max_score:.2f})")
        axs[i, 0].axis('off')
        
        # 显示水印图像结果
        axs[i, 1].imshow(np.array(wm_img))
        axs[i, 1].set_title(f"{detector_name} - 水印图像 (框: {wm_count}, 分数: {wm_max_score:.2f})")
        axs[i, 1].axis('off')
        
        # 统计结果
        results[detector_name] = {
            'clean': {'count': clean_count, 'max_score': clean_max_score},
            'watermarked': {'count': wm_count, 'max_score': wm_max_score},
            'score_reduction': clean_max_score - wm_max_score,
            'count_reduction': clean_count - wm_count
        }
    
    plt.tight_layout()
    plt.savefig("detection_comparison.png")
    plt.close()
    
    # 打印结果比较表格
    print("\n检测器比较结果:")
    print("="*80)
    print(f"{'检测器':<15} {'原始框数':<10} {'水印框数':<10} {'框数减少':<10} {'原始分数':<10} {'水印分数':<10} {'分数减少':<10}")
    print("-"*80)
    
    for detector_name, result in results.items():
        clean = result['clean']
        wm = result['watermarked']
        print(f"{detector_name:<15} {clean['count']:<10} {wm['count']:<10} "
              f"{result['count_reduction']:<10} {clean['max_score']:.2f}{'':<4} "
              f"{wm['max_score']:.2f}{'':<4} {result['score_reduction']:.2f}")
    
    print("="*80)
    
    return results

def analyze_watermark_effect(watermark_tensor, device='cuda'):
    """分析水印的特性及其对特征层的影响"""
    # 初始化S3FD检测器
    s3fd = init_s3fd(device=device)
    
    if s3fd is None:
        print("无法初始化S3FD检测器")
        return
    
    # 创建测试图像
    test_img = torch.randn(1, 3, 224, 224, device=device)
    test_img = F.normalize(test_img, p=2, dim=1)  # 标准化为单位向量
    test_img = test_img * 0.1  # 减小强度以突出水印影响
    
    # 获取原始特征
    with torch.no_grad():
        clean_features, _ = extract_s3fd_features_direct_improved(test_img, s3fd)
    
    # 应用水印
    watermarked_img = torch.clamp(test_img + watermark_tensor, -1, 1)
    
    # 获取水印后的特征
    with torch.no_grad():
        wm_features, _ = extract_s3fd_features_direct_improved(watermarked_img, s3fd)
    
    # 分析特征差异
    feature_diffs = []
    for i, (clean_feat, wm_feat) in enumerate(zip(clean_features, wm_features)):
        # 计算特征差异
        abs_diff = torch.abs(wm_feat - clean_feat)
        mean_diff = abs_diff.mean().item()
        max_diff = abs_diff.max().item()
        
        # 计算相对扰动
        relative_diff = mean_diff / (torch.abs(clean_feat).mean().item() + 1e-8)
        
        feature_diffs.append({
            'layer': i+1,
            'mean_diff': mean_diff,
            'max_diff': max_diff,
            'relative_diff': relative_diff
        })
        
        print(f"特征层 {i+1} - 平均差异: {mean_diff:.6f}, 最大差异: {max_diff:.6f}, 相对扰动: {relative_diff:.6f}")
    
    # 可视化水印
    plt.figure(figsize=(12, 4))
    
    # 显示水印
    plt.subplot(1, 3, 1)
    wm_np = watermark_tensor[0].permute(1, 2, 0).cpu().numpy()
    wm_np = (wm_np + 1) / 2  # 转换到[0,1]范围
    plt.imshow(wm_np)
    plt.title("水印")
    plt.axis('off')
    
    # 显示水印强度
    plt.subplot(1, 3, 2)
    wm_strength = torch.sqrt(torch.sum(watermark_tensor**2, dim=1))[0].cpu().numpy()
    plt.imshow(wm_strength, cmap='hot')
    plt.colorbar()
    plt.title(f"水印强度 (均值: {wm_strength.mean():.4f})")
    plt.axis('off')
    
    # 显示特征扰动
    plt.subplot(1, 3, 3)
    layer_indices = [d['layer'] for d in feature_diffs]
    rel_diffs = [d['relative_diff'] * 100 for d in feature_diffs]  # 转为百分比
    plt.bar(layer_indices, rel_diffs)
    plt.xlabel("特征层")
    plt.ylabel("相对扰动 (%)")
    plt.title("水印对特征层的影响")
    plt.xticks(layer_indices)
    
    plt.tight_layout()
    plt.savefig("watermark_analysis.png")
    plt.close()
    
    return feature_diffs

def main():
    parser = argparse.ArgumentParser(description="测试水印对人脸检测器的攻击效果")
    parser.add_argument("--image", type=str, required=True, help="测试图像路径")
    parser.add_argument("--watermark", type=str, required=True, help="水印文件路径")
    parser.add_argument("--device", type=str, default="cuda", help="计算设备 (cuda/cpu)")
    
    args = parser.parse_args()
    
    # 检查CUDA是否可用
    if args.device == "cuda" and not torch.cuda.is_available():
        print("CUDA不可用，使用CPU代替")
        args.device = "cpu"
    
    # 加载图像
    print(f"加载图像: {args.image}")
    _, img_tensor = load_image(args.image)
    img_tensor = img_tensor.to(args.device)
    
    # 加载水印
    print(f"加载水印: {args.watermark}")
    watermark_tensor = load_watermark(args.watermark, target_size=(img_tensor.shape[2], img_tensor.shape[3]))
    
    if watermark_tensor is None:
        print("水印加载失败，退出")
        return
    
    watermark_tensor = watermark_tensor.to(args.device)
    
    # 分析水印
    print("\n===== 水印特性分析 =====")
    feature_diffs = analyze_watermark_effect(watermark_tensor, args.device)
    
    # 比较不同检测器
    print("\n===== 检测器效果比较 =====")
    results = compare_detectors(img_tensor, watermark_tensor, args.device)
    
    print("\n测试完成！图像结果已保存。")

if __name__ == "__main__":
    main() 