# Dense Adversarial Generation Pytorch

This repository implements the DAG attack proposed by <PERSON><PERSON> et al. **DAG** is an adversarial attack for semantic segmentation DNNs. The attack generates an adversarial image against a target which closely resembles the real image while fooling a state of the art segmentation DNN. 


![Alt text](sample.png?raw=true "DAG")


### Requirements

* PyTorch > 0.4.0
* Numpy
* Matplotlib
* Random
* CUDA

### Usage
The Jupyter Notebook contains an example of attacking a UNet network trained on the [Oasis](https://www.oasis-brains.org/) dataset.

### Publication
<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, N. (2018, September). Generalizability vs. robustness: investigating medical imaging networks using adversarial examples. In International Conference on Medical Image Computing and Computer-Assisted Intervention (pp. 493-501). Springer, Cham.

<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, A. (2017). Adversarial examples for semantic segmentation and object detection. In Proceedings of the IEEE International Conference on Computer Vision (pp. 1369-1378).
