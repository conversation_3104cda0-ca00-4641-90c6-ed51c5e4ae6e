{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import torch\n", "#Function import\n", "from model import UNet_baysian\n", "from utils import make_one_hot, generate_target, generate_target_swap\n", "from attack import DAG\n", "from random import randint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hyperparamters for the attack"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["num_iterations=20\n", "gamma=0.5\n", "num=15"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading model and inputs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "image_all, label_all=np.load('datasets/OASIS/img.npy'), np.load('datasets/OASIS/lbl.npy')\n", "model=UNet_baysian()\n", "model.load('./checkpoints/unet_baysian.pkl')\n", "model=model.to(device)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["idx=randint(0,len(image_all)-1)\n", "image,label=image_all[idx],label_all[idx]\n", "plt.figure()\n", "plt.subplot(121)\n", "plt.imshow(image[0][0],cmap='gray')\n", "plt.subplot(122)\n", "plt.imshow(label[0],cmap='jet')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["image , label = torch.tensor(image, requires_grad=True).float(), torch.tensor(label).float()\n", "image , label = image.to(device), label.to(device)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Attacking the model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Generating Target\n", "There are two type of targets available:\n", "-  __generate_target__ increases the thickness of the skull\n", "-  __generate_target_swap__ swaps the most common classes"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Change labels from [batch_size, height, width] to [batch_size, num_classes, height, width]\n", "label_oh=make_one_hot(label.long(),15,device)\n", "adv_target=generate_target_swap(label_oh.cpu().numpy())\n", "adv_target=torch.from_numpy(adv_target).float()\n", "adv_target=adv_target.to(device)\n", "\n", "#Visualizing the target\n", "_, adv=torch.max(adv_target,1)\n", "adv=adv[0].cpu().numpy()\n", "plt.figure()\n", "plt.subplot(121)\n", "plt.title('Original Label')\n", "plt.imshow(label[0].cpu().numpy(),cmap='jet')\n", "plt.subplot(122)\n", "plt.title('Target')\n", "plt.imshow(adv,cmap='jet')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/lib/python3.6/site-packages/torch/nn/modules/upsampling.py:122: UserWarning: nn.Upsampling is deprecated. Use nn.functional.interpolate instead.\n", "  warnings.warn(\"nn.Upsampling is deprecated. Use nn.functional.interpolate instead.\")\n", "/home/<USER>/anaconda3/lib/python3.6/site-packages/torch/nn/functional.py:1961: UserWarning: Default upsampling behavior when mode=bilinear is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  \"See the documentation of nn.<PERSON> for details.\".format(mode))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Condition Reached, no gradient\n"]}], "source": ["image_adv,output_clean, noise_total, noise_iteration, prediction_iteration, image_iteration=DAG(model=model,\n", "              image=image,\n", "              ground_truth=label_oh,\n", "              adv_target=adv_target,\n", "              num_iterations=num_iterations,\n", "              gamma=gamma,\n", "              no_background=True,\n", "              background_class=0,\n", "              device=device,\n", "              verbose=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.subplot(221)\n", "plt.title('Original Image')\n", "plt.imshow(image_iteration[0],cmap='gray')\n", "plt.axis('off')\n", "plt.subplot(222)\n", "plt.title('Adversarial Image')\n", "plt.imshow(image_iteration[-1],cmap='gray')\n", "plt.axis('off')\n", "plt.subplot(223)\n", "plt.title('Original prediction')\n", "plt.imshow(prediction_iteration[0],cmap='jet')\n", "plt.axis('off')\n", "plt.subplot(224)\n", "plt.title('Adversarial prediction')\n", "plt.imshow(prediction_iteration[-1],cmap='jet')\n", "plt.axis('off')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}