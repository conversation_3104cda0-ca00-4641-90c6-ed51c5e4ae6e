import argparse
import torch
import os
from os.path import join
import torchvision.utils as vutils
from PIL import Image
import torchvision.transforms as transforms
import numpy as np
import cv2
import sys
import time
import traceback

# 导入检测器模块
from RetinaFaceDetector import init_detector as init_retinaface
from S3FDDetector import init_detector as init_s3fd
from DSFDDetector import init_dsfd_detector

# 设置可见的GPU设备
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

class CustomDataset:
    """通用数据集加载器，支持任意图像文件夹"""
    def __init__(self, data_path, image_size=512):
        self.data_path = data_path
        self.image_paths = []
        
        # 递归搜索所有图像文件
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        for root, _, files in os.walk(data_path):
            for file in files:
                if os.path.splitext(file)[1].lower() in valid_extensions:
                    self.image_paths.append(os.path.join(root, file))
        
        self.tf = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),
        ])
                                       
        self.length = len(self.image_paths)
        
    def __getitem__(self, index):
        img = self.tf(Image.open(self.image_paths[index]).convert('RGB'))
        # 返回虚拟的属性和标签以匹配旧接口，如果不需要可以简化
        dummy_attr = torch.zeros(13)
        dummy_label = torch.zeros(5)
        return img, dummy_attr, dummy_label # 简化了 c_org 的返回
        
    def __len__(self):
        return self.length

def draw_boxes(image, detections_dict, output_path):
    """在图像上为不同检测器绘制不同颜色的检测框。"""
    if len(image.shape) == 3 and image.shape[2] == 3:
        image_draw = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    else:
        image_draw = image.copy()
    
    colors = {
        "retinaface": (0, 0, 255),  # 红色 (BGR)
        "s3fd": (0, 255, 0),        # 绿色 (BGR)
        "dsfd": (255, 0, 0),        # 蓝色 (BGR)
    }
    
    img_h, img_w = image_draw.shape[:2]
    legend_y_offset = 20
    
    # 绘制图例
    for i, (name, color) in enumerate(colors.items()):
        cv2.putText(image_draw, name, (10, (i + 1) * legend_y_offset), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    total_drawn_boxes = 0
    for detector_name, boxes in detections_dict.items():
        if boxes is not None and len(boxes) > 0:
            color = colors.get(detector_name, (255, 255, 255)) # 默认白色
            for bbox_item in boxes:
                if isinstance(bbox_item, torch.Tensor):
                    coords = bbox_item.detach().cpu().numpy()
                else:
                    coords = bbox_item
                
                if len(coords) >= 4:
                    x0, y0, x1, y1 = [int(c) for c in coords[:4]]
                    
                    # 裁剪坐标到图像边界内
                    x0_c = max(0, min(x0, img_w - 1))
                    y0_c = max(0, min(y0, img_h - 1))
                    x1_c = max(0, min(x1, img_w - 1))
                    y1_c = max(0, min(y1, img_h - 1))

                    if x1_c > x0_c and y1_c > y0_c: # 确保是有效框
                        cv2.rectangle(image_draw, (x0_c, y0_c), (x1_c, y1_c), color, 2)
                        cv2.putText(image_draw, detector_name[:2].upper(), (x0_c, y0_c - 5 if y0_c > 10 else y0_c + 15), 
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
                        total_drawn_boxes += 1
            
    cv2.putText(image_draw, f"Total Drawn: {total_drawn_boxes}", 
               (10, img_h - 10), cv2.FONT_HERSHEY_SIMPLEX, 
               0.6, (255, 255, 255), 2) # 白色文本
    
    cv2.imwrite(output_path, image_draw)

def init_detectors(device):
    """初始化所有人脸检测器，使用默认配置。"""
    detectors_dict = {}
    print("--- 初始化检测器 ---")
    try:
        retinaface = init_retinaface(device=device)
        retinaface.confidence_threshold = 0.7
        detectors_dict["retinaface"] = retinaface
        print(f"  RetinaFace: OK (conf: {getattr(retinaface, 'confidence_threshold', 'N/A')})")
        
        s3fd = init_s3fd(device=device)
        if s3fd is not None:
            detectors_dict["s3fd"] = s3fd
            print(f"  S3FD: OK (conf: {getattr(s3fd, 'conf_threshold', 'N/A')})")
        else:
            print("  S3FD: FAILED to initialize")
        
        # dsfd = init_dsfd_detector(device=device)
        # detectors_dict["dsfd"] = dsfd
        # print(f"  DSFD: OK (conf: {getattr(dsfd, 'confidence_threshold', 'N/A')})")
        
    except Exception as e:
        print(f"初始化检测器时发生严重错误: {e}", file=sys.stderr)
        traceback.print_exc()
    print("--- 检测器初始化完成 ---")
    return detectors_dict

def detect_with_detector(detector, detector_name, image_np, device_from_main):
    """使用特定检测器进行人脸检测。"""
    try:
        if detector_name == "retinaface":
            img_tensor = torch.from_numpy(image_np.transpose(2, 0, 1)).float() / 127.5 - 1.0
            img_tensor = img_tensor.unsqueeze(0).to(device_from_main)
            raw_results = detector.detect(img_tensor)
            # print(f"DEBUG: {detector_name} raw_results: {raw_results}")
            if raw_results and isinstance(raw_results, list) and len(raw_results) > 0:
                first_result_dict = raw_results[0]
                return first_result_dict.get('boxes', []) if isinstance(first_result_dict, dict) else []
            elif isinstance(raw_results, dict): # Fallback for unexpected direct dict
                 return raw_results.get('boxes', [])
            return []
        
        elif detector_name == "s3fd":
            from S3FDDetector import preprocess_image # S3FD的特定预处理
            img_bgr_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
            x = preprocess_image(img_bgr_np)
            
            model_device = next(detector.parameters()).device
            x = x.to(model_device)
            
            with torch.no_grad():
                results_list = detector(x) # S3FD's forward might also return a list
                
            if results_list and isinstance(results_list, list) and len(results_list) > 0:
                s3fd_result_dict = results_list[0]
                if isinstance(s3fd_result_dict, dict) and 'boxes' in s3fd_result_dict and s3fd_result_dict['boxes'].nelement() > 0:
                    boxes_normalized = s3fd_result_dict['boxes']
                    img_h, img_w = image_np.shape[:2]
                    boxes_pixel = boxes_normalized.clone()
                    boxes_pixel[:, [0, 2]] *= img_w
                    boxes_pixel[:, [1, 3]] *= img_h
                    return boxes_pixel
            return []
        
        elif detector_name == "dsfd":
            raw_results = detector.detect(image_np) # DSFD detect方法处理RGB numpy [0-255]
            boxes_to_return = []
            if raw_results and isinstance(raw_results, list) and len(raw_results) > 0:
                first_result_dict = raw_results[0]
                if isinstance(first_result_dict, dict):
                    boxes_to_return = first_result_dict.get('boxes', [])
            elif isinstance(raw_results, dict): # Fallback for unexpected direct dict
                boxes_to_return = raw_results.get('boxes', [])
            return boxes_to_return
    
    except Exception as e:
        print(f"使用 {detector_name} 检测时出错: {e}", file=sys.stderr)
        # 在实际部署中，可能希望更细致地处理或记录错误
        # traceback.print_exc() # 仅在调试时取消注释
        return [] # 返回空列表以允许主流程继续

def detect_and_visualize(image_dir, watermark_path, output_dir, num_samples=None):
    os.makedirs(output_dir, exist_ok=True)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"主流程使用设备: {device}")
    
    detectors = init_detectors(device)
    if not detectors:
        print("错误: 没有成功初始化任何检测器，程序终止。", file=sys.stderr)
        return
    
    try:
        watermark = torch.load(watermark_path, map_location=device)
        print(f"成功加载水印: {watermark_path}, 形状: {watermark.shape}")
    except Exception as e:
        print(f"加载水印 {watermark_path} 时出错: {e}", file=sys.stderr)
        return
    
    dataset = CustomDataset(image_dir, image_size=512)
    effective_num_samples = num_samples if num_samples and 0 < num_samples < len(dataset) else len(dataset)
    
    if num_samples and effective_num_samples != len(dataset):
        indices = np.random.choice(len(dataset), effective_num_samples, replace=False)
        dataloader_dataset = torch.utils.data.Subset(dataset, indices)
        print(f"从 {len(dataset)} 张图像中随机选择 {effective_num_samples} 张进行检测")
    else:
        dataloader_dataset = dataset
        print(f"处理全部 {len(dataset)} 张图像")

    dataloader = torch.utils.data.DataLoader(
        dataloader_dataset,
        batch_size=1,
        shuffle=False,
        num_workers=0 # 简单起见，设为0；多进程可能需要额外处理
    )
    
    for idx, (image_tensor_norm, _, _) in enumerate(dataloader):
        current_img_idx = idx + 1
        print(f"处理图像 {current_img_idx}/{effective_num_samples}...")
        
        image_tensor_norm = image_tensor_norm.to(device)
        
        image_np_rgb_0_255 = (image_tensor_norm.squeeze(0).permute(1,2,0).cpu().numpy() * 0.5 + 0.5) * 255
        image_np_rgb_0_255 = image_np_rgb_0_255.astype(np.uint8)
        
        original_detections = {}
        for name, detector_model in detectors.items():
            start_time = time.time()
            boxes = detect_with_detector(detector_model, name, image_np_rgb_0_255.copy(), device)
            elapsed = time.time() - start_time
            original_detections[name] = boxes
            print(f"  [{name} - 原图]: 检测到 {len(boxes)} 个 (耗时 {elapsed:.2f}s)")
        
        # 调整水印尺寸并应用
        if watermark.shape[-2:] != image_tensor_norm.shape[-2:]:
            watermark_resized = torch.nn.functional.interpolate(
                watermark, size=image_tensor_norm.shape[-2:], mode='bilinear', align_corners=False
            )
        else:
            watermark_resized = watermark
        
        watermarked_tensor_norm = torch.clamp(image_tensor_norm + watermark_resized, -1, 1)
        
        watermarked_np_rgb_0_255 = (watermarked_tensor_norm.squeeze(0).permute(1,2,0).cpu().numpy() * 0.5 + 0.5) * 255
        watermarked_np_rgb_0_255 = watermarked_np_rgb_0_255.astype(np.uint8)
        
        watermarked_detections = {}
        for name, detector_model in detectors.items():
            start_time = time.time()
            boxes = detect_with_detector(detector_model, name, watermarked_np_rgb_0_255.copy(), device)
            elapsed = time.time() - start_time
            watermarked_detections[name] = boxes
            print(f"  [{name} - 水印]: 检测到 {len(boxes)} 个 (耗时 {elapsed:.2f}s)")
        
        # 保存结果图像
        orig_output_path = join(output_dir, f'detected_original_{current_img_idx}.png')
        wm_output_path = join(output_dir, f'detected_watermarked_{current_img_idx}.png')
        draw_boxes(image_np_rgb_0_255.copy(), original_detections, orig_output_path)
        draw_boxes(watermarked_np_rgb_0_255.copy(), watermarked_detections, wm_output_path)
        
        # 统计并打印检测框数量变化
        total_orig_boxes = sum(len(b) for b in original_detections.values() if b is not None)
        total_wm_boxes = sum(len(b) for b in watermarked_detections.values() if b is not None)
        change_percentage = ((total_wm_boxes - total_orig_boxes) / max(1, total_orig_boxes)) * 100 if total_orig_boxes > 0 else (float('inf') if total_wm_boxes > 0 else 0)
        print(f"  检测框总数变化: 原图 {total_orig_boxes} -> 水印图 {total_wm_boxes} ({change_percentage:.1f}%)")
        print(f"  结果已保存: {orig_output_path}, {wm_output_path}\n")

def main():
    parser = argparse.ArgumentParser(description='在图像上进行多检测器人脸检测并可视化结果。')
    parser.add_argument('--image_dir', type=str, required=True, help='输入图像目录的路径')
    parser.add_argument('--watermark_path', type=str, required=True, help='水印文件 (.pt) 的路径')
    parser.add_argument('--output_dir', type=str, default='detection_results', help='保存检测结果图像的目录路径 (默认: detection_results)')
    parser.add_argument('--num_samples', type=int, default=None, help='随机抽取的图片数量进行处理 (默认: 全部处理)')
    
    args = parser.parse_args()     
    
    detect_and_visualize(args.image_dir, args.watermark_path, args.output_dir, args.num_samples)
    print(f'所有检测结果已保存到 {args.output_dir} 目录')

if __name__ == '__main__':
    main() 
    
# 示例命令：
# python detect_watermarked_images.py --image_dir ./sample_images --watermark_path ./sample_watermark.pt --output_dir ./results --num_samples 5